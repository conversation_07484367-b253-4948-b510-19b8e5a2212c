# 🔧 环境配置说明

## 📋 概述

本项目支持开发环境和生产环境的配置分离，通过不同的环境配置文件和脚本实现快速切换。

## 📁 配置文件结构

```
backend/
├── .env.development     # 开发环境配置
├── .env.production      # 生产环境配置
├── .env                 # 当前激活的环境配置
└── ecosystem.config.js  # PM2多环境配置
```

## 🔄 环境切换方法

### 方法1: 使用npm脚本（推荐）

```bash
# 切换到开发环境并启动
npm run dev:auto

# 切换到开发环境（仅切换配置）
npm run env:dev

# 切换到生产环境（仅切换配置）
npm run env:prod

# 启动开发环境
npm run start:dev

# 启动生产环境
npm run start:prod
```

### 方法2: 使用PM2

```bash
# PM2开发环境
npm run pm2:dev

# PM2生产环境
npm run pm2:prod

# 或者直接使用PM2命令
pm2 start ecosystem.config.js --env development
pm2 start ecosystem.config.js --env production
```

## 🛡️ CORS安全配置

### 开发环境
- **行为**: 允许所有域名访问 (`*`)
- **配置**: 不设置 `CORS_ALLOWED_ORIGINS`
- **适用**: 本地开发、测试

### 生产环境
- **行为**: 仅允许配置的域名访问
- **配置**: `CORS_ALLOWED_ORIGINS="https://myaitts.com,https://admin.myaitts.com"`
- **适用**: 线上部署

## 🔍 配置验证

### 测试CORS配置
```bash
# 启动服务器后运行测试
node test-cors-config.js
```

### 测试数据库连接池
```bash
# 测试数据库连接池配置
node test-db-pool.js
```

### 检查当前环境
```bash
# 访问健康检查端点
curl http://localhost:3000/health  # 生产环境
curl http://localhost:3001/health  # 开发环境
```

## 📊 环境差异对比

| 配置项 | 开发环境 | 生产环境 |
|--------|----------|----------|
| `NODE_ENV` | development | production |
| `PORT` | 3001 | 3000 |
| `DEBUG` | true | false |
| `API_BASE_URL` | localhost:3001 | myaitts.com |
| `CORS` | 允许所有 (*) | 限制域名 |
| `日志级别` | verbose | basic |
| `调试功能` | 启用 | 禁用 |
| `文件路径` | 相对路径 | 绝对路径 |
| `数据库连接池最大` | 20 | 50 |
| `数据库连接池最小` | 2 | 5 |
| `数据库SSL` | 禁用 | 启用 |
| `PM2内存限制` | 无 | 1G |
| `Node.js内存` | 默认 | 1024MB |

## 🚨 安全注意事项

### 生产环境必须修改的配置

1. **JWT密钥**
   ```bash
   # 当前使用示例密钥，必须更换！
   JWT_SECRET="PROD_CHANGE_THIS_TO_STRONG_SECRET_KEY"
   ```

2. **数据库密码**
   ```bash
   # 使用强密码
   DATABASE_URL="************************************************/db"
   ```

3. **CORS域名**
   ```bash
   # 只允许信任的域名
   CORS_ALLOWED_ORIGINS="https://your-real-domain.com"
   ```

4. **调试功能**
   ```bash
   # 生产环境关闭所有调试
   DEBUG=false
   ENABLE_DEBUG_PROGRESS=false
   ENABLE_PROXY_DEBUG=false
   ```

## 🛠️ 部署流程

### 开发环境部署
```bash
# 1. 切换到开发环境
npm run env:dev

# 2. 启动开发服务
npm run dev:auto
```

### 生产环境部署
```bash
# 1. 切换到生产环境
npm run env:prod

# 2. 检查配置
cat .env | grep NODE_ENV

# 3. 启动生产服务
npm run pm2:prod

# 4. 验证部署
node test-cors-config.js
```

## 🔧 故障排除

### 常见问题

1. **CORS错误**
   - 检查 `CORS_ALLOWED_ORIGINS` 配置
   - 确认前端域名是否在允许列表中
   - 运行 `node test-cors-config.js` 测试

2. **环境配置未生效**
   - 确认 `.env` 文件已更新
   - 重启服务器
   - 检查 `process.env.NODE_ENV`

3. **端口冲突**
   - 开发环境使用 3001 端口
   - 生产环境使用 3000 端口
   - 检查端口是否被占用

### 调试命令

```bash
# 查看当前环境变量
node -e "console.log(process.env.NODE_ENV)"

# 查看CORS配置
node -e "console.log(process.env.CORS_ALLOWED_ORIGINS)"

# 测试服务器连接
curl -I http://localhost:3001/health
```

## 📝 最佳实践

1. **开发时使用开发环境配置**
2. **部署前切换到生产环境配置**
3. **定期更新生产环境密钥**
4. **使用HTTPS部署生产环境**
5. **定期运行CORS测试验证安全性**

## 🔗 相关文件

- `src/middleware/cors.js` - CORS中间件实现
- `ecosystem.config.js` - PM2配置
- `test-cors-config.js` - CORS测试脚本
- `.env.example` - 配置模板
