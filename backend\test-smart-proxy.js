require('dotenv').config();
const { ProxySelector, callTtsProxyWithSmartRetry, generateSpeech } = require('./src/utils/ttsUtils');
const { getTTSProxyConfig } = require('./src/utils/config');

/**
 * 测试ProxySelector类的功能
 */
function testProxySelector() {
  console.log('🧪 测试ProxySelector类功能...\n');

  const testUrls = [
    'https://proxy1.example.com',
    'https://proxy2.example.com', 
    'https://proxy3.example.com',
    'https://proxy4.example.com'
  ];

  // 1. 测试初始化和随机排序
  console.log('📋 1. 测试初始化和随机排序');
  const selector = new ProxySelector(testUrls, true);
  console.log('原始顺序:', testUrls);
  console.log('随机排序:', selector.shuffledUrls);
  console.log('✅ 随机排序完成\n');

  // 2. 测试正常选择流程
  console.log('🎯 2. 测试正常选择流程');
  for (let i = 0; i < testUrls.length; i++) {
    const selected = selector.getNextProxy();
    console.log(`第${i + 1}次选择: ${selected}`);
  }
  
  // 尝试超出范围的选择
  const extraSelect = selector.getNextProxy();
  console.log(`超出范围选择: ${extraSelect}`);
  console.log('✅ 正常选择流程测试完成\n');

  // 3. 测试故障排除机制
  console.log('❌ 3. 测试故障排除机制');
  selector.reset(); // 重置状态
  
  // 标记前两个代理为失败
  const firstProxy = selector.getNextProxy();
  console.log(`选择代理: ${firstProxy}`);
  selector.markFailed(firstProxy);
  
  const secondProxy = selector.getNextProxy();
  console.log(`选择代理: ${secondProxy}`);
  selector.markFailed(secondProxy);
  
  // 继续选择，应该跳过失败的代理
  const thirdProxy = selector.getNextProxy();
  console.log(`选择代理: ${thirdProxy}`);
  
  console.log('当前状态:', selector.getStatus());
  console.log('✅ 故障排除机制测试完成\n');

  // 4. 测试可用性检查
  console.log('🔍 4. 测试可用性检查');
  console.log('是否还有可用代理:', selector.hasAvailableProxy());
  
  // 标记所有代理为失败
  selector.originalUrls.forEach(url => selector.markFailed(url));
  console.log('标记所有代理失败后，是否还有可用代理:', selector.hasAvailableProxy());
  console.log('✅ 可用性检查测试完成\n');
}

/**
 * 测试智能代理重试功能
 */
async function testSmartProxyRetry() {
  console.log('🚀 测试智能代理重试功能...\n');

  const proxyConfig = getTTSProxyConfig();
  console.log('当前代理配置:', {
    ENABLE_TTS_PROXY: proxyConfig.ENABLE_TTS_PROXY,
    TTS_PROXY_MODE: proxyConfig.TTS_PROXY_MODE,
    TTS_PROXY_URLS: proxyConfig.TTS_PROXY_URLS,
    ENABLE_PROXY_DEBUG: proxyConfig.ENABLE_PROXY_DEBUG
  });

  if (!proxyConfig.ENABLE_TTS_PROXY || proxyConfig.TTS_PROXY_MODE !== 'proxy') {
    console.log('❌ 请确保 ENABLE_TTS_PROXY=true 且 TTS_PROXY_MODE="proxy"');
    return;
  }

  if (!proxyConfig.TTS_PROXY_URLS || proxyConfig.TTS_PROXY_URLS.length === 0) {
    console.log('❌ 没有配置代理URL');
    return;
  }

  // 测试智能重试机制
  const testText = 'Hello world';
  const testVoiceId = 'pNInz6obpgDQGcFmaJgB'; // Adam voice
  const testModel = 'eleven_turbo_v2';

  try {
    console.log('🎵 开始测试智能代理重试...');
    console.log(`测试文本: "${testText}"`);
    console.log(`语音ID: ${testVoiceId}`);
    console.log(`模型: ${testModel}`);
    console.log();

    const startTime = Date.now();
    const audioBuffer = await callTtsProxyWithSmartRetry(
      testText,
      testVoiceId,
      testModel,
      0.5,  // stability
      0.75, // similarity_boost
      0.5,  // style
      1.0,  // speed
      proxyConfig
    );
    const endTime = Date.now();

    console.log(`✅ 智能代理重试成功!`);
    console.log(`音频大小: ${audioBuffer.byteLength} bytes`);
    console.log(`总耗时: ${endTime - startTime}ms`);

  } catch (error) {
    console.log(`❌ 智能代理重试失败: ${error.message}`);
    console.log('这可能是因为所有代理都不可用');
  }
}

/**
 * 测试完整的generateSpeech集成
 */
async function testGenerateSpeechIntegration() {
  console.log('\n🔗 测试generateSpeech集成...\n');

  const testText = 'Integration test';
  const testVoiceId = 'pNInz6obpgDQGcFmaJgB';
  const testModel = 'eleven_turbo_v2';

  try {
    console.log('🎵 测试完整的generateSpeech流程...');
    console.log(`测试文本: "${testText}"`);
    
    const startTime = Date.now();
    const audioBuffer = await generateSpeech(
      testText,
      testVoiceId,
      testModel,
      0.5,  // stability
      0.75, // similarity_boost
      0.5,  // style
      1.0   // speed
    );
    const endTime = Date.now();

    console.log(`✅ generateSpeech集成测试成功!`);
    console.log(`音频大小: ${audioBuffer.byteLength} bytes`);
    console.log(`总耗时: ${endTime - startTime}ms`);

  } catch (error) {
    console.log(`❌ generateSpeech集成测试失败: ${error.message}`);
  }
}

/**
 * 模拟故障场景测试
 */
async function testFailureScenarios() {
  console.log('\n💥 测试故障场景处理...\n');

  // 创建一个包含无效代理的配置
  const testConfig = {
    TTS_PROXY_URLS: [
      'https://invalid-proxy-1.example.com',
      'https://invalid-proxy-2.example.com',
      'https://tts-proxy-hk-1.aispeak.top', // 可能可用的代理
      'https://tts-proxy-hk-2.aispeak.top'  // 可能可用的代理
    ],
    TTS_PROXY_SECRET: process.env.TTS_PROXY_SECRET,
    TTS_PROXY_TIMEOUT: 10000, // 较短的超时时间
    ENABLE_PROXY_DEBUG: true
  };

  try {
    console.log('🧪 测试混合故障场景（部分代理不可用）...');
    
    const audioBuffer = await callTtsProxyWithSmartRetry(
      'Test failure handling',
      'pNInz6obpgDQGcFmaJgB',
      'eleven_turbo_v2',
      0.5, 0.75, 0.5, 1.0,
      testConfig
    );

    console.log(`✅ 故障场景测试成功! 音频大小: ${audioBuffer.byteLength} bytes`);
    console.log('智能重试机制成功跳过了故障代理');

  } catch (error) {
    console.log(`❌ 故障场景测试失败: ${error.message}`);
    console.log('这可能表示所有代理都不可用，或者网络问题');
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始智能代理重试功能测试...\n');
  
  try {
    // 1. 测试ProxySelector类
    testProxySelector();
    
    // 2. 测试智能代理重试
    await testSmartProxyRetry();
    
    // 3. 测试generateSpeech集成
    await testGenerateSpeechIntegration();
    
    // 4. 测试故障场景
    await testFailureScenarios();
    
    console.log('\n🎉 所有测试完成!');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runAllTests();
}

module.exports = {
  testProxySelector,
  testSmartProxyRetry,
  testGenerateSpeechIntegration,
  testFailureScenarios,
  runAllTests
};
