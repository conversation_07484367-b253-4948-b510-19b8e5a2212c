{"proxies": {"GLOBAL": {"all": ["worker-selector-1", "worker-selector-2", "worker-selector-3", "worker-selector-4", "worker-selector-5", "worker-selector-6", "worker-selector-7", "worker-selector-8", "worker-selector-9", "worker-selector-10", "jp-awsjp-01-idx-0", "jp-awsjp-02-idx-1", "jp-awsjp-03-idx-2", "jp-awsjp-04-idx-3", "jp-awsjp-05-idx-4", "sg-awssg-01-idx-5", "sg-awssg-02-idx-6", "sg-awssg-03-idx-7", "sg-awssg-04-idx-8", "sg-awssg-05-idx-9", "us-us-01-0-1-hy2-idx-10", "us-us-02-0-1-hy2-idx-11", "us-us-03-0-1-hy2-idx-12", "us-us-04-0-1-hy2-idx-13", "us-us-05-0-1-hy2-idx-14", "us-us-06-0-1-hy2-idx-15", "us-us-07-0-1-hy2-idx-16", "us-us-08-0-1-hy2-idx-17", "us-us-09-0-1-hy2-idx-18", "us-us-10-0-1-hy2-idx-19", "us-us-11-0-1-hy2-idx-20", "us-us-12-0-1-hy2-idx-21", "us-us-13-0-1-hy2-idx-22", "us-us-14-0-1-hy2-idx-23", "us-us-15-0-1-hy2-idx-24", "us-us-02-hy2-idx-25", "us-us-03-hy2-idx-26", "us-us-04-hy2-idx-27", "us-us-05-hy2-idx-28", "us-us-06-hy2-idx-29", "kr-kr-hy2-idx-30", "kr-kr-2-hy2-idx-31", "kr-kr-3-hy2-idx-32", "kr-kr-4-hy2-idx-33", "kr-kr-5-hy2-idx-34", "ae-ae-hy2-idx-35", "de-de-hy2-idx-36", "de-de-2-hy2-idx-37", "ch-ch-hy2-idx-38", "au-au-hy2-idx-39", "au-au-2-hy2-idx-40", "au-au-3-hy2-idx-41", "sg-sg-hy2-idx-42", "sg-sg-2-hy2-idx-43", "sg-sg-3-hy2-idx-44", "sg-sg-4-hy2-idx-45", "sg-sg-5-hy2-idx-46", "sg-sg-6-hy2-idx-47", "us-us-hy2-idx-48", "us-us-01-0-1-idx-49", "us-us-02-0-1-idx-50", "us-us-03-0-1-idx-51", "us-us-04-0-1-idx-52", "us-us-05-0-1-idx-53", "us-us-06-0-1-idx-54", "kr-kr-01-idx-55", "hk-hkthk-01-idx-56", "hk-hkthk-02-idx-57", "hk-hkthk-03-idx-58", "hk-hkthk-04-idx-59", "hk-hkthk-05-idx-60", "tw-tw-idx-61", "tw-tw-2-idx-62", "tw-tw-3-idx-63", "jp-jp-01-0-1-idx-64", "in-in-01-0-1-idx-65", "us-us-01-0-1-idx-66", "us-us-02-0-1-idx-67", "us-us-03-0-1-idx-68", "us-us-04-0-1-idx-69", "us-us-05-0-1-idx-70", "us-us-01-0-01-idx-71", "us-us-02-0-01-idx-72", "us-us-03-0-01-idx-73", "us-us-04-0-01-idx-74", "us-us-05-0-01-idx-75", "us-us-01-idx-76", "us-us-05-idx-77", "sg-sg-idx-78", "sg-sg-2-idx-79", "sg-sg-3-idx-80", "sg-sg-4-idx-81", "jp-jp-idx-82"], "history": [], "name": "GLOBAL", "now": "direct-dns", "type": "Fallback", "udp": true}, "direct-dns": {"type": "Direct", "name": "direct-dns", "udp": true, "history": []}, "worker-selector-1": {"type": "Selector", "name": "worker-selector-1", "udp": true, "history": [], "now": "us-us-10-0-1-hy2-idx-19", "all": ["jp-awsjp-01-idx-0", "jp-awsjp-02-idx-1", "jp-awsjp-03-idx-2", "jp-awsjp-04-idx-3", "jp-awsjp-05-idx-4", "sg-awssg-01-idx-5", "sg-awssg-02-idx-6", "sg-awssg-03-idx-7", "sg-awssg-04-idx-8", "sg-awssg-05-idx-9", "us-us-01-0-1-hy2-idx-10", "us-us-02-0-1-hy2-idx-11", "us-us-03-0-1-hy2-idx-12", "us-us-04-0-1-hy2-idx-13", "us-us-05-0-1-hy2-idx-14", "us-us-06-0-1-hy2-idx-15", "us-us-07-0-1-hy2-idx-16", "us-us-08-0-1-hy2-idx-17", "us-us-09-0-1-hy2-idx-18", "us-us-10-0-1-hy2-idx-19", "us-us-11-0-1-hy2-idx-20", "us-us-12-0-1-hy2-idx-21", "us-us-13-0-1-hy2-idx-22", "us-us-14-0-1-hy2-idx-23", "us-us-15-0-1-hy2-idx-24", "us-us-02-hy2-idx-25", "us-us-03-hy2-idx-26", "us-us-04-hy2-idx-27", "us-us-05-hy2-idx-28", "us-us-06-hy2-idx-29", "kr-kr-hy2-idx-30", "kr-kr-2-hy2-idx-31", "kr-kr-3-hy2-idx-32", "kr-kr-4-hy2-idx-33", "kr-kr-5-hy2-idx-34", "ae-ae-hy2-idx-35", "de-de-hy2-idx-36", "de-de-2-hy2-idx-37", "ch-ch-hy2-idx-38", "au-au-hy2-idx-39", "au-au-2-hy2-idx-40", "au-au-3-hy2-idx-41", "sg-sg-hy2-idx-42", "sg-sg-2-hy2-idx-43", "sg-sg-3-hy2-idx-44", "sg-sg-4-hy2-idx-45", "sg-sg-5-hy2-idx-46", "sg-sg-6-hy2-idx-47", "us-us-hy2-idx-48", "us-us-01-0-1-idx-49", "us-us-02-0-1-idx-50", "us-us-03-0-1-idx-51", "us-us-04-0-1-idx-52", "us-us-05-0-1-idx-53", "us-us-06-0-1-idx-54", "kr-kr-01-idx-55", "hk-hkthk-01-idx-56", "hk-hkthk-02-idx-57", "hk-hkthk-03-idx-58", "hk-hkthk-04-idx-59", "hk-hkthk-05-idx-60", "tw-tw-idx-61", "tw-tw-2-idx-62", "tw-tw-3-idx-63", "jp-jp-01-0-1-idx-64", "in-in-01-0-1-idx-65", "us-us-01-0-1-idx-66", "us-us-02-0-1-idx-67", "us-us-03-0-1-idx-68", "us-us-04-0-1-idx-69", "us-us-05-0-1-idx-70", "us-us-01-0-01-idx-71", "us-us-02-0-01-idx-72", "us-us-03-0-01-idx-73", "us-us-04-0-01-idx-74", "us-us-05-0-01-idx-75", "us-us-01-idx-76", "us-us-05-idx-77", "sg-sg-idx-78", "sg-sg-2-idx-79", "sg-sg-3-idx-80", "sg-sg-4-idx-81", "jp-jp-idx-82"]}, "worker-selector-2": {"type": "Selector", "name": "worker-selector-2", "udp": true, "history": [], "now": "sg-awssg-04-idx-8", "all": ["jp-awsjp-01-idx-0", "jp-awsjp-02-idx-1", "jp-awsjp-03-idx-2", "jp-awsjp-04-idx-3", "jp-awsjp-05-idx-4", "sg-awssg-01-idx-5", "sg-awssg-02-idx-6", "sg-awssg-03-idx-7", "sg-awssg-04-idx-8", "sg-awssg-05-idx-9", "us-us-01-0-1-hy2-idx-10", "us-us-02-0-1-hy2-idx-11", "us-us-03-0-1-hy2-idx-12", "us-us-04-0-1-hy2-idx-13", "us-us-05-0-1-hy2-idx-14", "us-us-06-0-1-hy2-idx-15", "us-us-07-0-1-hy2-idx-16", "us-us-08-0-1-hy2-idx-17", "us-us-09-0-1-hy2-idx-18", "us-us-10-0-1-hy2-idx-19", "us-us-11-0-1-hy2-idx-20", "us-us-12-0-1-hy2-idx-21", "us-us-13-0-1-hy2-idx-22", "us-us-14-0-1-hy2-idx-23", "us-us-15-0-1-hy2-idx-24", "us-us-02-hy2-idx-25", "us-us-03-hy2-idx-26", "us-us-04-hy2-idx-27", "us-us-05-hy2-idx-28", "us-us-06-hy2-idx-29", "kr-kr-hy2-idx-30", "kr-kr-2-hy2-idx-31", "kr-kr-3-hy2-idx-32", "kr-kr-4-hy2-idx-33", "kr-kr-5-hy2-idx-34", "ae-ae-hy2-idx-35", "de-de-hy2-idx-36", "de-de-2-hy2-idx-37", "ch-ch-hy2-idx-38", "au-au-hy2-idx-39", "au-au-2-hy2-idx-40", "au-au-3-hy2-idx-41", "sg-sg-hy2-idx-42", "sg-sg-2-hy2-idx-43", "sg-sg-3-hy2-idx-44", "sg-sg-4-hy2-idx-45", "sg-sg-5-hy2-idx-46", "sg-sg-6-hy2-idx-47", "us-us-hy2-idx-48", "us-us-01-0-1-idx-49", "us-us-02-0-1-idx-50", "us-us-03-0-1-idx-51", "us-us-04-0-1-idx-52", "us-us-05-0-1-idx-53", "us-us-06-0-1-idx-54", "kr-kr-01-idx-55", "hk-hkthk-01-idx-56", "hk-hkthk-02-idx-57", "hk-hkthk-03-idx-58", "hk-hkthk-04-idx-59", "hk-hkthk-05-idx-60", "tw-tw-idx-61", "tw-tw-2-idx-62", "tw-tw-3-idx-63", "jp-jp-01-0-1-idx-64", "in-in-01-0-1-idx-65", "us-us-01-0-1-idx-66", "us-us-02-0-1-idx-67", "us-us-03-0-1-idx-68", "us-us-04-0-1-idx-69", "us-us-05-0-1-idx-70", "us-us-01-0-01-idx-71", "us-us-02-0-01-idx-72", "us-us-03-0-01-idx-73", "us-us-04-0-01-idx-74", "us-us-05-0-01-idx-75", "us-us-01-idx-76", "us-us-05-idx-77", "sg-sg-idx-78", "sg-sg-2-idx-79", "sg-sg-3-idx-80", "sg-sg-4-idx-81", "jp-jp-idx-82"]}, "worker-selector-3": {"type": "Selector", "name": "worker-selector-3", "udp": true, "history": [], "now": "sg-awssg-05-idx-9", "all": ["jp-awsjp-01-idx-0", "jp-awsjp-02-idx-1", "jp-awsjp-03-idx-2", "jp-awsjp-04-idx-3", "jp-awsjp-05-idx-4", "sg-awssg-01-idx-5", "sg-awssg-02-idx-6", "sg-awssg-03-idx-7", "sg-awssg-04-idx-8", "sg-awssg-05-idx-9", "us-us-01-0-1-hy2-idx-10", "us-us-02-0-1-hy2-idx-11", "us-us-03-0-1-hy2-idx-12", "us-us-04-0-1-hy2-idx-13", "us-us-05-0-1-hy2-idx-14", "us-us-06-0-1-hy2-idx-15", "us-us-07-0-1-hy2-idx-16", "us-us-08-0-1-hy2-idx-17", "us-us-09-0-1-hy2-idx-18", "us-us-10-0-1-hy2-idx-19", "us-us-11-0-1-hy2-idx-20", "us-us-12-0-1-hy2-idx-21", "us-us-13-0-1-hy2-idx-22", "us-us-14-0-1-hy2-idx-23", "us-us-15-0-1-hy2-idx-24", "us-us-02-hy2-idx-25", "us-us-03-hy2-idx-26", "us-us-04-hy2-idx-27", "us-us-05-hy2-idx-28", "us-us-06-hy2-idx-29", "kr-kr-hy2-idx-30", "kr-kr-2-hy2-idx-31", "kr-kr-3-hy2-idx-32", "kr-kr-4-hy2-idx-33", "kr-kr-5-hy2-idx-34", "ae-ae-hy2-idx-35", "de-de-hy2-idx-36", "de-de-2-hy2-idx-37", "ch-ch-hy2-idx-38", "au-au-hy2-idx-39", "au-au-2-hy2-idx-40", "au-au-3-hy2-idx-41", "sg-sg-hy2-idx-42", "sg-sg-2-hy2-idx-43", "sg-sg-3-hy2-idx-44", "sg-sg-4-hy2-idx-45", "sg-sg-5-hy2-idx-46", "sg-sg-6-hy2-idx-47", "us-us-hy2-idx-48", "us-us-01-0-1-idx-49", "us-us-02-0-1-idx-50", "us-us-03-0-1-idx-51", "us-us-04-0-1-idx-52", "us-us-05-0-1-idx-53", "us-us-06-0-1-idx-54", "kr-kr-01-idx-55", "hk-hkthk-01-idx-56", "hk-hkthk-02-idx-57", "hk-hkthk-03-idx-58", "hk-hkthk-04-idx-59", "hk-hkthk-05-idx-60", "tw-tw-idx-61", "tw-tw-2-idx-62", "tw-tw-3-idx-63", "jp-jp-01-0-1-idx-64", "in-in-01-0-1-idx-65", "us-us-01-0-1-idx-66", "us-us-02-0-1-idx-67", "us-us-03-0-1-idx-68", "us-us-04-0-1-idx-69", "us-us-05-0-1-idx-70", "us-us-01-0-01-idx-71", "us-us-02-0-01-idx-72", "us-us-03-0-01-idx-73", "us-us-04-0-01-idx-74", "us-us-05-0-01-idx-75", "us-us-01-idx-76", "us-us-05-idx-77", "sg-sg-idx-78", "sg-sg-2-idx-79", "sg-sg-3-idx-80", "sg-sg-4-idx-81", "jp-jp-idx-82"]}, "worker-selector-4": {"type": "Selector", "name": "worker-selector-4", "udp": true, "history": [], "now": "us-us-01-0-1-hy2-idx-10", "all": ["jp-awsjp-01-idx-0", "jp-awsjp-02-idx-1", "jp-awsjp-03-idx-2", "jp-awsjp-04-idx-3", "jp-awsjp-05-idx-4", "sg-awssg-01-idx-5", "sg-awssg-02-idx-6", "sg-awssg-03-idx-7", "sg-awssg-04-idx-8", "sg-awssg-05-idx-9", "us-us-01-0-1-hy2-idx-10", "us-us-02-0-1-hy2-idx-11", "us-us-03-0-1-hy2-idx-12", "us-us-04-0-1-hy2-idx-13", "us-us-05-0-1-hy2-idx-14", "us-us-06-0-1-hy2-idx-15", "us-us-07-0-1-hy2-idx-16", "us-us-08-0-1-hy2-idx-17", "us-us-09-0-1-hy2-idx-18", "us-us-10-0-1-hy2-idx-19", "us-us-11-0-1-hy2-idx-20", "us-us-12-0-1-hy2-idx-21", "us-us-13-0-1-hy2-idx-22", "us-us-14-0-1-hy2-idx-23", "us-us-15-0-1-hy2-idx-24", "us-us-02-hy2-idx-25", "us-us-03-hy2-idx-26", "us-us-04-hy2-idx-27", "us-us-05-hy2-idx-28", "us-us-06-hy2-idx-29", "kr-kr-hy2-idx-30", "kr-kr-2-hy2-idx-31", "kr-kr-3-hy2-idx-32", "kr-kr-4-hy2-idx-33", "kr-kr-5-hy2-idx-34", "ae-ae-hy2-idx-35", "de-de-hy2-idx-36", "de-de-2-hy2-idx-37", "ch-ch-hy2-idx-38", "au-au-hy2-idx-39", "au-au-2-hy2-idx-40", "au-au-3-hy2-idx-41", "sg-sg-hy2-idx-42", "sg-sg-2-hy2-idx-43", "sg-sg-3-hy2-idx-44", "sg-sg-4-hy2-idx-45", "sg-sg-5-hy2-idx-46", "sg-sg-6-hy2-idx-47", "us-us-hy2-idx-48", "us-us-01-0-1-idx-49", "us-us-02-0-1-idx-50", "us-us-03-0-1-idx-51", "us-us-04-0-1-idx-52", "us-us-05-0-1-idx-53", "us-us-06-0-1-idx-54", "kr-kr-01-idx-55", "hk-hkthk-01-idx-56", "hk-hkthk-02-idx-57", "hk-hkthk-03-idx-58", "hk-hkthk-04-idx-59", "hk-hkthk-05-idx-60", "tw-tw-idx-61", "tw-tw-2-idx-62", "tw-tw-3-idx-63", "jp-jp-01-0-1-idx-64", "in-in-01-0-1-idx-65", "us-us-01-0-1-idx-66", "us-us-02-0-1-idx-67", "us-us-03-0-1-idx-68", "us-us-04-0-1-idx-69", "us-us-05-0-1-idx-70", "us-us-01-0-01-idx-71", "us-us-02-0-01-idx-72", "us-us-03-0-01-idx-73", "us-us-04-0-01-idx-74", "us-us-05-0-01-idx-75", "us-us-01-idx-76", "us-us-05-idx-77", "sg-sg-idx-78", "sg-sg-2-idx-79", "sg-sg-3-idx-80", "sg-sg-4-idx-81", "jp-jp-idx-82"]}, "worker-selector-5": {"type": "Selector", "name": "worker-selector-5", "udp": true, "history": [], "now": "us-us-02-0-1-hy2-idx-11", "all": ["jp-awsjp-01-idx-0", "jp-awsjp-02-idx-1", "jp-awsjp-03-idx-2", "jp-awsjp-04-idx-3", "jp-awsjp-05-idx-4", "sg-awssg-01-idx-5", "sg-awssg-02-idx-6", "sg-awssg-03-idx-7", "sg-awssg-04-idx-8", "sg-awssg-05-idx-9", "us-us-01-0-1-hy2-idx-10", "us-us-02-0-1-hy2-idx-11", "us-us-03-0-1-hy2-idx-12", "us-us-04-0-1-hy2-idx-13", "us-us-05-0-1-hy2-idx-14", "us-us-06-0-1-hy2-idx-15", "us-us-07-0-1-hy2-idx-16", "us-us-08-0-1-hy2-idx-17", "us-us-09-0-1-hy2-idx-18", "us-us-10-0-1-hy2-idx-19", "us-us-11-0-1-hy2-idx-20", "us-us-12-0-1-hy2-idx-21", "us-us-13-0-1-hy2-idx-22", "us-us-14-0-1-hy2-idx-23", "us-us-15-0-1-hy2-idx-24", "us-us-02-hy2-idx-25", "us-us-03-hy2-idx-26", "us-us-04-hy2-idx-27", "us-us-05-hy2-idx-28", "us-us-06-hy2-idx-29", "kr-kr-hy2-idx-30", "kr-kr-2-hy2-idx-31", "kr-kr-3-hy2-idx-32", "kr-kr-4-hy2-idx-33", "kr-kr-5-hy2-idx-34", "ae-ae-hy2-idx-35", "de-de-hy2-idx-36", "de-de-2-hy2-idx-37", "ch-ch-hy2-idx-38", "au-au-hy2-idx-39", "au-au-2-hy2-idx-40", "au-au-3-hy2-idx-41", "sg-sg-hy2-idx-42", "sg-sg-2-hy2-idx-43", "sg-sg-3-hy2-idx-44", "sg-sg-4-hy2-idx-45", "sg-sg-5-hy2-idx-46", "sg-sg-6-hy2-idx-47", "us-us-hy2-idx-48", "us-us-01-0-1-idx-49", "us-us-02-0-1-idx-50", "us-us-03-0-1-idx-51", "us-us-04-0-1-idx-52", "us-us-05-0-1-idx-53", "us-us-06-0-1-idx-54", "kr-kr-01-idx-55", "hk-hkthk-01-idx-56", "hk-hkthk-02-idx-57", "hk-hkthk-03-idx-58", "hk-hkthk-04-idx-59", "hk-hkthk-05-idx-60", "tw-tw-idx-61", "tw-tw-2-idx-62", "tw-tw-3-idx-63", "jp-jp-01-0-1-idx-64", "in-in-01-0-1-idx-65", "us-us-01-0-1-idx-66", "us-us-02-0-1-idx-67", "us-us-03-0-1-idx-68", "us-us-04-0-1-idx-69", "us-us-05-0-1-idx-70", "us-us-01-0-01-idx-71", "us-us-02-0-01-idx-72", "us-us-03-0-01-idx-73", "us-us-04-0-01-idx-74", "us-us-05-0-01-idx-75", "us-us-01-idx-76", "us-us-05-idx-77", "sg-sg-idx-78", "sg-sg-2-idx-79", "sg-sg-3-idx-80", "sg-sg-4-idx-81", "jp-jp-idx-82"]}, "worker-selector-6": {"type": "Selector", "name": "worker-selector-6", "udp": true, "history": [], "now": "jp-awsjp-01-idx-0", "all": ["jp-awsjp-01-idx-0", "jp-awsjp-02-idx-1", "jp-awsjp-03-idx-2", "jp-awsjp-04-idx-3", "jp-awsjp-05-idx-4", "sg-awssg-01-idx-5", "sg-awssg-02-idx-6", "sg-awssg-03-idx-7", "sg-awssg-04-idx-8", "sg-awssg-05-idx-9", "us-us-01-0-1-hy2-idx-10", "us-us-02-0-1-hy2-idx-11", "us-us-03-0-1-hy2-idx-12", "us-us-04-0-1-hy2-idx-13", "us-us-05-0-1-hy2-idx-14", "us-us-06-0-1-hy2-idx-15", "us-us-07-0-1-hy2-idx-16", "us-us-08-0-1-hy2-idx-17", "us-us-09-0-1-hy2-idx-18", "us-us-10-0-1-hy2-idx-19", "us-us-11-0-1-hy2-idx-20", "us-us-12-0-1-hy2-idx-21", "us-us-13-0-1-hy2-idx-22", "us-us-14-0-1-hy2-idx-23", "us-us-15-0-1-hy2-idx-24", "us-us-02-hy2-idx-25", "us-us-03-hy2-idx-26", "us-us-04-hy2-idx-27", "us-us-05-hy2-idx-28", "us-us-06-hy2-idx-29", "kr-kr-hy2-idx-30", "kr-kr-2-hy2-idx-31", "kr-kr-3-hy2-idx-32", "kr-kr-4-hy2-idx-33", "kr-kr-5-hy2-idx-34", "ae-ae-hy2-idx-35", "de-de-hy2-idx-36", "de-de-2-hy2-idx-37", "ch-ch-hy2-idx-38", "au-au-hy2-idx-39", "au-au-2-hy2-idx-40", "au-au-3-hy2-idx-41", "sg-sg-hy2-idx-42", "sg-sg-2-hy2-idx-43", "sg-sg-3-hy2-idx-44", "sg-sg-4-hy2-idx-45", "sg-sg-5-hy2-idx-46", "sg-sg-6-hy2-idx-47", "us-us-hy2-idx-48", "us-us-01-0-1-idx-49", "us-us-02-0-1-idx-50", "us-us-03-0-1-idx-51", "us-us-04-0-1-idx-52", "us-us-05-0-1-idx-53", "us-us-06-0-1-idx-54", "kr-kr-01-idx-55", "hk-hkthk-01-idx-56", "hk-hkthk-02-idx-57", "hk-hkthk-03-idx-58", "hk-hkthk-04-idx-59", "hk-hkthk-05-idx-60", "tw-tw-idx-61", "tw-tw-2-idx-62", "tw-tw-3-idx-63", "jp-jp-01-0-1-idx-64", "in-in-01-0-1-idx-65", "us-us-01-0-1-idx-66", "us-us-02-0-1-idx-67", "us-us-03-0-1-idx-68", "us-us-04-0-1-idx-69", "us-us-05-0-1-idx-70", "us-us-01-0-01-idx-71", "us-us-02-0-01-idx-72", "us-us-03-0-01-idx-73", "us-us-04-0-01-idx-74", "us-us-05-0-01-idx-75", "us-us-01-idx-76", "us-us-05-idx-77", "sg-sg-idx-78", "sg-sg-2-idx-79", "sg-sg-3-idx-80", "sg-sg-4-idx-81", "jp-jp-idx-82"]}, "worker-selector-7": {"type": "Selector", "name": "worker-selector-7", "udp": true, "history": [], "now": "jp-awsjp-01-idx-0", "all": ["jp-awsjp-01-idx-0", "jp-awsjp-02-idx-1", "jp-awsjp-03-idx-2", "jp-awsjp-04-idx-3", "jp-awsjp-05-idx-4", "sg-awssg-01-idx-5", "sg-awssg-02-idx-6", "sg-awssg-03-idx-7", "sg-awssg-04-idx-8", "sg-awssg-05-idx-9", "us-us-01-0-1-hy2-idx-10", "us-us-02-0-1-hy2-idx-11", "us-us-03-0-1-hy2-idx-12", "us-us-04-0-1-hy2-idx-13", "us-us-05-0-1-hy2-idx-14", "us-us-06-0-1-hy2-idx-15", "us-us-07-0-1-hy2-idx-16", "us-us-08-0-1-hy2-idx-17", "us-us-09-0-1-hy2-idx-18", "us-us-10-0-1-hy2-idx-19", "us-us-11-0-1-hy2-idx-20", "us-us-12-0-1-hy2-idx-21", "us-us-13-0-1-hy2-idx-22", "us-us-14-0-1-hy2-idx-23", "us-us-15-0-1-hy2-idx-24", "us-us-02-hy2-idx-25", "us-us-03-hy2-idx-26", "us-us-04-hy2-idx-27", "us-us-05-hy2-idx-28", "us-us-06-hy2-idx-29", "kr-kr-hy2-idx-30", "kr-kr-2-hy2-idx-31", "kr-kr-3-hy2-idx-32", "kr-kr-4-hy2-idx-33", "kr-kr-5-hy2-idx-34", "ae-ae-hy2-idx-35", "de-de-hy2-idx-36", "de-de-2-hy2-idx-37", "ch-ch-hy2-idx-38", "au-au-hy2-idx-39", "au-au-2-hy2-idx-40", "au-au-3-hy2-idx-41", "sg-sg-hy2-idx-42", "sg-sg-2-hy2-idx-43", "sg-sg-3-hy2-idx-44", "sg-sg-4-hy2-idx-45", "sg-sg-5-hy2-idx-46", "sg-sg-6-hy2-idx-47", "us-us-hy2-idx-48", "us-us-01-0-1-idx-49", "us-us-02-0-1-idx-50", "us-us-03-0-1-idx-51", "us-us-04-0-1-idx-52", "us-us-05-0-1-idx-53", "us-us-06-0-1-idx-54", "kr-kr-01-idx-55", "hk-hkthk-01-idx-56", "hk-hkthk-02-idx-57", "hk-hkthk-03-idx-58", "hk-hkthk-04-idx-59", "hk-hkthk-05-idx-60", "tw-tw-idx-61", "tw-tw-2-idx-62", "tw-tw-3-idx-63", "jp-jp-01-0-1-idx-64", "in-in-01-0-1-idx-65", "us-us-01-0-1-idx-66", "us-us-02-0-1-idx-67", "us-us-03-0-1-idx-68", "us-us-04-0-1-idx-69", "us-us-05-0-1-idx-70", "us-us-01-0-01-idx-71", "us-us-02-0-01-idx-72", "us-us-03-0-01-idx-73", "us-us-04-0-01-idx-74", "us-us-05-0-01-idx-75", "us-us-01-idx-76", "us-us-05-idx-77", "sg-sg-idx-78", "sg-sg-2-idx-79", "sg-sg-3-idx-80", "sg-sg-4-idx-81", "jp-jp-idx-82"]}, "worker-selector-8": {"type": "Selector", "name": "worker-selector-8", "udp": true, "history": [], "now": "jp-awsjp-01-idx-0", "all": ["jp-awsjp-01-idx-0", "jp-awsjp-02-idx-1", "jp-awsjp-03-idx-2", "jp-awsjp-04-idx-3", "jp-awsjp-05-idx-4", "sg-awssg-01-idx-5", "sg-awssg-02-idx-6", "sg-awssg-03-idx-7", "sg-awssg-04-idx-8", "sg-awssg-05-idx-9", "us-us-01-0-1-hy2-idx-10", "us-us-02-0-1-hy2-idx-11", "us-us-03-0-1-hy2-idx-12", "us-us-04-0-1-hy2-idx-13", "us-us-05-0-1-hy2-idx-14", "us-us-06-0-1-hy2-idx-15", "us-us-07-0-1-hy2-idx-16", "us-us-08-0-1-hy2-idx-17", "us-us-09-0-1-hy2-idx-18", "us-us-10-0-1-hy2-idx-19", "us-us-11-0-1-hy2-idx-20", "us-us-12-0-1-hy2-idx-21", "us-us-13-0-1-hy2-idx-22", "us-us-14-0-1-hy2-idx-23", "us-us-15-0-1-hy2-idx-24", "us-us-02-hy2-idx-25", "us-us-03-hy2-idx-26", "us-us-04-hy2-idx-27", "us-us-05-hy2-idx-28", "us-us-06-hy2-idx-29", "kr-kr-hy2-idx-30", "kr-kr-2-hy2-idx-31", "kr-kr-3-hy2-idx-32", "kr-kr-4-hy2-idx-33", "kr-kr-5-hy2-idx-34", "ae-ae-hy2-idx-35", "de-de-hy2-idx-36", "de-de-2-hy2-idx-37", "ch-ch-hy2-idx-38", "au-au-hy2-idx-39", "au-au-2-hy2-idx-40", "au-au-3-hy2-idx-41", "sg-sg-hy2-idx-42", "sg-sg-2-hy2-idx-43", "sg-sg-3-hy2-idx-44", "sg-sg-4-hy2-idx-45", "sg-sg-5-hy2-idx-46", "sg-sg-6-hy2-idx-47", "us-us-hy2-idx-48", "us-us-01-0-1-idx-49", "us-us-02-0-1-idx-50", "us-us-03-0-1-idx-51", "us-us-04-0-1-idx-52", "us-us-05-0-1-idx-53", "us-us-06-0-1-idx-54", "kr-kr-01-idx-55", "hk-hkthk-01-idx-56", "hk-hkthk-02-idx-57", "hk-hkthk-03-idx-58", "hk-hkthk-04-idx-59", "hk-hkthk-05-idx-60", "tw-tw-idx-61", "tw-tw-2-idx-62", "tw-tw-3-idx-63", "jp-jp-01-0-1-idx-64", "in-in-01-0-1-idx-65", "us-us-01-0-1-idx-66", "us-us-02-0-1-idx-67", "us-us-03-0-1-idx-68", "us-us-04-0-1-idx-69", "us-us-05-0-1-idx-70", "us-us-01-0-01-idx-71", "us-us-02-0-01-idx-72", "us-us-03-0-01-idx-73", "us-us-04-0-01-idx-74", "us-us-05-0-01-idx-75", "us-us-01-idx-76", "us-us-05-idx-77", "sg-sg-idx-78", "sg-sg-2-idx-79", "sg-sg-3-idx-80", "sg-sg-4-idx-81", "jp-jp-idx-82"]}, "worker-selector-9": {"type": "Selector", "name": "worker-selector-9", "udp": true, "history": [], "now": "jp-awsjp-01-idx-0", "all": ["jp-awsjp-01-idx-0", "jp-awsjp-02-idx-1", "jp-awsjp-03-idx-2", "jp-awsjp-04-idx-3", "jp-awsjp-05-idx-4", "sg-awssg-01-idx-5", "sg-awssg-02-idx-6", "sg-awssg-03-idx-7", "sg-awssg-04-idx-8", "sg-awssg-05-idx-9", "us-us-01-0-1-hy2-idx-10", "us-us-02-0-1-hy2-idx-11", "us-us-03-0-1-hy2-idx-12", "us-us-04-0-1-hy2-idx-13", "us-us-05-0-1-hy2-idx-14", "us-us-06-0-1-hy2-idx-15", "us-us-07-0-1-hy2-idx-16", "us-us-08-0-1-hy2-idx-17", "us-us-09-0-1-hy2-idx-18", "us-us-10-0-1-hy2-idx-19", "us-us-11-0-1-hy2-idx-20", "us-us-12-0-1-hy2-idx-21", "us-us-13-0-1-hy2-idx-22", "us-us-14-0-1-hy2-idx-23", "us-us-15-0-1-hy2-idx-24", "us-us-02-hy2-idx-25", "us-us-03-hy2-idx-26", "us-us-04-hy2-idx-27", "us-us-05-hy2-idx-28", "us-us-06-hy2-idx-29", "kr-kr-hy2-idx-30", "kr-kr-2-hy2-idx-31", "kr-kr-3-hy2-idx-32", "kr-kr-4-hy2-idx-33", "kr-kr-5-hy2-idx-34", "ae-ae-hy2-idx-35", "de-de-hy2-idx-36", "de-de-2-hy2-idx-37", "ch-ch-hy2-idx-38", "au-au-hy2-idx-39", "au-au-2-hy2-idx-40", "au-au-3-hy2-idx-41", "sg-sg-hy2-idx-42", "sg-sg-2-hy2-idx-43", "sg-sg-3-hy2-idx-44", "sg-sg-4-hy2-idx-45", "sg-sg-5-hy2-idx-46", "sg-sg-6-hy2-idx-47", "us-us-hy2-idx-48", "us-us-01-0-1-idx-49", "us-us-02-0-1-idx-50", "us-us-03-0-1-idx-51", "us-us-04-0-1-idx-52", "us-us-05-0-1-idx-53", "us-us-06-0-1-idx-54", "kr-kr-01-idx-55", "hk-hkthk-01-idx-56", "hk-hkthk-02-idx-57", "hk-hkthk-03-idx-58", "hk-hkthk-04-idx-59", "hk-hkthk-05-idx-60", "tw-tw-idx-61", "tw-tw-2-idx-62", "tw-tw-3-idx-63", "jp-jp-01-0-1-idx-64", "in-in-01-0-1-idx-65", "us-us-01-0-1-idx-66", "us-us-02-0-1-idx-67", "us-us-03-0-1-idx-68", "us-us-04-0-1-idx-69", "us-us-05-0-1-idx-70", "us-us-01-0-01-idx-71", "us-us-02-0-01-idx-72", "us-us-03-0-01-idx-73", "us-us-04-0-01-idx-74", "us-us-05-0-01-idx-75", "us-us-01-idx-76", "us-us-05-idx-77", "sg-sg-idx-78", "sg-sg-2-idx-79", "sg-sg-3-idx-80", "sg-sg-4-idx-81", "jp-jp-idx-82"]}, "worker-selector-10": {"type": "Selector", "name": "worker-selector-10", "udp": true, "history": [], "now": "jp-awsjp-01-idx-0", "all": ["jp-awsjp-01-idx-0", "jp-awsjp-02-idx-1", "jp-awsjp-03-idx-2", "jp-awsjp-04-idx-3", "jp-awsjp-05-idx-4", "sg-awssg-01-idx-5", "sg-awssg-02-idx-6", "sg-awssg-03-idx-7", "sg-awssg-04-idx-8", "sg-awssg-05-idx-9", "us-us-01-0-1-hy2-idx-10", "us-us-02-0-1-hy2-idx-11", "us-us-03-0-1-hy2-idx-12", "us-us-04-0-1-hy2-idx-13", "us-us-05-0-1-hy2-idx-14", "us-us-06-0-1-hy2-idx-15", "us-us-07-0-1-hy2-idx-16", "us-us-08-0-1-hy2-idx-17", "us-us-09-0-1-hy2-idx-18", "us-us-10-0-1-hy2-idx-19", "us-us-11-0-1-hy2-idx-20", "us-us-12-0-1-hy2-idx-21", "us-us-13-0-1-hy2-idx-22", "us-us-14-0-1-hy2-idx-23", "us-us-15-0-1-hy2-idx-24", "us-us-02-hy2-idx-25", "us-us-03-hy2-idx-26", "us-us-04-hy2-idx-27", "us-us-05-hy2-idx-28", "us-us-06-hy2-idx-29", "kr-kr-hy2-idx-30", "kr-kr-2-hy2-idx-31", "kr-kr-3-hy2-idx-32", "kr-kr-4-hy2-idx-33", "kr-kr-5-hy2-idx-34", "ae-ae-hy2-idx-35", "de-de-hy2-idx-36", "de-de-2-hy2-idx-37", "ch-ch-hy2-idx-38", "au-au-hy2-idx-39", "au-au-2-hy2-idx-40", "au-au-3-hy2-idx-41", "sg-sg-hy2-idx-42", "sg-sg-2-hy2-idx-43", "sg-sg-3-hy2-idx-44", "sg-sg-4-hy2-idx-45", "sg-sg-5-hy2-idx-46", "sg-sg-6-hy2-idx-47", "us-us-hy2-idx-48", "us-us-01-0-1-idx-49", "us-us-02-0-1-idx-50", "us-us-03-0-1-idx-51", "us-us-04-0-1-idx-52", "us-us-05-0-1-idx-53", "us-us-06-0-1-idx-54", "kr-kr-01-idx-55", "hk-hkthk-01-idx-56", "hk-hkthk-02-idx-57", "hk-hkthk-03-idx-58", "hk-hkthk-04-idx-59", "hk-hkthk-05-idx-60", "tw-tw-idx-61", "tw-tw-2-idx-62", "tw-tw-3-idx-63", "jp-jp-01-0-1-idx-64", "in-in-01-0-1-idx-65", "us-us-01-0-1-idx-66", "us-us-02-0-1-idx-67", "us-us-03-0-1-idx-68", "us-us-04-0-1-idx-69", "us-us-05-0-1-idx-70", "us-us-01-0-01-idx-71", "us-us-02-0-01-idx-72", "us-us-03-0-01-idx-73", "us-us-04-0-01-idx-74", "us-us-05-0-01-idx-75", "us-us-01-idx-76", "us-us-05-idx-77", "sg-sg-idx-78", "sg-sg-2-idx-79", "sg-sg-3-idx-80", "sg-sg-4-idx-81", "jp-jp-idx-82"]}, "jp-awsjp-01-idx-0": {"type": "VLESS", "name": "jp-awsjp-01-idx-0", "udp": true, "history": []}, "jp-awsjp-02-idx-1": {"type": "VLESS", "name": "jp-awsjp-02-idx-1", "udp": true, "history": []}, "jp-awsjp-03-idx-2": {"type": "VLESS", "name": "jp-awsjp-03-idx-2", "udp": true, "history": []}, "jp-awsjp-04-idx-3": {"type": "VLESS", "name": "jp-awsjp-04-idx-3", "udp": true, "history": []}, "jp-awsjp-05-idx-4": {"type": "VLESS", "name": "jp-awsjp-05-idx-4", "udp": true, "history": []}, "sg-awssg-01-idx-5": {"type": "VLESS", "name": "sg-awssg-01-idx-5", "udp": true, "history": []}, "sg-awssg-02-idx-6": {"type": "VLESS", "name": "sg-awssg-02-idx-6", "udp": true, "history": []}, "sg-awssg-03-idx-7": {"type": "VLESS", "name": "sg-awssg-03-idx-7", "udp": true, "history": []}, "sg-awssg-04-idx-8": {"type": "VLESS", "name": "sg-awssg-04-idx-8", "udp": true, "history": []}, "sg-awssg-05-idx-9": {"type": "VLESS", "name": "sg-awssg-05-idx-9", "udp": true, "history": []}, "us-us-01-0-1-hy2-idx-10": {"type": "Hysteria2", "name": "us-us-01-0-1-hy2-idx-10", "udp": true, "history": []}, "us-us-02-0-1-hy2-idx-11": {"type": "Hysteria2", "name": "us-us-02-0-1-hy2-idx-11", "udp": true, "history": []}, "us-us-03-0-1-hy2-idx-12": {"type": "Hysteria2", "name": "us-us-03-0-1-hy2-idx-12", "udp": true, "history": []}, "us-us-04-0-1-hy2-idx-13": {"type": "Hysteria2", "name": "us-us-04-0-1-hy2-idx-13", "udp": true, "history": []}, "us-us-05-0-1-hy2-idx-14": {"type": "Hysteria2", "name": "us-us-05-0-1-hy2-idx-14", "udp": true, "history": []}, "us-us-06-0-1-hy2-idx-15": {"type": "Hysteria2", "name": "us-us-06-0-1-hy2-idx-15", "udp": true, "history": []}, "us-us-07-0-1-hy2-idx-16": {"type": "Hysteria2", "name": "us-us-07-0-1-hy2-idx-16", "udp": true, "history": []}, "us-us-08-0-1-hy2-idx-17": {"type": "Hysteria2", "name": "us-us-08-0-1-hy2-idx-17", "udp": true, "history": []}, "us-us-09-0-1-hy2-idx-18": {"type": "Hysteria2", "name": "us-us-09-0-1-hy2-idx-18", "udp": true, "history": []}, "us-us-10-0-1-hy2-idx-19": {"type": "Hysteria2", "name": "us-us-10-0-1-hy2-idx-19", "udp": true, "history": []}, "us-us-11-0-1-hy2-idx-20": {"type": "Hysteria2", "name": "us-us-11-0-1-hy2-idx-20", "udp": true, "history": []}, "us-us-12-0-1-hy2-idx-21": {"type": "Hysteria2", "name": "us-us-12-0-1-hy2-idx-21", "udp": true, "history": []}, "us-us-13-0-1-hy2-idx-22": {"type": "Hysteria2", "name": "us-us-13-0-1-hy2-idx-22", "udp": true, "history": []}, "us-us-14-0-1-hy2-idx-23": {"type": "Hysteria2", "name": "us-us-14-0-1-hy2-idx-23", "udp": true, "history": []}, "us-us-15-0-1-hy2-idx-24": {"type": "Hysteria2", "name": "us-us-15-0-1-hy2-idx-24", "udp": true, "history": []}, "us-us-02-hy2-idx-25": {"type": "Hysteria2", "name": "us-us-02-hy2-idx-25", "udp": true, "history": []}, "us-us-03-hy2-idx-26": {"type": "Hysteria2", "name": "us-us-03-hy2-idx-26", "udp": true, "history": []}, "us-us-04-hy2-idx-27": {"type": "Hysteria2", "name": "us-us-04-hy2-idx-27", "udp": true, "history": []}, "us-us-05-hy2-idx-28": {"type": "Hysteria2", "name": "us-us-05-hy2-idx-28", "udp": true, "history": []}, "us-us-06-hy2-idx-29": {"type": "Hysteria2", "name": "us-us-06-hy2-idx-29", "udp": true, "history": []}, "kr-kr-hy2-idx-30": {"type": "Hysteria2", "name": "kr-kr-hy2-idx-30", "udp": true, "history": []}, "kr-kr-2-hy2-idx-31": {"type": "Hysteria2", "name": "kr-kr-2-hy2-idx-31", "udp": true, "history": []}, "kr-kr-3-hy2-idx-32": {"type": "Hysteria2", "name": "kr-kr-3-hy2-idx-32", "udp": true, "history": []}, "kr-kr-4-hy2-idx-33": {"type": "Hysteria2", "name": "kr-kr-4-hy2-idx-33", "udp": true, "history": []}, "kr-kr-5-hy2-idx-34": {"type": "Hysteria2", "name": "kr-kr-5-hy2-idx-34", "udp": true, "history": []}, "ae-ae-hy2-idx-35": {"type": "Hysteria2", "name": "ae-ae-hy2-idx-35", "udp": true, "history": []}, "de-de-hy2-idx-36": {"type": "Hysteria2", "name": "de-de-hy2-idx-36", "udp": true, "history": []}, "de-de-2-hy2-idx-37": {"type": "Hysteria2", "name": "de-de-2-hy2-idx-37", "udp": true, "history": []}, "ch-ch-hy2-idx-38": {"type": "Hysteria2", "name": "ch-ch-hy2-idx-38", "udp": true, "history": []}, "au-au-hy2-idx-39": {"type": "Hysteria2", "name": "au-au-hy2-idx-39", "udp": true, "history": []}, "au-au-2-hy2-idx-40": {"type": "Hysteria2", "name": "au-au-2-hy2-idx-40", "udp": true, "history": []}, "au-au-3-hy2-idx-41": {"type": "Hysteria2", "name": "au-au-3-hy2-idx-41", "udp": true, "history": []}, "sg-sg-hy2-idx-42": {"type": "Hysteria2", "name": "sg-sg-hy2-idx-42", "udp": true, "history": []}, "sg-sg-2-hy2-idx-43": {"type": "Hysteria2", "name": "sg-sg-2-hy2-idx-43", "udp": true, "history": []}, "sg-sg-3-hy2-idx-44": {"type": "Hysteria2", "name": "sg-sg-3-hy2-idx-44", "udp": true, "history": []}, "sg-sg-4-hy2-idx-45": {"type": "Hysteria2", "name": "sg-sg-4-hy2-idx-45", "udp": true, "history": []}, "sg-sg-5-hy2-idx-46": {"type": "Hysteria2", "name": "sg-sg-5-hy2-idx-46", "udp": true, "history": []}, "sg-sg-6-hy2-idx-47": {"type": "Hysteria2", "name": "sg-sg-6-hy2-idx-47", "udp": true, "history": []}, "us-us-hy2-idx-48": {"type": "Hysteria2", "name": "us-us-hy2-idx-48", "udp": true, "history": []}, "us-us-01-0-1-idx-49": {"type": "VLESS", "name": "us-us-01-0-1-idx-49", "udp": true, "history": []}, "us-us-02-0-1-idx-50": {"type": "VLESS", "name": "us-us-02-0-1-idx-50", "udp": true, "history": []}, "us-us-03-0-1-idx-51": {"type": "VLESS", "name": "us-us-03-0-1-idx-51", "udp": true, "history": []}, "us-us-04-0-1-idx-52": {"type": "VLESS", "name": "us-us-04-0-1-idx-52", "udp": true, "history": []}, "us-us-05-0-1-idx-53": {"type": "VLESS", "name": "us-us-05-0-1-idx-53", "udp": true, "history": []}, "us-us-06-0-1-idx-54": {"type": "VLESS", "name": "us-us-06-0-1-idx-54", "udp": true, "history": []}, "kr-kr-01-idx-55": {"type": "VLESS", "name": "kr-kr-01-idx-55", "udp": true, "history": []}, "hk-hkthk-01-idx-56": {"type": "VLESS", "name": "hk-hkthk-01-idx-56", "udp": true, "history": []}, "hk-hkthk-02-idx-57": {"type": "VLESS", "name": "hk-hkthk-02-idx-57", "udp": true, "history": []}, "hk-hkthk-03-idx-58": {"type": "VLESS", "name": "hk-hkthk-03-idx-58", "udp": true, "history": []}, "hk-hkthk-04-idx-59": {"type": "VLESS", "name": "hk-hkthk-04-idx-59", "udp": true, "history": []}, "hk-hkthk-05-idx-60": {"type": "VLESS", "name": "hk-hkthk-05-idx-60", "udp": true, "history": []}, "tw-tw-idx-61": {"type": "VLESS", "name": "tw-tw-idx-61", "udp": true, "history": []}, "tw-tw-2-idx-62": {"type": "VLESS", "name": "tw-tw-2-idx-62", "udp": true, "history": []}, "tw-tw-3-idx-63": {"type": "VLESS", "name": "tw-tw-3-idx-63", "udp": true, "history": []}, "jp-jp-01-0-1-idx-64": {"type": "VLESS", "name": "jp-jp-01-0-1-idx-64", "udp": true, "history": []}, "in-in-01-0-1-idx-65": {"type": "VLESS", "name": "in-in-01-0-1-idx-65", "udp": true, "history": []}, "us-us-01-0-1-idx-66": {"type": "VLESS", "name": "us-us-01-0-1-idx-66", "udp": true, "history": []}, "us-us-02-0-1-idx-67": {"type": "VLESS", "name": "us-us-02-0-1-idx-67", "udp": true, "history": []}, "us-us-03-0-1-idx-68": {"type": "VLESS", "name": "us-us-03-0-1-idx-68", "udp": true, "history": []}, "us-us-04-0-1-idx-69": {"type": "VLESS", "name": "us-us-04-0-1-idx-69", "udp": true, "history": []}, "us-us-05-0-1-idx-70": {"type": "VLESS", "name": "us-us-05-0-1-idx-70", "udp": true, "history": []}, "us-us-01-0-01-idx-71": {"type": "VLESS", "name": "us-us-01-0-01-idx-71", "udp": true, "history": []}, "us-us-02-0-01-idx-72": {"type": "VLESS", "name": "us-us-02-0-01-idx-72", "udp": true, "history": []}, "us-us-03-0-01-idx-73": {"type": "VLESS", "name": "us-us-03-0-01-idx-73", "udp": true, "history": []}, "us-us-04-0-01-idx-74": {"type": "VLESS", "name": "us-us-04-0-01-idx-74", "udp": true, "history": []}, "us-us-05-0-01-idx-75": {"type": "VLESS", "name": "us-us-05-0-01-idx-75", "udp": true, "history": []}, "us-us-01-idx-76": {"type": "VLESS", "name": "us-us-01-idx-76", "udp": true, "history": []}, "us-us-05-idx-77": {"type": "VLESS", "name": "us-us-05-idx-77", "udp": true, "history": []}, "sg-sg-idx-78": {"type": "VLESS", "name": "sg-sg-idx-78", "udp": true, "history": []}, "sg-sg-2-idx-79": {"type": "VLESS", "name": "sg-sg-2-idx-79", "udp": true, "history": []}, "sg-sg-3-idx-80": {"type": "VLESS", "name": "sg-sg-3-idx-80", "udp": true, "history": []}, "sg-sg-4-idx-81": {"type": "VLESS", "name": "sg-sg-4-idx-81", "udp": true, "history": []}, "jp-jp-idx-82": {"type": "VLESS", "name": "jp-jp-idx-82", "udp": true, "history": []}}}