# TTS应用服务器

基于Express.js的文本转语音(TTS)API服务器，从Cloudflare Worker迁移到Ubuntu服务器的完整解决方案。

## 🚀 功能特性

- **完整的TTS服务**: 支持ElevenLabs API的文本转语音功能
- **用户认证系统**: JWT令牌认证，支持用户注册、登录
- **VIP会员系统**: 支持多种套餐类型，配额管理
- **卡密充值系统**: 支持卡密生成和使用
- **实时WebSocket**: 支持实时任务进度推送
- **管理员面板**: 完整的后台管理功能
- **高可用部署**: PM2集群模式，支持负载均衡
- **完整日志系统**: 结构化日志记录和错误追踪

## 📋 系统要求

- **操作系统**: Ubuntu 22.04 LTS
- **Node.js**: v18.x LTS
- **PostgreSQL**: 14.x
- **Redis**: 7.x
- **Nginx**: 1.18+
- **PM2**: 5.x

## 🛠️ 安装部署

### 1. 克隆项目

```bash
git clone <repository-url>
cd tts-app-server
```

### 2. 安装依赖

```bash
npm install
```

### 3. 环境配置

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
nano .env
```

### 4. 数据库初始化

```bash
# 创建数据库表结构
npm run migrate:create

# 迁移数据（可选，如果从Cloudflare Worker迁移）
npm run migrate:data
```

### 5. 启动服务

```bash
# 开发模式
npm run dev

# 生产模式
npm start

# PM2集群模式
npm run pm2:start
```

## 📁 项目结构

```
tts-app-server/
├── src/
│   ├── api/                 # API路由
│   │   ├── auth.js         # 认证相关API
│   │   ├── tts.js          # TTS相关API
│   │   ├── user.js         # 用户相关API
│   │   └── admin.js        # 管理员API
│   ├── services/           # 核心服务
│   │   ├── dbClient.js     # PostgreSQL客户端
│   │   ├── redisClient.js  # Redis客户端
│   │   ├── websocketManager.js # WebSocket管理
│   │   ├── ttsProcessor.js # TTS处理服务
│   │   └── authService.js  # 认证服务
│   ├── utils/              # 工具函数
│   │   ├── logger.js       # 日志工具
│   │   ├── config.js       # 配置管理
│   │   ├── helpers.js      # 辅助函数
│   │   ├── validators.js   # 验证器
│   │   └── ttsUtils.js     # TTS工具函数
│   ├── middleware/         # 中间件
│   │   ├── auth.js         # 认证中间件
│   │   ├── cors.js         # CORS中间件
│   │   ├── errorHandler.js # 错误处理中间件
│   │   └── validation.js   # 验证中间件
│   └── app.js              # 应用主入口
├── config/                 # 配置文件
├── scripts/                # 脚本文件
│   ├── create_tables.sql   # 数据库表结构
│   ├── create_tables.js    # 表创建脚本
│   └── migrate_data.js     # 数据迁移脚本
├── tests/                  # 测试文件
├── logs/                   # 日志文件
├── .env.example            # 环境变量模板
├── ecosystem.config.js     # PM2配置
└── package.json            # 项目依赖
```

## 🔧 API接口

### 认证接口

- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/send-verification` - 发送邮箱验证码
- `POST /api/auth/refresh` - 刷新令牌
- `GET /api/auth/verify` - 验证令牌

### TTS接口

- `WS /api/tts/ws/generate` - WebSocket TTS生成
- `GET /api/tts/status/:taskId` - 获取任务状态
- `GET /api/tts/download/:taskId` - 下载音频文件
- `GET /api/tts/voices` - 获取语音列表
- `POST /api/tts/validate` - 验证TTS参数
- `GET /api/tts/history` - 获取TTS历史

### 用户接口

- `GET /api/user/quota` - 获取用户配额
- `GET /api/user/profile` - 获取用户信息
- `PUT /api/user/profile` - 更新用户信息
- `GET /api/user/stats` - 获取使用统计

### 卡密接口

- `POST /api/card/use` - 使用卡密激活VIP

### 管理员接口

#### 用户管理
- `GET /api/admin/users` - 获取用户列表
- `GET /api/admin/stats` - 获取系统统计
- `GET /api/admin/users/:username` - 获取用户详情
- `PUT /api/admin/users/:username/vip` - 更新用户VIP

#### 卡密管理
- `POST /api/admin/cards/generate` - 生成卡密
- `GET /api/admin/cards` - 获取卡密列表
- `GET /api/admin/cards/packages` - 获取可用套餐类型

## � API详细说明

### 卡密管理接口

#### 生成卡密
```http
POST /api/admin/cards/generate
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "packageType": "PT",        // 套餐类型: M, Q, H, PM, PQ, PH, PT
  "quantity": 1,              // 生成数量 (1-100)
  "customCode": "optional"    // 可选：自定义32位卡密
}
```

**响应示例**：
```json
{
  "success": true,
  "generated": 1,
  "requested": 1,
  "cards": [
    {
      "code": "5ltcGRXIDHpRsaFbKGUBScKNORTlxV9Q",
      "packageType": "PT",
      "packageInfo": {
        "type": "PT",
        "duration": 1800000,
        "quotaChars": 5000,
        "price": 0,
        "description": "测试套餐"
      }
    }
  ]
}
```

#### 获取卡密列表
```http
GET /api/admin/cards?page=1&limit=20&status=unused&packageType=PT
Authorization: Bearer <admin_token>
```

**查询参数**：
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20, 最大: 100)
- `status`: 卡密状态 (unused, used, expired)
- `packageType`: 套餐类型过滤

**响应示例**：
```json
{
  "cards": [
    {
      "id": 1,
      "code": "5ltcGRXIDHpRsaFbKGUBScKNORTlxV9Q",
      "package_type": "PT",
      "status": "unused",
      "package_info": {...},
      "created_at": "2025-01-22T02:29:15.872Z",
      "used_at": null,
      "used_by": null
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 24,
    "totalPages": 2,
    "hasNext": true,
    "hasPrev": false
  }
}
```

#### 获取套餐类型
```http
GET /api/admin/cards/packages
Authorization: Bearer <admin_token>
```

**响应示例**：
```json
{
  "packages": [
    {
      "type": "M",
      "description": "标准月套餐",
      "days": 30,
      "price": 25,
      "chars": 80000
    },
    {
      "type": "PT",
      "description": "测试套餐",
      "days": 0.0208,
      "price": 0,
      "chars": 5000
    }
  ]
}
```

### 套餐类型说明

| 类型 | 名称 | 时长 | 字符配额 | 价格 |
|------|------|------|----------|------|
| M | 标准月套餐 | 30天 | 80,000 | ¥25 |
| Q | 标准季度套餐 | 90天 | 250,000 | ¥55 |
| H | 标准半年套餐 | 180天 | 550,000 | ¥99 |
| PM | PRO月套餐 | 30天 | 250,000 | ¥45 |
| PQ | PRO季度套餐 | 90天 | 800,000 | ¥120 |
| PH | PRO半年套餐 | 180天 | 2,000,000 | ¥220 |
| PT | 测试套餐 | 30分钟 | 5,000 | ¥0 |

### 卡密格式要求

- **长度**: 32位字符
- **字符集**: 大小写字母 + 数字 (a-zA-Z0-9)
- **正则表达式**: `/^[a-zA-Z0-9]{32}$/`
- **示例**: `5ltcGRXIDHpRsaFbKGUBScKNORTlxV9Q`

### 卡密使用接口

#### 使用卡密激活VIP
```http
POST /api/card/use
Authorization: Bearer <user_token>
Content-Type: application/json

{
  "code": "5ltcGRXIDHpRsaFbKGUBScKNORTlxV9Q"
}
```

**响应示例**：
```json
{
  "quota": {
    "type": "PT",
    "expireAt": 1737598155872,
    "quotaChars": 5000,
    "usedChars": 0,
    "remainingChars": 5000
  }
}
```

**错误响应**：
```json
{
  "error": "卡密无效或已被使用"
}
```

## �🔐 环境变量配置

关键环境变量说明：

```bash
# 数据库配置
DATABASE_URL="postgresql://user:password@localhost:5432/database"
REDIS_URL="redis://localhost:6379"

# JWT认证
JWT_SECRET="your-secret-key"
ACCESS_TOKEN_EXPIRE=7200
REFRESH_TOKEN_EXPIRE=604800

# ElevenLabs API (现在使用免费接口，无需API Key)
# ELEVENLABS_API_KEY="your-elevenlabs-api-key"  # 已不再需要

# 文件存储
AUDIO_STORAGE_PATH="/var/data/tts-app/audios"

# 管理员用户
ADMIN_USERS="admin1,admin2"
```

## 🚀 PM2部署

```bash
# 启动集群
npm run pm2:start

# 查看状态
pm2 status

# 查看日志
npm run pm2:logs

# 重启服务
npm run pm2:restart

# 停止服务
npm run pm2:stop
```

## 📊 监控和日志

- **应用日志**: `./logs/` 目录
- **PM2日志**: 通过 `pm2 logs` 查看
- **健康检查**: `GET /health`
- **系统监控**: `pm2 monit`

## 🧪 测试

```bash
# 运行测试
npm test

# 监听模式
npm run test:watch

# 覆盖率报告
npm run test:coverage
```

## 🔧 开发

```bash
# 开发模式（自动重启）
npm run dev

# 代码检查
npm run lint

# 修复代码风格
npm run lint:fix

# 生成测试卡密（包含所有套餐类型）
node scripts/create-test-cards.js

# 创建数据库表
node scripts/create_tables.js
```

### 快速生成测试卡密

```bash
# 生成所有类型的测试卡密
npm run cards:create

# 或者手动运行脚本
node scripts/create-test-cards.js
```

**生成的卡密示例**：
- 测试套餐: `5ltcGRXIDHpRsaFbKGUBScKNORTlxV9Q` (30分钟, 5000字符)
- 标准月套餐: `Jda95BrNzXv6eQQKpNB7PJ9eoewXtyQq` (30天, 80000字符)
- PRO月套餐: `G0xz5XS05cSrYixgWkrdjOE96Dp1Bs6z` (30天, 250000字符)

## 📝 数据迁移

如果从Cloudflare Worker迁移，需要配置Cloudflare API：

```bash
# 设置Cloudflare API配置
export CF_ACCOUNT_ID="your-account-id"
export CF_API_TOKEN="your-api-token"

# 执行数据迁移
npm run migrate:data
```

## 🛡️ 安全注意事项

1. **JWT密钥**: 使用强随机密钥
2. **数据库密码**: 使用复杂密码
3. **API密钥**: 妥善保管ElevenLabs API密钥
4. **文件权限**: 确保音频存储目录权限正确
5. **防火墙**: 配置适当的防火墙规则

## 📞 技术支持

如有问题，请查看：

1. **日志文件**: `./logs/` 目录下的日志
2. **健康检查**: 访问 `/health` 端点
3. **PM2状态**: 运行 `pm2 status`

## 📄 许可证

MIT License
