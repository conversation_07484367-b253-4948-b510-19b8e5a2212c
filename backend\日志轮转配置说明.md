# 日志轮转配置说明

## 📋 概述

本文档说明了TTS应用后端的日志轮转和管理功能，包括自动文件大小控制、日志清理和PM2日志轮转配置。

## 🔧 功能特性

### 1. 应用日志轮转 (Logger类增强)

- ✅ **文件大小限制**: 单个日志文件超过限制时自动轮转
- ✅ **自动轮转**: `2025-07-26.log` → `2025-07-26.log.1` → `2025-07-26.log.2`
- ✅ **压缩支持**: 可选择压缩旧日志文件节省磁盘空间
- ✅ **自动清理**: 定期删除超过保留期的日志文件
- ✅ **向后兼容**: 完全兼容现有日志功能

### 2. PM2日志轮转

- ✅ **文件大小限制**: PM2进程日志文件大小控制
- ✅ **文件数量限制**: 保留指定数量的历史日志文件

## ⚙️ 环境变量配置

### 应用日志配置

```bash
# 日志目录
LOG_DIR="/var/log/tts-app"

# 单个日志文件最大大小 (支持 B, KB, MB, GB)
LOG_MAX_FILE_SIZE=50MB

# 保留的日志文件数量
LOG_MAX_FILES=30

# 是否启用日志压缩 (true/false)
LOG_ENABLE_COMPRESSION=true

# 日志清理检查间隔 (毫秒) - 24小时
LOG_CLEANUP_INTERVAL=86400000
```

### PM2日志配置 (ecosystem.config.js)

```javascript
// PM2日志轮转配置
max_log_size: '50M',        // 单个日志文件最大大小
retain_logs: 30,            // 保留的日志文件数量
```

## 📊 推荐配置

### 开发环境
```bash
LOG_MAX_FILE_SIZE=10MB      # 较小的文件便于调试
LOG_MAX_FILES=7             # 保留一周的日志
LOG_ENABLE_COMPRESSION=false # 不压缩便于实时查看
LOG_CLEANUP_INTERVAL=43200000 # 12小时清理一次
```

### 生产环境
```bash
LOG_MAX_FILE_SIZE=50MB      # 平衡性能和管理便利性
LOG_MAX_FILES=30            # 保留一个月的日志
LOG_ENABLE_COMPRESSION=true # 启用压缩节省空间
LOG_CLEANUP_INTERVAL=86400000 # 24小时清理一次
```

## 🚀 使用方法

### 1. 启动应用
```bash
# 开发环境
npm run dev

# 生产环境
npm run pm2:start
```

### 2. 测试日志轮转功能
```bash
# 运行测试脚本
node test-log-rotation.js
```

### 3. 手动触发清理
```javascript
const { logger } = require('./src/utils/logger');
logger.cleanupOldLogs();
```

## 📁 日志文件结构

```
logs/
├── 2025-07-26.log          # 当前日志文件
├── 2025-07-26.log.1        # 第一次轮转
├── 2025-07-26.log.1.gz     # 压缩的轮转文件
├── 2025-07-25.log.2.gz     # 更早的轮转文件
├── combined.log            # PM2合并日志
├── out.log                 # PM2标准输出
├── error.log               # PM2错误日志
└── auto-tag/               # 自动标签审计日志
    └── auto-tag-2025-07-23.log
```

## 🔍 监控和维护

### 1. 检查日志文件大小
```bash
# 查看日志目录使用情况
du -sh /var/log/tts-app/*

# 查找大文件
find /var/log/tts-app -name "*.log" -size +100M
```

### 2. 手动清理
```bash
# 清理超过30天的日志
find /var/log/tts-app -name "*.log*" -mtime +30 -delete

# 清理压缩文件
find /var/log/tts-app -name "*.gz" -mtime +30 -delete
```

### 3. 磁盘空间监控
```bash
# 检查磁盘使用率
df -h /var/log/tts-app

# 监控脚本示例
#!/bin/bash
USAGE=$(df /var/log/tts-app | tail -1 | awk '{print $5}' | sed 's/%//')
if [ "$USAGE" -gt 80 ]; then
    echo "WARNING: Log directory usage is ${USAGE}%"
fi
```

## ⚠️ 注意事项

### 1. 性能影响
- 文件轮转操作是同步的，可能短暂影响日志写入
- 压缩操作是异步的，不会阻塞应用运行
- 建议在低峰期进行大量日志清理

### 2. 磁盘空间
- 启用压缩可节省60-80%的磁盘空间
- 定期监控磁盘使用情况
- 根据业务需求调整保留天数

### 3. 兼容性
- 完全向后兼容现有日志功能
- 不影响Gateway健康检查日志
- 不影响自动标签审计日志

## 🛠️ 故障排除

### 1. 轮转失败
```bash
# 检查日志目录权限
ls -la /var/log/tts-app

# 检查磁盘空间
df -h /var/log/tts-app
```

### 2. 压缩失败
```bash
# 检查zlib模块
node -e "console.log(require('zlib'))"

# 手动压缩测试
gzip /var/log/tts-app/test.log
```

### 3. 清理失败
```bash
# 检查文件权限
ls -la /var/log/tts-app/*.log

# 手动清理测试
rm /var/log/tts-app/old-file.log
```

## 📈 性能优化建议

1. **合理设置文件大小**: 过小频繁轮转影响性能，过大不便管理
2. **适当的保留期**: 平衡存储成本和审计需求
3. **启用压缩**: 生产环境建议启用以节省空间
4. **定期监控**: 设置磁盘空间告警机制

## 🔄 升级说明

本次升级完全向后兼容，无需修改现有代码。如需回退，只需：

1. 恢复原始的 `logger.js` 文件
2. 移除环境变量中的日志轮转配置
3. 恢复 `ecosystem.config.js` 中的PM2配置
