"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[964],{1007:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2486:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},2712:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(2115),o=globalThis?.document?r.useLayoutEffect:()=>{}},2919:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},3655:(e,t,n)=>{n.d(t,{hO:()=>l,sG:()=>u});var r=n(2115),o=n(7650),i=n(9708),a=n(5155),u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=r.forwardRef((e,n)=>{let{asChild:r,...o}=e,u=r?i.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(u,{...o,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function l(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},5185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},5339:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5525:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},5845:(e,t,n)=>{n.d(t,{i:()=>i});var r=n(2115),o=n(9033);function i({prop:e,defaultProp:t,onChange:n=()=>{}}){let[i,a]=function({defaultProp:e,onChange:t}){let n=r.useState(e),[i]=n,a=r.useRef(i),u=(0,o.c)(t);return r.useEffect(()=>{a.current!==i&&(u(i),a.current=i)},[i,a,u]),n}({defaultProp:t,onChange:n}),u=void 0!==e,l=u?e:i,s=(0,o.c)(n);return[l,r.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&s(n)}else a(t)},[u,e,a,s])]}},6081:(e,t,n)=>{n.d(t,{A:()=>a,q:()=>i});var r=n(2115),o=n(5155);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,a=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:a,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function a(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let a=r.createContext(i),u=n.length;n=[...n,i];let l=t=>{let{scope:n,children:i,...l}=t,s=n?.[e]?.[u]||a,c=r.useMemo(()=>l,Object.values(l));return(0,o.jsx)(s.Provider,{value:c,children:i})};return l.displayName=t+"Provider",[l,function(n,o){let l=o?.[e]?.[u]||a,s=r.useContext(l);if(s)return s;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},6981:(e,t,n)=>{n.d(t,{C1:()=>A,bL:()=>E});var r=n(2115),o=n(6101),i=n(6081),a=n(5185),u=n(5845),l=n(5503),s=n(1275),c=n(8905),d=n(3655),f=n(5155),m="Checkbox",[p,v]=(0,i.A)(m),[y,h]=p(m),N=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:i,checked:l,defaultChecked:s,required:c,disabled:m,value:p="on",onCheckedChange:v,form:h,...N}=e,[k,w]=r.useState(null),E=(0,o.s)(t,e=>w(e)),A=r.useRef(!1),C=!k||h||!!k.closest("form"),[M=!1,O]=(0,u.i)({prop:l,defaultProp:s,onChange:v}),R=r.useRef(M);return r.useEffect(()=>{let e=null==k?void 0:k.form;if(e){let t=()=>O(R.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[k,O]),(0,f.jsxs)(y,{scope:n,state:M,disabled:m,children:[(0,f.jsx)(d.sG.button,{type:"button",role:"checkbox","aria-checked":b(M)?"mixed":M,"aria-required":c,"data-state":g(M),"data-disabled":m?"":void 0,disabled:m,value:p,...N,ref:E,onKeyDown:(0,a.m)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,a.m)(e.onClick,e=>{O(e=>!!b(e)||!e),C&&(A.current=e.isPropagationStopped(),A.current||e.stopPropagation())})}),C&&(0,f.jsx)(x,{control:k,bubbles:!A.current,name:i,value:p,checked:M,required:c,disabled:m,form:h,style:{transform:"translateX(-100%)"},defaultChecked:!b(s)&&s})]})});N.displayName=m;var k="CheckboxIndicator",w=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:r,...o}=e,i=h(k,n);return(0,f.jsx)(c.C,{present:r||b(i.state)||!0===i.state,children:(0,f.jsx)(d.sG.span,{"data-state":g(i.state),"data-disabled":i.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});w.displayName=k;var x=e=>{let{control:t,checked:n,bubbles:o=!0,defaultChecked:i,...a}=e,u=r.useRef(null),c=(0,l.Z)(n),d=(0,s.X)(t);r.useEffect(()=>{let e=u.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(c!==n&&t){let r=new Event("click",{bubbles:o});e.indeterminate=b(n),t.call(e,!b(n)&&n),e.dispatchEvent(r)}},[c,n,o]);let m=r.useRef(!b(n)&&n);return(0,f.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:null!=i?i:m.current,...a,tabIndex:-1,ref:u,style:{...e.style,...d,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function b(e){return"indeterminate"===e}function g(e){return b(e)?"indeterminate":e?"checked":"unchecked"}var E=N,A=w},8883:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},8905:(e,t,n)=>{n.d(t,{C:()=>a});var r=n(2115),o=n(6101),i=n(2712),a=e=>{let{present:t,children:n}=e,a=function(e){var t,n;let[o,a]=r.useState(),l=r.useRef({}),s=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=u(l.current);c.current="mounted"===d?e:"none"},[d]),(0,i.N)(()=>{let t=l.current,n=s.current;if(n!==e){let r=c.current,o=u(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,i.N)(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=u(l.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(c.current=u(l.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{e&&(l.current=getComputedStyle(e)),a(e)},[])}}(t),l="function"==typeof n?n({present:a.isPresent}):r.Children.only(n),s=(0,o.s)(a.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof n||a.isPresent?r.cloneElement(l,{ref:s}):null};function u(e){return(null==e?void 0:e.animationName)||"none"}a.displayName="Presence"},9033:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(2115);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},9946:(e,t,n)=>{n.d(t,{A:()=>l});var r=n(2115);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:o=24,strokeWidth:u=2,absoluteStrokeWidth:l,className:s="",children:c,iconNode:d,...f}=e;return(0,r.createElement)("svg",{ref:t,...a,width:o,height:o,stroke:n,strokeWidth:l?24*Number(u)/Number(o):u,className:i("lucide",s),...f},[...d.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(c)?c:[c]])}),l=(e,t)=>{let n=(0,r.forwardRef)((n,a)=>{let{className:l,...s}=n;return(0,r.createElement)(u,{ref:a,iconNode:t,className:i("lucide-".concat(o(e)),l),...s})});return n.displayName="".concat(e),n}}}]);