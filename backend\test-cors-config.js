#!/usr/bin/env node

/**
 * CORS配置测试脚本
 * 用于验证开发环境和生产环境的CORS配置是否正常工作
 */

const axios = require('axios');

// 测试配置
const TEST_CONFIG = {
  // 测试服务器地址
  serverUrl: 'http://localhost:3000',
  
  // 测试的域名列表
  testOrigins: [
    'https://myaitts.com',           // 生产环境允许的域名
    'https://admin.myaitts.com',     // 管理后台域名
    'https://evil-site.com',         // 恶意域名（应该被拒绝）
    'http://localhost:3000',         // 本地前端
    'http://localhost:4000',         // 另一个本地端口
    null                             // 无Origin头（如Postman）
  ]
};

// 颜色输出函数
function colorLog(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 测试CORS配置
async function testCORS(origin) {
  try {
    const headers = {};
    if (origin) {
      headers['Origin'] = origin;
    }

    const response = await axios.get(`${TEST_CONFIG.serverUrl}/health`, {
      headers,
      timeout: 5000
    });

    const corsHeader = response.headers['access-control-allow-origin'];
    
    return {
      success: true,
      status: response.status,
      corsHeader,
      origin: origin || 'none'
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      origin: origin || 'none'
    };
  }
}

// 主测试函数
async function runCORSTests() {
  colorLog('blue', '🔍 开始CORS配置测试...\n');

  // 检查服务器是否运行
  try {
    await axios.get(`${TEST_CONFIG.serverUrl}/health`, { timeout: 3000 });
    colorLog('green', '✅ 服务器连接正常');
  } catch (error) {
    colorLog('red', '❌ 无法连接到服务器，请确保服务器已启动');
    colorLog('yellow', `   服务器地址: ${TEST_CONFIG.serverUrl}`);
    process.exit(1);
  }

  console.log('\n📋 测试结果:\n');

  // 测试每个域名
  for (const origin of TEST_CONFIG.testOrigins) {
    const result = await testCORS(origin);
    
    if (result.success) {
      const originDisplay = origin || '无Origin';
      const corsValue = result.corsHeader || '未设置';
      
      if (origin && result.corsHeader === origin) {
        colorLog('green', `✅ ${originDisplay} -> 允许访问 (${corsValue})`);
      } else if (!origin && result.corsHeader) {
        colorLog('green', `✅ ${originDisplay} -> 允许访问 (${corsValue})`);
      } else if (result.corsHeader === '*') {
        colorLog('yellow', `⚠️  ${originDisplay} -> 允许访问 (开发模式: *)`);
      } else if (result.corsHeader === 'null') {
        colorLog('red', `❌ ${originDisplay} -> 被拒绝 (${corsValue})`);
      } else {
        colorLog('blue', `ℹ️  ${originDisplay} -> 允许访问 (${corsValue})`);
      }
    } else {
      colorLog('red', `❌ ${result.origin} -> 请求失败: ${result.error}`);
    }
  }

  // 环境检测
  console.log('\n🔧 环境信息:');
  
  try {
    const envResponse = await axios.get(`${TEST_CONFIG.serverUrl}/health`);
    const envData = envResponse.data;
    
    if (envData.environment) {
      const env = envData.environment;
      if (env === 'production') {
        colorLog('red', `📍 当前环境: ${env} (生产环境)`);
        colorLog('yellow', '   ⚠️  生产环境应该限制CORS域名');
      } else {
        colorLog('green', `📍 当前环境: ${env} (开发环境)`);
        colorLog('blue', '   ℹ️  开发环境允许所有域名访问');
      }
    }
  } catch (error) {
    colorLog('yellow', '⚠️  无法获取环境信息');
  }

  console.log('\n💡 CORS配置说明:');
  console.log('   ✅ 绿色: 正常允许的域名');
  console.log('   ⚠️  黄色: 开发环境允许所有域名 (*)');
  console.log('   ❌ 红色: 被拒绝的域名或请求失败');
  console.log('   ℹ️  蓝色: 其他情况');
  
  console.log('\n🔒 安全建议:');
  console.log('   • 生产环境必须配置 CORS_ALLOWED_ORIGINS');
  console.log('   • 只允许信任的域名访问API');
  console.log('   • 定期检查和更新允许的域名列表');
}

// 运行测试
if (require.main === module) {
  runCORSTests().catch(error => {
    colorLog('red', `测试失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { runCORSTests, testCORS };
