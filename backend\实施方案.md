TTS 应用单机健壮部署实施方案 (Ubuntu + Node.js + Redis + PostgreSQL + PM2)

本方案旨在将原Cloudflare Worker项目迁移至一台Ubuntu服务器，实现功能对等且具备高可用性和数据持久性的生产级部署。

1. 核心架构与技术选型
模块	技术选型	作用与优势
操作系统	Ubuntu 22.04 LTS	稳定、社区支持广泛。
运行时	Node.js v18+	现代JavaScript特性支持，性能优越。
Web框架	Express.js	成熟稳定，生态丰富，快速开发API和WebSocket服务。
进程管理	PM2	保证应用后台运行、自动重启、负载均衡（单机多核）和日志管理。
实时任务管理 (替代DO)	Redis	核心健壮方案。高性能内存数据库，用于：<br/>1. 任务队列: 管理待处理的TTS任务。<br/>2. 实时状态跟踪: 存储每个任务的详细状态（processing, complete, failed）。<br/>3. WebSocket会话映射: 将用户连接ID与任务ID关联。<br/>4. 发布/订阅: 在多进程模式下广播任务状态更新。
持久化数据 (替代KV)	PostgreSQL	健壮数据存储。功能强大的关系型数据库，用于：<br/>1. 存储用户账户、密码、邮箱。<br/>2. 管理卡密信息和使用记录。<br/>3. 持久化记录用户用量统计。
文件存储 (替代R2)	本地文件系统 + Nginx	优化文件服务。音频文件存放在本地磁盘。使用Nginx作为反向代理和静态文件服务器，性能远高于Node.js直接提供文件服务。
配置管理	.env 文件 + dotenv	标准的环境变量管理方式，安全且灵活。
2. 详细实施步骤
步骤 2.1: 服务器环境准备

安装基础软件:

Generated bash
sudo apt update
sudo apt install -y nodejs npm nginx postgresql redis-server git


配置Node.js版本 (推荐使用nvm):

Generated bash
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.1/install.sh | bash
source ~/.bashrc
nvm install 18
nvm use 18
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Bash
IGNORE_WHEN_COPYING_END

安装PM2:

Generated bash
npm install pm2 -g
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Bash
IGNORE_WHEN_COPYING_END

配置PostgreSQL:

创建一个数据库和用户供应用使用。

Generated sql
sudo -u postgres psql
CREATE DATABASE tts_app_db;
CREATE USER tts_app_user WITH PASSWORD 'a_strong_password';
GRANT ALL PRIVILEGES ON DATABASE tts_app_db TO tts_app_user;
\q
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
SQL
IGNORE_WHEN_COPYING_END

创建项目目录:

Generated bash
sudo mkdir -p /var/www/tts-app
sudo chown -R $USER:$USER /var/www/tts-app
cd /var/www/tts-app
git clone <your-repo-url> .  # 或初始化新项目 git init
npm install
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Bash
IGNORE_WHEN_COPYING_END

创建文件存储目录:

Generated bash
sudo mkdir -p /var/data/tts-app/audios
sudo chown -R www-data:www-data /var/data/tts-app # Nginx/Node.js用户需要权限
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Bash
IGNORE_WHEN_COPYING_END
步骤 2.2: 项目代码改造
文件结构:
Generated code
/tts-app
├── src/
│   ├── api/          # Express路由
│   │   ├── auth.js
│   │   ├── tts.js
│   │   └── user.js
│   ├── services/     # 核心服务
│   │   ├── redisClient.js
│   │   ├── dbClient.js
│   │   ├── ttsProcessor.js  # 封装了splitText, processChunks等逻辑
│   │   └── websocketManager.js
│   ├── utils/        # 可复用的工具函数
│   └── app.js        # Express应用主入口
├── public/           # 静态文件 (如果需要)
├── .env              # 环境变量
├── package.json
└── ecosystem.config.js # PM2配置文件
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END
核心改造点:

.env 配置文件:

Generated dotenv
NODE_ENV=production
PORT=3000
DATABASE_URL="postgresql://tts_app_user:a_strong_password@localhost:5432/tts_app_db"
REDIS_URL="redis://localhost:6379"
JWT_SECRET="your_jwt_secret_key"
TENCENT_SECRET_ID="..."
# ... 其他所有配置
AUDIO_STORAGE_PATH="/var/data/tts-app/audios"
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Dotenv
IGNORE_WHEN_COPYING_END

Redis 客户端 (src/services/redisClient.js):

使用 ioredis 库，它支持连接池和自动重连，非常健壮。

Generated javascript
const IORedis = require('ioredis');
const client = new IORedis(process.env.REDIS_URL);

client.on('error', err => console.error('Redis Client Error', err));

module.exports = client;
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

PostgreSQL 客户端 (src/services/dbClient.js):

使用 pg 库，并配置连接池。

Generated javascript
const { Pool } = require('pg');
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

module.exports = {
  query: (text, params) => pool.query(text, params),
};
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

WebSocket 与任务管理 (替代DO):

src/app.js: 设置WebSocket路由。

Generated javascript
const express = require('express');
const enableWs = require('express-ws');
const websocketManager = require('./services/websocketManager');

const app = express();
enableWs(app);

app.ws('/api/tts/ws/generate', (ws, req) => {
  websocketManager.handleConnection(ws, req);
});

// ... 其他HTTP路由
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

src/services/websocketManager.js:

这里是替代Durable Object的关键逻辑。

Generated javascript
const redis = require('./redisClient');
const ttsProcessor = require('./ttsProcessor');
const { v4: uuidv4 } = require('uuid');

const TASK_KEY_PREFIX = 'tts:task:';
const TASK_EXPIRATION_SECONDS = 24 * 60 * 60; // 24小时

async function handleConnection(ws, req) {
  const taskId = uuidv4();
  const taskKey = `${TASK_KEY_PREFIX}${taskId}`;
  
  // 1. 初始化任务状态并存入Redis
  const initialTaskData = {
    status: 'initialized',
    taskId,
    createdAt: Date.now(),
    // ... 从请求中获取用户信息 (需认证)
  };
  // 使用 HSET 存储任务数据，更灵活
  await redis.hset(taskKey, initialTaskData);
  await redis.expire(taskKey, TASK_EXPIRATION_SECONDS);
  
  ws.send(JSON.stringify({ type: 'initialized', taskId }));

  ws.on('message', async (msg) => {
    const data = JSON.parse(msg);
    if (data.action === 'start') {
      try {
        // 2. 更新状态为 processing
        await redis.hset(taskKey, 'status', 'processing');
        
        // 3. 调用后台处理器，传入 taskId 和 ws 实例 (用于发送进度)
        // 注意：这里不能直接传ws，因为ws对象不能被序列化。
        // 而是通过 taskId 让 ttsProcessor 能找到并更新Redis中的状态。
        // 进度更新可以通过Redis的Pub/Sub来通知WebSocket管理器。
        await ttsProcessor.start(taskId, data); 
        
      } catch (error) {
        await redis.hset(taskKey, 'status', 'failed', 'error', error.message);
        ws.send(JSON.stringify({ type: 'error', message: error.message }));
      }
    }
  });

  ws.on('close', () => {
    // 清理逻辑，例如取消正在运行的任务
    console.log(`WebSocket closed for task ${taskId}`);
  });
}

module.exports = { handleConnection };
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

src/services/ttsProcessor.js:

这是无状态的后台任务处理器。

Generated javascript
const redis = require('./redisClient');
// ... 复用原项目的 splitText, processChunks 等函数

async function start(taskId, taskData) {
  const taskKey = `tts:task:${taskId}`;
  
  try {
    // 更新进度到Redis
    await redis.hset(taskKey, 'progress', '文本分割中...');
    const chunks = await splitText(taskData.input);
    
    await redis.hset(taskKey, 'progress', '音频生成中...');
    const audioBuffer = await processChunks(chunks, ...);
    
    // 存储文件到本地
    const filePath = `${process.env.AUDIO_STORAGE_PATH}/${taskId}.mp3`;
    fs.writeFileSync(filePath, audioBuffer);
    
    // 任务完成，更新最终状态到Redis
    const finalStatus = {
      status: 'complete',
      downloadPath: `/media/${taskId}.mp3`, // Nginx服务的路径
      completedAt: Date.now(),
    };
    await redis.hset(taskKey, finalStatus);
    
  } catch (error) {
    await redis.hset(taskKey, 'status', 'failed', 'error', error.message);
  }
}

module.exports = { start };
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

注意: ws.send 的进度更新需要更精巧的设计。一种健壮的方式是：

websocketManager 在处理连接时，订阅一个与 taskId 相关的Redis频道 (SUBSCRIBE tts:progress:${taskId})。

ttsProcessor 在处理任务时，通过Redis发布进度消息 (PUBLISH tts:progress:${taskId} '{"message":"进度..."}')。

websocketManager 收到订阅消息后，通过对应的 ws 连接将进度发给客户端。
这样就实现了后台任务与WebSocket连接的解耦，非常健壮。

步骤 2.3: 部署与服务配置

Nginx 配置 (/etc/nginx/sites-available/tts-app):

配置反向代理和静态文件服务。

Generated nginx
server {
    listen 80;
    server_name your_domain.com;

    # API 和 WebSocket 反向代理
    location /api {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # 健壮的文件服务 (替代Node.js直接发送)
    # /media/ 路径映射到服务器的存储目录
    location /media {
        alias /var/data/tts-app/audios;
        expires 7d; # 浏览器缓存7天
        add_header Cache-Control "public";
    }
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Nginx
IGNORE_WHEN_COPYING_END

启用配置: sudo ln -s /etc/nginx/sites-available/tts-app /etc/nginx/sites-enabled/

测试并重启Nginx: sudo nginx -t && sudo systemctl restart nginx

PM2 配置文件 (ecosystem.config.js):

这是保证应用高可用的关键。

Generated javascript
module.exports = {
  apps: [
    {
      name: 'tts-app',
      script: 'src/app.js',
      instances: 'max', // 在单机上启动与CPU核心数相等的进程
      exec_mode: 'cluster', // 启用集群模式，实现负载均衡
      autorestart: true,
      watch: false, // 生产环境不建议watch
      max_memory_restart: '1G', // 内存超限时自动重启
      env: {
        NODE_ENV: 'production',
      },
    },
  ],
};
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

启动应用:

Generated bash
cd /var/www/tts-app
pm2 start ecosystem.config.js
pm2 save          # 保存当前进程列表，以便服务器重启后自动恢复
pm2 startup       # 生成开机自启脚本
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Bash
IGNORE_WHEN_COPYING_END
3. 总结：健壮性体现在何处

进程健壮性: PM2确保了即使应用因未知错误崩溃，也能在几毫秒内自动重启，服务中断时间极短。cluster模式利用了多核CPU，提高了吞吐量和稳定性。

任务健壮性: Redis作为任务中心，即使Node.js进程重启，任务状态和数据也得以保留。应用重启后可以继续处理或查询之前的任务。

数据健壮性: PostgreSQL提供了ACID事务保证，确保用户数据和卡密操作的原子性和一致性，远比KV存储安全。

服务性能与健壮性: Nginx处理静态文件下载的性能远超Node.js，并且可以配置更复杂的缓存策略和安全头，减轻了Node.js应用的负担。

连接健壮性: 使用ioredis和pg的连接池，能自动处理与数据库和Redis的连接中断与重连，无需在业务代码中关心底层连接问题。

通过以上方案，原有的Cloudflare Worker应用就被成功地、健壮地迁移到了单台Ubuntu服务器上，具备了生产环境所需的高可用性和数据安全性。