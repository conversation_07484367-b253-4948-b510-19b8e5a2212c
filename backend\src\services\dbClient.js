const { Pool } = require('pg');

class DatabaseClient {
  constructor() {
    // 根据环境动态配置连接池
    const isProduction = process.env.NODE_ENV === 'production';

    // 获取配置参数（优先使用环境变量，否则使用默认值）
    const poolConfig = {
      connectionString: process.env.DATABASE_URL,
      max: parseInt(process.env.DB_POOL_MAX) || (isProduction ? 50 : 20),
      min: parseInt(process.env.DB_POOL_MIN) || (isProduction ? 5 : 2),
      idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT) || 30000,
      connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT) || 5000,
      acquireTimeoutMillis: parseInt(process.env.DB_ACQUIRE_TIMEOUT) || 60000,
    };

    // 生产环境启用SSL
    if (isProduction && process.env.DB_SSL_ENABLED === 'true') {
      poolConfig.ssl = { rejectUnauthorized: false };
    }

    this.pool = new Pool(poolConfig);

    // 连接池事件监听
    this.pool.on('error', (err) => {
      console.error('Unexpected error on idle client', err);
    });

    this.pool.on('connect', (client) => {
      if (process.env.DEBUG === 'true') {
        console.log('New database client connected');
      }
    });

    this.pool.on('remove', (client) => {
      if (process.env.DEBUG === 'true') {
        console.log('Database client removed');
      }
    });

    // 输出连接池配置信息
    console.log(`Database pool initialized: max=${poolConfig.max}, min=${poolConfig.min}, env=${process.env.NODE_ENV}`);
  }

  async query(text, params) {
    const start = Date.now();
    try {
      const res = await this.pool.query(text, params);
      const duration = Date.now() - start;
      if (process.env.DEBUG === 'true') {
        console.log('Executed query', { text, duration, rows: res.rowCount });
      }
      return res;
    } catch (error) {
      console.error('Database query error:', { text, error: error.message });
      throw error;
    }
  }

  async getClient() {
    return await this.pool.connect();
  }

  async end() {
    await this.pool.end();
  }
}

module.exports = new DatabaseClient();
