高可用 API 代理网关：开发手册 (Node.js + sing-box)
1. 概述

本手册旨在指导开发者构建一个高可用的、智能的代理网关系统。该系统主要用于解决在进行大规模 API 请求（如网络爬虫、数据聚合）时，因目标服务器的速率限制、地区封锁或 IP 封禁（如返回 403, 429 状态码,“quota_exceeded”“Too Many Requests”配额不足等响应）而导致的业务中断问题。

1.1. 核心架构：大脑与执行者

我们采用“关注点分离”的设计原则，将系统解耦为两个核心组件：

执行者 (The Executor) - sing-box 服务:

一个强大、高效的网络代理核心。

它负责管理所有代理节点（如 VLESS）的底层连接细节。

它本身不具备业务逻辑判断能力，是一个忠实的指令执行者。

大脑 (The Brain) - Node.js 应用:

您的核心业务应用。

它负责发起 API 请求，并理解请求的业务层结果（成功或失败）。

它维护一个“健康代理节点池”，并根据业务反馈动态管理这个池。

它通过 sing-box 提供的 Controller API 来指挥执行者切换代理节点。

这种架构的优势在于，复杂的网络协议和连接管理与复杂的业务逻辑完全解耦，使得系统每一部分都更清晰、更易于维护和扩展。

2. 架构与工作流

初始化: Node.js 应用（大脑）启动，从可用节点列表中选择一个默认节点，并通过 Controller API 指示 sing-box 使用该节点。

请求发送: 大脑将所有 API 请求发送到一个固定的本地代理端口（如 127.0.0.1:1080）。

流量转发: sing-box（执行者）接收到请求，根据大脑当前指定的节点，将请求通过该节点的IP转发出去。

结果分析:

如果成功 (HTTP 200): 大脑不做任何操作，继续使用当前节点。

如果失败 (HTTP 403, 429, etc.): 大脑将当前节点从“健康池”中移除，并选择下一个健康节点。

下达指令: 大脑通过 Controller API 向 sing-box 发送指令，命令其切换到新的健康节点。

重试: 大脑使用同一个本地代理端口（127.0.0.1:1080）重新发起失败的请求，此时流量将通过新的节点IP发出。

3. 构建大脑 (Node.js)
3.1. 核心原则

大脑全权掌控状态: 大脑维护着“当前使用节点”和“健康节点池”的状态。它不询问，只命令。

通信是单向的: 只有大脑可以向 sing-box 发送指令，sing-box 不会反向通信。

指令是稀疏的: 只在需要切换时才发送 API 指令，而非每次请求都发送。

3.2. 伪代码示例

这是一个完整的、包含所有逻辑的伪代码实现。

Generated typescript
// 文件: aiproxy_client.js

// --- 全局配置与状态管理 ---
const SINGBOX_API_ENDPOINT = "http://127.0.0.1:9090";
const SELECTOR_NAME = "proxy-selector";
const PROXY_AGENT = new SocksProxyAgent("socks5h://127.0.0.1:1080");

// 健康节点池 (在实际应用中，这个列表应从配置文件或订阅脚本动态加载)
let healthyNodeTags = ["node-A", "node-B", "node-C"];

// 大脑自己记录的当前状态
let currentNodeTag = "";

// --- 核心功能函数 ---

/**
 * 命令 sing-box 切换到指定节点
 * @param {string} nodeTag
 */
async function commandSwitchNode(nodeTag) {
    console.log(`[BRAIN] Issuing command: Switch to ${nodeTag}`);
    try {
        await axios.put(
            `${SINGBOX_API_ENDPOINT}/outbounds/${SELECTOR_NAME}`,
            { "outbound": nodeTag }
        );
        // 命令成功后，更新大脑自己的状态记录
        currentNodeTag = nodeTag;
        console.log(`[BRAIN] Switch successful. Current node is now ${currentNodeTag}`);
        return true;
    } catch (error) {
        console.error(`[BRAIN] Command to switch to ${nodeTag} FAILED: ${error.message}`);
        return false;
    }
}

/**
 * 判断是否为业务层面的失败
 * @param {Error} error
 */
function isApiFailure(error) {
    if (!error.response) return true; // 网络层错误也算失败
    
    const status = error.response.status;
    const responseBody = JSON.stringify(error.response.data || "").toLowerCase();
    
    if ([401, 403, 429].includes(status)) return true;
    if (responseBody.includes("quota_exceeded") || responseBody.includes("too many requests")) return true;
    
    return false;
}

/**
 * 使用代理发起 API 请求，并实现智能故障切换
 * @param {string} apiUrl
 */
async function makeRequest(apiUrl) {
    if (healthyNodeTags.length === 0) {
        throw new Error("All proxy nodes have failed.");
    }
    
    // 1. 尝试使用当前节点发起请求
    console.log(`[BRAIN] Attempting request to ${apiUrl} via node ${currentNodeTag}`);
    try {
        const response = await axios.get(apiUrl, { httpsAgent: PROXY_AGENT, timeout: 15000 });
        console.log(`[BRAIN] Request successful via ${currentNodeTag}`);
        return response.data; // 成功，直接返回
    } catch (error) {
        console.warn(`[BRAIN] Request via ${currentNodeTag} failed: ${error.message}`);
        
        // 2. 判断失败类型
        if (isApiFailure(error)) {
            console.error(`[BRAIN] API-level failure detected for node ${currentNodeTag}. Removing from pool.`);
            
            // 3. 从健康池中移除失败的节点
            const failedNode = currentNodeTag;
            healthyNodeTags = healthyNodeTags.filter(tag => tag !== failedNode);
            
            if (healthyNodeTags.length === 0) {
                throw new Error("All proxy nodes have failed after the last attempt.");
            }
            
            // 4. 选择下一个健康节点并下达切换指令
            const nextNode = healthyNodeTags[0];
            await commandSwitchNode(nextNode);
            
            // 5. 递归调用，用新节点重试一次
            console.log(`[BRAIN] Retrying the request with new node ${nextNode}...`);
            return makeRequest(apiUrl);
        } else {
            // 如果不是我们定义的业务失败，则直接抛出异常
            throw error;
        }
    }
}

/**
 * 初始化系统
 */
async function initialize() {
    if (healthyNodeTags.length > 0) {
        // 应用启动时，确保 sing-box 使用的是我们健康池里的第一个节点
        await commandSwitchNode(healthyNodeTags[0]);
    } else {
        console.error("Initialization failed: No healthy nodes available.");
    }
}

// --- 程序入口 ---
async function main() {
    await initialize();
    
    try {
        const data = await makeRequest("https://api.example.com/sensitive-data");
        console.log("Final data received:", data);
    } catch (e) {
        console.error("FATAL: Could not complete the request.", e.message);
        console.log("Remaining healthy nodes:", healthyNodeTags);
    }
}

main();
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
TypeScript
IGNORE_WHEN_COPYING_END
4. 自动化与维护：处理订阅

手动维护100个节点的配置是不现实的。我们需要一个脚本来自动从订阅链接更新节点。

4.1. 思路

创建一个 Shell 脚本，该脚本：

通过 curl 访问订阅链接。

将订阅内容通过一个“订阅转换服务”转换为 sing-box 的 outbounds JSON 数组格式。

将生成的 JSON 数组写入一个单独的文件（如 /etc/sing-box/nodes.json）。

动态生成 config.json 中的 outbounds 列表，包含选择器和所有节点定义。

通过 cron 定时任务（如每天执行一次）来运行此脚本。

脚本运行成功后，重载 (reload) sing-box 服务使其应用新配置，这比 restart 更平滑。

4.2. 更新脚本伪代码 (update_nodes.sh)
Generated bash
#!/bin/bash

# 配置
SUBSCRIBE_URL="YOUR_SUBSCRIBE_LINK_HERE"
CONFIG_FILE="/etc/sing-box/config.json"
TEMP_CONFIG_FILE="/etc/sing-box/config.json.tmp"
CONVERTER_API="https://api.subconverter.example.com/sub?target=singbox-fragment&url=${SUBSCRIBE_URL}" # 'singbox-fragment' 输出纯outbound数组

echo "Fetching and converting nodes..."
# 获取节点数组
NODES_JSON=$(curl -fsSL "${CONVERTER_API}")

# 检查是否获取成功
if [ -z "$NODES_JSON" ]; then
    echo "Failed to fetch or convert nodes. Aborting."
    exit 1
fi

# 读取 config.json 的模板（不含outbounds部分）
CONFIG_TEMPLATE=$(cat <<'EOF'
{
  "log": { ... },
  "experimental": { ... },
  "inbounds": [ ... ],
  "route": { ... },
  "outbounds": []
}
EOF
)

# 使用 jq 工具将节点 JSON 插入到模板中
# 首先，需要提取所有节点的 tag 来填充 selector
NODE_TAGS=$(echo "$NODES_JSON" | jq -r '[.[].tag] | @json')

# 其次，将节点数组本身和 selector 合并
FINAL_OUTBOUNDS=$(echo "$NODES_JSON" | jq --argjson tags "$NODE_TAGS" \
    '. + [{"type": "selector", "tag": "proxy-selector", "outbounds": $tags, "default": $tags[0]}]')

# 最后，将完整的 outbounds 注入到主配置模板中
echo "$CONFIG_TEMPLATE" | jq --argjson outbounds "$FINAL_OUTBOUNDS" '.outbounds = $outbounds' > "$TEMP_CONFIG_FILE"

# 替换旧的配置文件并重载服务
mv "$TEMP_CONFIG_FILE" "$CONFIG_FILE"
echo "Config updated. Reloading sing-box service..."
sudo systemctl reload sing-box

echo "Update complete."
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Bash
IGNORE_WHEN_COPYING_END
4.3. 设置定时任务
Generated bash
# 打开 crontab 编辑器
sudo crontab -e

# 添加以下行，设置每天凌晨4点执行
0 4 * * * /path/to/your/update_nodes.sh >> /var/log/singbox_update.log 2>&1
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Bash
IGNORE_WHEN_COPYING_END
5. 总结与最佳实践

状态持久化: 对于生产环境，建议将 healthyNodeTags 列表存储在 Redis 或数据库中，而不是内存里，这样即使 Node.js 应用重启，也不会丢失节点状态。

日志: 详细的日志是排查问题的关键。在大脑的每个决策点都应记录日志。

安全: sing-box 的 Controller API 默认监听在 127.0.0.1，是安全的。如需远程控制，务必配置认证或通过防火墙严格限制访问。

遵循本手册，您将能够构建一个强大、智能且可自我修复的代理网关，为您的业务提供稳定可靠的数据支持。