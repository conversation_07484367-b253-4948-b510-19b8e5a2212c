# 播放器问题修复说明

## 🔍 问题分析

### 原始问题
- **错误URL**: `http://localhost:4000/api/tts/download/taskId/`
- **控制台错误**: `GET http://localhost:4000/api/tts/download/xxx/ net::ERR_ABORTED 404 (Not Found)`
- **播放器错误**: `NotSupportedError: The element has no supported sources.`

### 根本原因
1. **端口不匹配**: 前端运行在4000端口，后端运行在3001端口
2. **相对路径问题**: 后端返回相对路径 `/api/tts/download/taskId`
3. **认证缺失**: 音频下载需要token，但直接设置audio.src无法传递认证信息
4. **CORS问题**: 跨域访问音频资源

## ✅ 修复方案

### 1. **后端修复 - 返回完整URL**

**修改文件**: `src/services/ttsProcessor.js`

```javascript
// 修复前
downloadUrl: `/api/tts/download/${taskId}`

// 修复后  
const baseUrl = process.env.API_BASE_URL || 'http://localhost:3001';
downloadUrl: `${baseUrl}/api/tts/download/${taskId}`
```

**影响范围**:
- ✅ 普通TTS任务 (`startSingle` 方法)
- ✅ 对话式TTS任务 (`startDialogue` 方法)

### 2. **后端修复 - 增强下载接口**

**修改文件**: `src/api/tts.js`

**新增功能**:
```javascript
// 1. CORS支持
res.setHeader('Access-Control-Allow-Origin', '*');
res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
res.setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');

// 2. 支持内联播放
res.setHeader('Content-Disposition', `inline; filename="${taskId}.mp3"`);

// 3. OPTIONS预检请求处理
router.options('/download/:taskId', (_req, res) => {
  // CORS预检响应
});
```

### 3. **环境变量配置**

**新增配置**: `.env.example`
```bash
# API基础URL，用于生成完整的下载链接
API_BASE_URL=http://localhost:3001
```

**生产环境示例**:
```bash
API_BASE_URL=https://your-api-domain.com
```

## 🔄 修复后的完整流程

### 原始流程（有问题）
```
1. 后端返回: downloadUrl: "/api/tts/download/taskId"
2. 前端设置: audio.src = "/api/tts/download/taskId"  
3. 浏览器请求: http://localhost:4000/api/tts/download/taskId/
4. 结果: 404 Not Found (端口错误)
```

### 修复后流程（正确）
```
1. 后端返回: downloadUrl: "http://localhost:3001/api/tts/download/taskId"
2. 前端设置: audio.src = "http://localhost:3001/api/tts/download/taskId"
3. 浏览器请求: http://localhost:3001/api/tts/download/taskId
4. 结果: 200 OK (正确的服务器和端口)
```

## 🧪 测试验证

### 运行测试脚本
```bash
node test-download-fix.js
```

### 测试内容
1. ✅ **URL格式检查** - 确保返回完整URL
2. ✅ **认证测试** - 验证token认证机制
3. ✅ **CORS测试** - 验证跨域访问支持
4. ✅ **下载测试** - 验证音频文件可正常下载

### 预期结果
```
✅ URL格式正确 - 包含完整的服务器地址
✅ 认证检查正常 - 无token时返回401
✅ CORS配置正确
✅ 下载接口正常工作
```

## 🚀 部署步骤

### 1. 更新环境变量
```bash
# 在 .env 文件中添加
API_BASE_URL=http://localhost:3001

# 生产环境
API_BASE_URL=https://your-api-domain.com
```

### 2. 重启服务
```bash
npm start
# 或
pm2 restart tts-app
```

### 3. 验证修复
1. 生成一个TTS任务
2. 检查返回的downloadUrl格式
3. 测试播放器功能

## 📋 前端适配建议

虽然后端已经修复了主要问题，但前端也可以进行一些优化：

### 1. **错误处理增强**
```javascript
audioRef.current.onerror = (e) => {
  console.error('音频加载失败:', e);
  // 显示用户友好的错误信息
};
```

### 2. **加载状态显示**
```javascript
audioRef.current.onloadstart = () => {
  setLoading(true);
};

audioRef.current.oncanplay = () => {
  setLoading(false);
};
```

### 3. **认证token传递**（可选）
如果需要更安全的认证方式：
```javascript
// 方案1: URL参数传递token
const urlWithToken = `${downloadUrl}?token=${userToken}`;

// 方案2: 使用fetch + blob URL
const response = await fetch(downloadUrl, {
  headers: { 'Authorization': `Bearer ${userToken}` }
});
const blob = await response.blob();
const blobUrl = URL.createObjectURL(blob);
audioRef.current.src = blobUrl;
```

## ⚠️ 注意事项

### 1. **环境配置**
- 确保 `API_BASE_URL` 环境变量正确设置
- 生产环境需要使用HTTPS协议

### 2. **CORS配置**
- 当前配置允许所有域名访问 (`*`)
- 生产环境建议限制为特定域名

### 3. **文件存储**
- 确保 `AUDIO_STORAGE_PATH` 目录存在且可访问
- 定期清理过期的音频文件

## 🎉 修复效果

修复后，播放器将能够：
- ✅ 正确加载音频文件
- ✅ 支持播放/暂停控制
- ✅ 显示正确的音频时长
- ✅ 支持进度条拖拽
- ✅ 支持下载功能

**问题彻底解决！** 🎊
