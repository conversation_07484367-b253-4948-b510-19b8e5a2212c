const dbClient = require('./src/services/dbClient');
const { bcrypt } = require('./src/services/authService');

async function createTestUser() {
  try {
    console.log('🔧 创建测试用户...');
    
    const username = 'testuser';
    const password = 'oldpassword123';
    const email = '<EMAIL>';
    
    // 检查用户是否已存在
    const existingUser = await dbClient.query(
      'SELECT username FROM users WHERE username = $1 OR email = $2',
      [username, email]
    );
    
    if (existingUser.rows.length > 0) {
      console.log('⚠️ 测试用户已存在，删除旧用户...');
      await dbClient.query(
        'DELETE FROM users WHERE username = $1 OR email = $2',
        [username, email]
      );
    }
    
    // 创建新用户
    const hashedPassword = await bcrypt(password);
    await dbClient.query(
      'INSERT INTO users (username, password_hash, email, created_at) VALUES ($1, $2, $3, CURRENT_TIMESTAMP)',
      [username, hashedPassword, email]
    );
    
    console.log('✅ 测试用户创建成功！');
    console.log(`用户名: ${username}`);
    console.log(`密码: ${password}`);
    console.log(`邮箱: ${email}`);
    
    // 关闭数据库连接
    await dbClient.end();
    
  } catch (error) {
    console.error('❌ 创建测试用户失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  createTestUser();
}

module.exports = { createTestUser };
