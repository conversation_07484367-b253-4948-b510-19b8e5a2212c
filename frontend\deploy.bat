@echo off
echo ========================================
echo    Cloudflare Pages 部署准备脚本
echo ========================================
echo.

echo [1/4] 检查环境变量配置...
if not exist .env.local (
    echo 警告: .env.local 文件不存在
    echo 请创建 .env.local 文件并配置 NEXT_PUBLIC_API_URL
    pause
    exit /b 1
)

echo [2/4] 安装依赖...
call npm install
if %errorlevel% neq 0 (
    echo 错误: 依赖安装失败
    pause
    exit /b 1
)

echo [3/4] 构建项目...
call npm run build
if %errorlevel% neq 0 (
    echo 错误: 项目构建失败
    pause
    exit /b 1
)

echo [4/4] 构建完成！
echo.
echo 构建文件位于: out/ 目录
echo.
echo 下一步操作：
echo 1. 访问 https://dash.cloudflare.com
echo 2. 进入 Pages 部分
echo 3. 创建新项目或更新现有项目
echo 4. 上传 out/ 目录中的所有文件
echo.
echo 或者使用 Git 连接自动部署
echo.
pause
