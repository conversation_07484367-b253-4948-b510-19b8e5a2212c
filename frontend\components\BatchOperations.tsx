"use client"

import React, { useState, useCallback } from 'react';
import { Copy, Upload, CheckCircle, AlertTriangle, Info, BarChart3 } from 'lucide-react';
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  formatDialogueForCopy,
  copyToClipboard,
  parseImportText,
  validateImportData,
  generateSampleImportText,
  getDialogueStats
} from "@/lib/batch-operations";

interface Voice {
  id: string;
  name: string;
  gender: string;
  language?: string;
  description: string;
  preview?: string;
}

interface DialogueLine {
  id: number;
  voice: string;
  text: string;
}

interface BatchOperationsProps {
  dialogueLines: DialogueLine[];
  voices: Voice[];
  onImport: (lines: DialogueLine[], mode: 'replace' | 'append') => void;
  className?: string;
}

interface BatchOperationState {
  showImportDialog: boolean;
  showStatsDialog: boolean;
  importText: string;
  importMode: 'replace' | 'append';
  importPreview: DialogueLine[];
  importErrors: string[];
  importWarnings: string[];
  isImporting: boolean;
  isCopying: boolean;
  copySuccess: boolean;
  showSample: boolean;
}

export const BatchOperations: React.FC<BatchOperationsProps> = ({
  dialogueLines,
  voices,
  onImport,
  className = ""
}) => {
  const [state, setState] = useState<BatchOperationState>({
    showImportDialog: false,
    showStatsDialog: false,
    importText: "",
    importMode: 'replace',
    importPreview: [],
    importErrors: [],
    importWarnings: [],
    isImporting: false,
    isCopying: false,
    copySuccess: false,
    showSample: false
  });

  // 复制对话功能
  const handleCopyDialogue = useCallback(async () => {
    if (state.isCopying) return;

    setState(prev => ({ ...prev, isCopying: true }));

    try {
      const copyText = formatDialogueForCopy(dialogueLines, voices);
      const success = await copyToClipboard(copyText);

      if (success) {
        setState(prev => ({ ...prev, copySuccess: true }));
        setTimeout(() => {
          setState(prev => ({ ...prev, copySuccess: false }));
        }, 2000);
      } else {
        throw new Error('复制失败');
      }

    } catch (error) {
      console.error('复制失败:', error);
      // TODO: 显示错误提示
    } finally {
      setState(prev => ({ ...prev, isCopying: false }));
    }
  }, [dialogueLines, voices, state.isCopying]);

  // 显示示例文本
  const handleShowSample = useCallback(() => {
    const sampleText = generateSampleImportText(voices);
    setState(prev => ({
      ...prev,
      importText: sampleText,
      showSample: true
    }));

    // 解析示例文本
    const { validLines, errors, warnings } = parseImportText(sampleText, voices);
    setState(prev => ({
      ...prev,
      importPreview: validLines,
      importErrors: errors,
      importWarnings: warnings
    }));
  }, [voices]);

  // 显示统计信息
  const handleShowStats = useCallback(() => {
    setState(prev => ({ ...prev, showStatsDialog: true }));
  }, []);

  // 处理导入文本变化
  const handleImportTextChange = useCallback((text: string) => {
    setState(prev => ({ ...prev, importText: text, showSample: false }));

    if (text.trim()) {
      const { validLines, errors, warnings } = parseImportText(text, voices);
      setState(prev => ({
        ...prev,
        importPreview: validLines,
        importErrors: errors,
        importWarnings: warnings
      }));
    } else {
      setState(prev => ({
        ...prev,
        importPreview: [],
        importErrors: [],
        importWarnings: []
      }));
    }
  }, [voices]);

  // 执行导入
  const handleImportDialogue = useCallback(async () => {
    if (state.importPreview.length === 0) return;

    setState(prev => ({ ...prev, isImporting: true }));

    try {
      await onImport(state.importPreview, state.importMode);

      // 关闭对话框并重置状态
      setState(prev => ({
        ...prev,
        showImportDialog: false,
        importText: "",
        importPreview: [],
        importErrors: [],
        importWarnings: [],
        isImporting: false,
        showSample: false
      }));

    } catch (error) {
      console.error('导入失败:', error);
      setState(prev => ({ ...prev, isImporting: false }));
    }
  }, [state.importPreview, state.importMode, onImport]);

  // 检查是否有可复制的内容
  const hasValidContent = dialogueLines.some(line => line.text.trim());

  return (
    <>
      {/* 批量操作按钮组 */}
      <div className={`flex items-center gap-3 ${className}`}>
        <button
          onClick={handleCopyDialogue}
          disabled={state.isCopying || !hasValidContent}
          className={`relative overflow-hidden px-4 py-2 text-xs font-semibold rounded-2xl transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform group ${
            state.isCopying || !hasValidContent
              ? "text-gray-400 bg-gray-100 cursor-not-allowed opacity-50"
              : "text-white bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 hover:from-blue-600 hover:via-blue-700 hover:to-indigo-700 hover:scale-105 backdrop-blur-sm"
          }`}
          title={!hasValidContent ? "没有可复制的对话内容" : "复制所有对话到剪贴板"}
        >
          {/* 渐变光效背景 */}
          {!state.isCopying && !(!hasValidContent) && (
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />
          )}

          {state.isCopying ? (
            <div className="w-3.5 h-3.5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
          ) : state.copySuccess ? (
            <CheckCircle className="w-3.5 h-3.5 text-green-400" />
          ) : (
            <Copy className="w-3.5 h-3.5" />
          )}
          <span className="relative z-10">
            {state.isCopying ? "复制中..." : state.copySuccess ? "已复制" : "复制对话"}
          </span>
        </button>

        <button
          onClick={() => setState(prev => ({ ...prev, showImportDialog: true }))}
          className="relative overflow-hidden px-4 py-2 text-xs font-semibold text-white bg-gradient-to-r from-emerald-500 via-green-600 to-teal-600 hover:from-emerald-600 hover:via-green-700 hover:to-teal-700 rounded-2xl transition-all duration-300 hover:scale-105 flex items-center gap-2 shadow-lg hover:shadow-xl backdrop-blur-sm group"
          title="从剪贴板导入批量对话"
        >
          {/* 渐变光效背景 */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />

          <Upload className="w-3.5 h-3.5 relative z-10" />
          <span className="relative z-10">导入对话</span>
        </button>

        {hasValidContent && (
          <button
            onClick={handleShowStats}
            className="relative overflow-hidden px-4 py-2 text-xs font-semibold text-white bg-gradient-to-r from-purple-500 via-violet-600 to-purple-600 hover:from-purple-600 hover:via-violet-700 hover:to-purple-700 rounded-2xl transition-all duration-300 hover:scale-105 flex items-center gap-2 shadow-lg hover:shadow-xl backdrop-blur-sm group"
            title="查看对话统计信息"
          >
            {/* 渐变光效背景 */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />

            <BarChart3 className="w-3.5 h-3.5 relative z-10" />
            <span className="relative z-10">统计</span>
          </button>
        )}
      </div>

      {/* 导入对话模态框 */}
      <Dialog
        open={state.showImportDialog}
        onOpenChange={(open) => setState(prev => ({ ...prev, showImportDialog: open }))}
      >
        <DialogContent className="max-w-2xl h-[85vh] bg-white/95 backdrop-blur-xl border-0 shadow-2xl flex flex-col overflow-hidden">
          {/* 背景装饰 */}
          <div className="absolute inset-0 bg-gradient-to-br from-emerald-50/80 via-green-50/60 to-teal-50/80" />
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-emerald-200/30 to-green-200/30 rounded-full blur-3xl" />
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-teal-200/30 to-emerald-200/30 rounded-full blur-2xl" />

          {/* 内容区域 */}
          <div className="relative z-10 flex flex-col h-full min-h-0">
            <DialogHeader className="space-y-4 pb-4 flex-shrink-0">
              <div className="flex items-center gap-3">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-emerald-400 to-green-400 rounded-full blur-lg opacity-50 animate-pulse" />
                  <div className="relative p-2 bg-gradient-to-r from-emerald-500 to-green-500 rounded-full shadow-lg">
                    <Upload className="w-5 h-5 text-white" />
                  </div>
                </div>
                <DialogTitle className="text-xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent">
                  批量导入对话
                </DialogTitle>
              </div>
              <DialogDescription className="text-gray-600 bg-white/60 backdrop-blur-sm rounded-xl p-3 border border-emerald-200/50">
                每行格式：<code className="bg-emerald-100 text-emerald-800 px-2 py-1 rounded-lg font-mono text-sm">声音名称@对话内容</code>。支持的声音名称请参考声音库。
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-6 flex-1 overflow-y-auto min-h-0 pr-2 pb-2">
              {/* 导入内容输入区域 */}
              <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-4 border border-emerald-200/50 shadow-lg">
                <div className="flex items-center justify-between mb-3">
                  <label className="text-sm font-semibold text-gray-800">
                    对话内容
                  </label>
                  <button
                    onClick={handleShowSample}
                    className="relative overflow-hidden px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 rounded-xl transition-all duration-300 hover:scale-105 flex items-center gap-1.5 shadow-md hover:shadow-lg group"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />
                    <Info className="w-3 h-3 relative z-10" />
                    <span className="relative z-10">加载示例</span>
                  </button>
                </div>
                <textarea
                  value={state.importText}
                  onChange={(e) => handleImportTextChange(e.target.value)}
                  placeholder={`示例格式：\nAdam@你好！今天过得怎么样？\nJessica@我今天过得很好，谢谢你的关心！\nBrian@那真是太好了，有什么特别的事情吗？`}
                  className="w-full h-32 p-4 border-2 border-emerald-200/60 rounded-xl resize-none focus:ring-4 focus:ring-emerald-500/20 focus:border-emerald-400 transition-all duration-300 text-sm bg-white/80 backdrop-blur-sm placeholder-gray-400"
                />
                {state.showSample && (
                  <div className="mt-3 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200/50">
                    <div className="text-xs text-blue-700 flex items-center gap-2 font-medium">
                      <div className="p-1 bg-blue-500 rounded-full">
                        <Info className="w-3 h-3 text-white" />
                      </div>
                      已加载示例文本，您可以直接导入或修改后导入
                    </div>
                  </div>
                )}
              </div>

              {/* 导入模式选择 */}
              <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-4 border border-emerald-200/50 shadow-lg">
                <label className="text-sm font-semibold text-gray-800 mb-3 block">
                  导入模式
                </label>
                <div className="grid grid-cols-2 gap-3">
                  <label className="relative cursor-pointer group">
                    <input
                      type="radio"
                      checked={state.importMode === 'replace'}
                      onChange={() => setState(prev => ({ ...prev, importMode: 'replace' }))}
                      className="sr-only"
                    />
                    <div className={`p-3 rounded-xl border-2 transition-all duration-300 ${
                      state.importMode === 'replace'
                        ? 'border-orange-400 bg-gradient-to-r from-orange-50 to-red-50 shadow-lg'
                        : 'border-gray-200 bg-white/80 hover:border-orange-300 hover:bg-orange-50/50'
                    }`}>
                      <div className="flex items-center gap-2">
                        <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                          state.importMode === 'replace'
                            ? 'border-orange-500 bg-orange-500'
                            : 'border-gray-300'
                        }`}>
                          {state.importMode === 'replace' && (
                            <div className="w-2 h-2 bg-white rounded-full" />
                          )}
                        </div>
                        <span className={`text-sm font-medium ${
                          state.importMode === 'replace' ? 'text-orange-700' : 'text-gray-700'
                        }`}>
                          替换现有对话
                        </span>
                      </div>
                    </div>
                  </label>

                  <label className="relative cursor-pointer group">
                    <input
                      type="radio"
                      checked={state.importMode === 'append'}
                      onChange={() => setState(prev => ({ ...prev, importMode: 'append' }))}
                      className="sr-only"
                    />
                    <div className={`p-3 rounded-xl border-2 transition-all duration-300 ${
                      state.importMode === 'append'
                        ? 'border-emerald-400 bg-gradient-to-r from-emerald-50 to-green-50 shadow-lg'
                        : 'border-gray-200 bg-white/80 hover:border-emerald-300 hover:bg-emerald-50/50'
                    }`}>
                      <div className="flex items-center gap-2">
                        <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                          state.importMode === 'append'
                            ? 'border-emerald-500 bg-emerald-500'
                            : 'border-gray-300'
                        }`}>
                          {state.importMode === 'append' && (
                            <div className="w-2 h-2 bg-white rounded-full" />
                          )}
                        </div>
                        <span className={`text-sm font-medium ${
                          state.importMode === 'append' ? 'text-emerald-700' : 'text-gray-700'
                        }`}>
                          追加到现有对话
                        </span>
                      </div>
                    </div>
                  </label>
                </div>
              </div>

              {/* 预览区域 */}
              {(state.importPreview.length > 0 || state.importErrors.length > 0 || state.importWarnings.length > 0) && (
                <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-4 border border-emerald-200/50 shadow-lg">
                  <div className="flex items-center gap-2 mb-3">
                    <div className="p-1.5 bg-gradient-to-r from-emerald-500 to-green-500 rounded-lg">
                      <CheckCircle className="w-4 h-4 text-white" />
                    </div>
                    <label className="text-sm font-semibold text-gray-800">
                      预览结果
                    </label>
                    <div className="ml-auto flex items-center gap-2 text-xs">
                      <span className="px-2 py-1 bg-green-100 text-green-700 rounded-lg font-medium">
                        {state.importPreview.length}行有效
                      </span>
                      {state.importErrors.length > 0 && (
                        <span className="px-2 py-1 bg-red-100 text-red-700 rounded-lg font-medium">
                          {state.importErrors.length}行错误
                        </span>
                      )}
                      {state.importWarnings.length > 0 && (
                        <span className="px-2 py-1 bg-yellow-100 text-yellow-700 rounded-lg font-medium">
                          {state.importWarnings.length}个警告
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="max-h-32 overflow-y-auto bg-white/80 backdrop-blur-sm rounded-xl p-3 border border-emerald-200/30 space-y-2">
                    {state.importPreview.map((line, index) => {
                      const voice = voices.find(v => v.id === line.voice);
                      return (
                        <div key={index} className="flex items-start gap-3 p-2 bg-green-50/80 rounded-lg border border-green-200/50">
                          <div className="p-1 bg-green-500 rounded-full">
                            <CheckCircle className="w-3 h-3 text-white" />
                          </div>
                          <span className="text-sm flex-1">
                            <span className="font-semibold text-green-700 bg-green-100 px-2 py-0.5 rounded-lg">{voice?.name}</span>
                            <span className="mx-2 text-gray-400">→</span>
                            <span className="text-gray-700">{line.text.length > 50 ? `${line.text.substring(0, 50)}...` : line.text}</span>
                          </span>
                        </div>
                      );
                    })}
                    {state.importWarnings.map((warning, index) => (
                      <div key={index} className="flex items-start gap-3 p-2 bg-yellow-50/80 rounded-lg border border-yellow-200/50">
                        <div className="p-1 bg-yellow-500 rounded-full">
                          <AlertTriangle className="w-3 h-3 text-white" />
                        </div>
                        <span className="text-sm text-yellow-700 font-medium">{warning}</span>
                      </div>
                    ))}
                    {state.importErrors.map((error, index) => (
                      <div key={index} className="flex items-start gap-3 p-2 bg-red-50/80 rounded-lg border border-red-200/50">
                        <div className="p-1 bg-red-500 rounded-full">
                          <AlertTriangle className="w-3 h-3 text-white" />
                        </div>
                        <span className="text-sm text-red-700 font-medium">{error}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <DialogFooter className="flex gap-3 pt-4 pb-2 px-2 flex-shrink-0 border-t border-emerald-200/50 mt-4 bg-white/80 backdrop-blur-sm">
              <Button
                variant="outline"
                onClick={() => setState(prev => ({
                  ...prev,
                  showImportDialog: false,
                  importText: "",
                  importPreview: [],
                  importErrors: [],
                  importWarnings: [],
                  showSample: false
                }))}
                className="flex-1 border-2 border-gray-300 hover:border-gray-400 hover:bg-gray-50 transition-all duration-300 rounded-xl py-2.5 font-medium"
              >
                取消
              </Button>
              <Button
                onClick={handleImportDialogue}
                disabled={state.importPreview.length === 0 || state.isImporting}
                className="flex-1 min-w-[140px] bg-gradient-to-r from-emerald-500 via-green-600 to-teal-600 hover:from-emerald-600 hover:via-green-700 hover:to-teal-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 rounded-xl py-2.5 font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {state.isImporting ? (
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    <span>导入中...</span>
                  </div>
                ) : (
                  <div className="flex items-center justify-center gap-2">
                    <Upload className="w-4 h-4" />
                    <span>确认导入({state.importPreview.length}行)</span>
                  </div>
                )}
              </Button>
            </DialogFooter>
          </div>
        </DialogContent>
      </Dialog>

      {/* 统计信息对话框 */}
      <Dialog
        open={state.showStatsDialog}
        onOpenChange={(open) => setState(prev => ({ ...prev, showStatsDialog: open }))}
      >
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5 text-purple-600" />
              对话统计信息
            </DialogTitle>
          </DialogHeader>

          {(() => {
            const stats = getDialogueStats(dialogueLines, voices);
            return (
              <div className="space-y-4">
                {/* 基础统计 */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-blue-50 p-3 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{stats.totalLines}</div>
                    <div className="text-sm text-blue-700">对话行数</div>
                  </div>
                  <div className="bg-green-50 p-3 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{stats.totalCharacters}</div>
                    <div className="text-sm text-green-700">总字符数</div>
                  </div>
                </div>

                <div className="bg-purple-50 p-3 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">{stats.averageLength}</div>
                  <div className="text-sm text-purple-700">平均每行字符数</div>
                </div>

                {/* 声音分布 */}
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">声音使用分布</h4>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {stats.voiceDistribution.map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <div className="flex items-center gap-2">
                          <div className={`w-3 h-3 rounded-full ${
                            item.voice.gender === 'male' ? 'bg-blue-500' :
                            item.voice.gender === 'female' ? 'bg-pink-500' : 'bg-gray-500'
                          }`} />
                          <span className="text-sm font-medium">{item.voice.name}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-gray-600">{item.count}次</span>
                          <span className="text-xs text-gray-500">({item.percentage}%)</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            );
          })()}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setState(prev => ({ ...prev, showStatsDialog: false }))}
            >
              关闭
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default BatchOperations;
