#!/usr/bin/env node

/**
 * 服务连接测试脚本
 * 测试Redis和PostgreSQL连接状态
 */

const { Pool } = require('pg');
const IORedis = require('ioredis');

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 测试Redis连接
async function testRedis() {
  log('blue', '\n🔴 测试Redis连接...');
  
  try {
    const redis = new IORedis(process.env.REDIS_URL || 'redis://localhost:6379', {
      connectTimeout: 5000,
      lazyConnect: true
    });

    await redis.connect();
    const result = await redis.ping();
    
    if (result === 'PONG') {
      log('green', '✅ Redis连接成功');
      
      // 测试基本操作
      await redis.set('test:connection', 'success');
      const value = await redis.get('test:connection');
      
      if (value === 'success') {
        log('green', '✅ Redis读写操作正常');
      }
      
      await redis.del('test:connection');
      await redis.disconnect();
      
      return true;
    } else {
      log('red', '❌ Redis ping失败');
      return false;
    }
  } catch (error) {
    log('red', `❌ Redis连接失败: ${error.message}`);
    if (error.code === 'ECONNREFUSED') {
      log('yellow', '   💡 请确保Redis服务已启动');
      log('yellow', '   💡 启动命令: redis-server.exe');
    }
    return false;
  }
}

// 测试PostgreSQL连接
async function testPostgreSQL() {
  log('blue', '\n🐘 测试PostgreSQL连接...');
  
  try {
    const pool = new Pool({
      connectionString: process.env.DATABASE_URL || 'postgresql://postgres:password@localhost:5432/postgres',
      connectionTimeoutMillis: 5000,
    });

    const client = await pool.connect();
    const result = await client.query('SELECT version()');
    
    log('green', '✅ PostgreSQL连接成功');
    log('green', `✅ 数据库版本: ${result.rows[0].version.split(' ')[0]} ${result.rows[0].version.split(' ')[1]}`);
    
    // 测试基本操作
    await client.query('SELECT NOW() as current_time');
    log('green', '✅ PostgreSQL查询操作正常');
    
    client.release();
    await pool.end();
    
    return true;
  } catch (error) {
    log('red', `❌ PostgreSQL连接失败: ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      log('yellow', '   💡 请确保PostgreSQL服务已启动');
      log('yellow', '   💡 启动命令: pg_ctl -D ../data start');
    } else if (error.code === '28P01') {
      log('yellow', '   💡 认证失败，请检查用户名和密码');
    } else if (error.code === '3D000') {
      log('yellow', '   💡 数据库不存在，请先创建数据库');
    }
    return false;
  }
}

// 测试TTS应用数据库
async function testTTSDatabase() {
  log('blue', '\n🎤 测试TTS应用数据库...');
  
  try {
    const pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      connectionTimeoutMillis: 5000,
    });

    const client = await pool.connect();
    
    // 检查是否存在TTS应用的表
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('users', 'cards', 'task_status', 'voice_mappings')
    `);
    
    const tables = tablesResult.rows.map(row => row.table_name);
    
    if (tables.length > 0) {
      log('green', `✅ 找到TTS应用表: ${tables.join(', ')}`);
      
      // 检查语音映射数据
      const voiceResult = await client.query('SELECT COUNT(*) FROM voice_mappings');
      const voiceCount = voiceResult.rows[0].count;
      log('green', `✅ 语音映射数据: ${voiceCount} 条记录`);
      
    } else {
      log('yellow', '⚠️  未找到TTS应用表，需要运行数据库迁移');
      log('yellow', '   💡 运行命令: npm run migrate:create');
    }
    
    client.release();
    await pool.end();
    
    return tables.length > 0;
  } catch (error) {
    log('red', `❌ TTS数据库测试失败: ${error.message}`);
    return false;
  }
}

// 主测试函数
async function runTests() {
  log('blue', '🧪 开始服务连接测试...');
  log('blue', '================================');
  
  const results = {
    redis: false,
    postgresql: false,
    ttsDatabase: false
  };
  
  // 测试Redis
  results.redis = await testRedis();
  
  // 测试PostgreSQL
  results.postgresql = await testPostgreSQL();
  
  // 如果PostgreSQL连接成功，测试TTS数据库
  if (results.postgresql) {
    results.ttsDatabase = await testTTSDatabase();
  }
  
  // 输出测试总结
  log('blue', '\n📊 测试总结:');
  log('blue', '================================');
  
  log(results.redis ? 'green' : 'red', 
      `🔴 Redis: ${results.redis ? '✅ 正常' : '❌ 失败'}`);
  
  log(results.postgresql ? 'green' : 'red', 
      `🐘 PostgreSQL: ${results.postgresql ? '✅ 正常' : '❌ 失败'}`);
  
  log(results.ttsDatabase ? 'green' : 'yellow', 
      `🎤 TTS数据库: ${results.ttsDatabase ? '✅ 已配置' : '⚠️  需要初始化'}`);
  
  // 给出建议
  log('blue', '\n💡 建议:');
  
  if (!results.redis) {
    log('yellow', '1. 启动Redis服务: 双击 redis-server.exe');
  }
  
  if (!results.postgresql) {
    log('yellow', '2. 启动PostgreSQL服务: pg_ctl -D ../data start');
  }
  
  if (results.postgresql && !results.ttsDatabase) {
    log('yellow', '3. 初始化TTS数据库: npm run migrate:create');
  }
  
  if (results.redis && results.postgresql && results.ttsDatabase) {
    log('green', '\n🎉 所有服务正常！可以启动TTS应用了');
    log('green', '   启动命令: npm run dev');
  }
  
  return results;
}

// 启动测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testRedis, testPostgreSQL, testTTSDatabase, runTests };
