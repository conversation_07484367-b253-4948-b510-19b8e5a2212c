#!/usr/bin/env node

/**
 * 验证代码修改是否正确
 */

const fs = require('fs');
const path = require('path');

// 检查修改是否正确
function verifyChanges() {
  console.log('🔍 验证代码修改...\n');
  
  // 读取修改后的文件
  const ttsUtilsPath = path.join(__dirname, 'src/utils/ttsUtils.js');
  const content = fs.readFileSync(ttsUtilsPath, 'utf8');
  
  // 检查关键修改点
  const checks = [
    {
      name: '使用 allow_unauthenticated=1 参数',
      test: content.includes('allow_unauthenticated=1'),
      expected: true
    },
    {
      name: '移除 xi-api-key 头部',
      test: content.includes('xi-api-key'),
      expected: false
    },
    {
      name: '移除 Accept 头部',
      test: content.includes('Accept'),
      expected: false
    },
    {
      name: '只使用 Content-Type 头部',
      test: content.includes("headers = { 'Content-Type': 'application/json' }"),
      expected: true
    },
    {
      name: '包含模型特定的 voice_settings 逻辑',
      test: content.includes("if (modelId === 'eleven_v3')"),
      expected: true
    },
    {
      name: '使用参考代码的默认值 (stability: 0.58)',
      test: content.includes('stability || 0.58'),
      expected: true
    },
    {
      name: '包含 use_speaker_boost 参数',
      test: content.includes('use_speaker_boost: true'),
      expected: true
    }
  ];
  
  let allPassed = true;
  
  checks.forEach((check, index) => {
    const passed = check.test === check.expected;
    const status = passed ? '✅' : '❌';
    console.log(`${index + 1}. ${status} ${check.name}`);
    
    if (!passed) {
      allPassed = false;
      console.log(`   预期: ${check.expected}, 实际: ${check.test}`);
    }
  });
  
  console.log('\n📋 修改总结:');
  if (allPassed) {
    console.log('✅ 所有修改都已正确应用！');
    console.log('\n🎯 关键变化:');
    console.log('   • URL: https://api.elevenlabs.io/v1/text-to-speech/${voiceId}?allow_unauthenticated=1');
    console.log('   • 认证: 无需API Key');
    console.log('   • 头部: 只使用 Content-Type: application/json');
    console.log('   • voice_settings: 根据模型类型动态构建');
    console.log('   • 默认值: 使用参考代码的默认值');
  } else {
    console.log('❌ 部分修改未正确应用，请检查代码。');
  }
  
  return allPassed;
}

// 检查环境变量配置文件
function verifyConfigFiles() {
  console.log('\n🔧 验证配置文件...\n');
  
  const configChecks = [
    {
      file: '.env.example',
      name: '.env.example 文件',
      test: () => {
        const content = fs.readFileSync('.env.example', 'utf8');
        return content.includes('# 已不再需要') && content.includes('allow_unauthenticated=1');
      }
    },
    {
      file: 'README.md',
      name: 'README.md 文件',
      test: () => {
        const content = fs.readFileSync('README.md', 'utf8');
        return content.includes('现在使用免费接口') && content.includes('# 已不再需要');
      }
    }
  ];
  
  let allConfigPassed = true;
  
  configChecks.forEach((check, index) => {
    try {
      const passed = check.test();
      const status = passed ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${check.name}`);
      
      if (!passed) {
        allConfigPassed = false;
      }
    } catch (error) {
      console.log(`${index + 1}. ❌ ${check.name} (文件读取错误)`);
      allConfigPassed = false;
    }
  });
  
  return allConfigPassed;
}

// 主函数
function main() {
  console.log('🚀 开始验证代码修改...\n');
  
  const codeChanges = verifyChanges();
  const configChanges = verifyConfigFiles();
  
  console.log('\n📊 验证结果:');
  if (codeChanges && configChanges) {
    console.log('🎉 所有修改验证通过！');
    console.log('\n📝 下一步:');
    console.log('   1. 测试API调用是否正常工作');
    console.log('   2. 验证免费配额是否足够使用');
    console.log('   3. 检查音频生成质量');
    console.log('   4. 更新部署文档');
  } else {
    console.log('⚠️  部分验证失败，请检查修改。');
  }
}

if (require.main === module) {
  main();
}

module.exports = { verifyChanges, verifyConfigFiles };
