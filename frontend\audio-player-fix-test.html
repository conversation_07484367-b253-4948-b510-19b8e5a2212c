<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频播放器竞态条件修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .test-case {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .audio-player {
            margin: 10px 0;
            padding: 15px;
            border: 2px solid #007bff;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-playing { background-color: #28a745; }
        .status-paused { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }
        .log-container {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
        }
        .log-info { color: #007bff; }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
    </style>
</head>
<body>
    <h1>🎵 音频播放器竞态条件修复测试</h1>

    <div class="test-container">
        <h2 class="test-title">测试说明</h2>
        <p>此测试用于验证音频播放器竞态条件修复的有效性。测试将模拟以下场景：</p>
        <ul>
            <li><strong>正常播放</strong>：音频加载完成后正常播放</li>
            <li><strong>快速播放</strong>：音频未完全加载时尝试播放（模拟竞态条件）</li>
            <li><strong>网络延迟</strong>：模拟慢速网络环境</li>
            <li><strong>播放失败</strong>：模拟音频播放失败的情况</li>
        </ul>
    </div>

    <div class="test-container">
        <h2 class="test-title">测试用例</h2>

        <!-- 测试用例1：正常播放 -->
        <div class="test-case">
            <h3>测试1：正常播放（修复后的逻辑）</h3>
            <p>使用 async/await 确保播放状态与实际一致</p>
            <div class="audio-player">
                <div>
                    <span class="status-indicator" id="status1"></span>
                    <span id="statusText1">未开始</span>
                </div>
                <button onclick="testNormalPlay()">开始测试</button>
                <button onclick="togglePlay1()" id="playBtn1" disabled>播放/暂停</button>
                <div>时间: <span id="time1">00:00 / 00:00</span></div>
                <audio id="audio1" style="display: none;"></audio>
            </div>
            <div class="test-result" id="result1"></div>
            <div class="log-container" id="log1"></div>
        </div>

        <!-- 测试用例2：旧版本逻辑（有问题的版本） -->
        <div class="test-case">
            <h3>测试2：旧版本逻辑（存在竞态条件）</h3>
            <p>不使用 async/await，立即设置播放状态</p>
            <div class="audio-player">
                <div>
                    <span class="status-indicator" id="status2"></span>
                    <span id="statusText2">未开始</span>
                </div>
                <button onclick="testOldLogic()">开始测试</button>
                <button onclick="togglePlay2()" id="playBtn2" disabled>播放/暂停</button>
                <div>时间: <span id="time2">00:00 / 00:00</span></div>
                <audio id="audio2" style="display: none;"></audio>
            </div>
            <div class="test-result" id="result2"></div>
            <div class="log-container" id="log2"></div>
        </div>
    </div>

    <script>
        // 测试音频URL（使用公共的测试音频）
        const TEST_AUDIO_URL = 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';

        // 全局状态管理
        const playerStates = {
            1: { isPlaying: false, audio: null },
            2: { isPlaying: false, audio: null }
        };

        // 日志记录函数
        function log(testId, message, type = 'info') {
            const logContainer = document.getElementById(`log${testId}`);
            // 使用固定格式避免本地化差异
            const now = new Date();
            const timestamp = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 更新状态显示
        function updateStatus(testId, status, text) {
            const statusEl = document.getElementById(`status${testId}`);
            const textEl = document.getElementById(`statusText${testId}`);

            statusEl.className = `status-indicator status-${status}`;
            textEl.textContent = text;
        }

        // 更新测试结果
        function updateResult(testId, message, type) {
            const resultEl = document.getElementById(`result${testId}`);
            resultEl.className = `test-result ${type}`;
            resultEl.textContent = message;
        }

        // 测试1：正常播放（修复后的逻辑）
        async function testNormalPlay() {
            const testId = 1;
            const audio = document.getElementById(`audio${testId}`);

            log(testId, '开始测试正常播放（修复后逻辑）');
            updateStatus(testId, 'paused', '准备中...');

            try {
                // 设置音频源
                audio.src = TEST_AUDIO_URL;
                log(testId, '设置音频源');

                // 等待元数据加载
                await new Promise((resolve, reject) => {
                    audio.addEventListener('loadedmetadata', resolve, { once: true });
                    audio.addEventListener('error', reject, { once: true });
                });

                log(testId, '音频元数据加载完成');

                // 修复后的自动播放逻辑
                try {
                    log(testId, '尝试自动播放...');
                    await audio.play();
                    playerStates[testId].isPlaying = true;
                    updateStatus(testId, 'playing', '播放中');
                    log(testId, '✅ 自动播放成功', 'success');
                    updateResult(testId, '✅ 测试通过：播放状态与实际一致', 'success');
                } catch (error) {
                    log(testId, `❌ 自动播放失败: ${error.message}`, 'error');
                    playerStates[testId].isPlaying = false;
                    updateStatus(testId, 'error', '播放失败');
                    updateResult(testId, '✅ 测试通过：正确处理播放失败', 'success');
                }

                document.getElementById(`playBtn${testId}`).disabled = false;

            } catch (error) {
                log(testId, `❌ 测试失败: ${error.message}`, 'error');
                updateStatus(testId, 'error', '加载失败');
                updateResult(testId, '❌ 测试失败：音频加载错误', 'error');
            }
        }

        // 测试2：旧版本逻辑（存在竞态条件）
        async function testOldLogic() {
            const testId = 2;
            const audio = document.getElementById(`audio${testId}`);

            log(testId, '开始测试旧版本逻辑（存在竞态条件）');
            updateStatus(testId, 'paused', '准备中...');

            try {
                // 设置音频源
                audio.src = TEST_AUDIO_URL;
                log(testId, '设置音频源');

                // 等待元数据加载
                await new Promise((resolve, reject) => {
                    audio.addEventListener('loadedmetadata', resolve, { once: true });
                    audio.addEventListener('error', reject, { once: true });
                });

                log(testId, '音频元数据加载完成');

                // 旧版本的有问题的逻辑
                log(testId, '使用旧版本逻辑尝试播放...');
                audio.play(); // 不等待Promise
                playerStates[testId].isPlaying = true; // 立即设置状态
                updateStatus(testId, 'playing', '播放中');
                log(testId, '⚠️ 立即设置播放状态（可能不准确）', 'warning');

                // 检查实际播放状态
                setTimeout(() => {
                    if (audio.paused && playerStates[testId].isPlaying) {
                        log(testId, '❌ 检测到竞态条件：UI显示播放中，但实际未播放', 'error');
                        updateResult(testId, '❌ 检测到竞态条件：状态不一致', 'error');
                    } else {
                        log(testId, '✅ 这次运气好，没有出现竞态条件', 'success');
                        updateResult(testId, '⚠️ 这次没有出现问题，但风险依然存在', 'warning');
                    }
                }, 100);

                document.getElementById(`playBtn${testId}`).disabled = false;

            } catch (error) {
                log(testId, `❌ 测试失败: ${error.message}`, 'error');
                updateStatus(testId, 'error', '加载失败');
                updateResult(testId, '❌ 测试失败：音频加载错误', 'error');
            }
        }

        // 播放/暂停切换函数
        async function togglePlay1() { await togglePlayGeneric(1); }
        async function togglePlay2() { await togglePlayGeneric(2); }

        async function togglePlayGeneric(testId) {
            const audio = document.getElementById(`audio${testId}`);
            const state = playerStates[testId];

            if (state.isPlaying) {
                audio.pause();
                state.isPlaying = false;
                updateStatus(testId, 'paused', '已暂停');
                log(testId, '手动暂停');
            } else {
                try {
                    await audio.play();
                    state.isPlaying = true;
                    updateStatus(testId, 'playing', '播放中');
                    log(testId, '手动播放成功', 'success');
                } catch (error) {
                    log(testId, `手动播放失败: ${error.message}`, 'error');
                    state.isPlaying = false;
                    updateStatus(testId, 'error', '播放失败');
                }
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('音频播放器竞态条件修复测试页面已加载');

            // 为所有音频元素添加事件监听器
            [1, 2].forEach(testId => {
                const audio = document.getElementById(`audio${testId}`);

                audio.addEventListener('ended', () => {
                    playerStates[testId].isPlaying = false;
                    updateStatus(testId, 'paused', '播放结束');
                    log(testId, '播放结束');
                });

                audio.addEventListener('timeupdate', () => {
                    if (audio.duration) {
                        const current = Math.floor(audio.currentTime);
                        const total = Math.floor(audio.duration);
                        const currentMin = Math.floor(current / 60);
                        const currentSec = current % 60;
                        const totalMin = Math.floor(total / 60);
                        const totalSec = total % 60;

                        document.getElementById(`time${testId}`).textContent =
                            `${currentMin.toString().padStart(2, '0')}:${currentSec.toString().padStart(2, '0')} / ${totalMin.toString().padStart(2, '0')}:${totalSec.toString().padStart(2, '0')}`;
                    }
                });
            });
        });
    </script>
</body>
</html>
