const http = require('http');

console.log('🧪 测试TTS API...');

// 测试健康检查端点
const req = http.get('http://localhost:3000/health', (res) => {
  console.log(`✅ 健康检查状态码: ${res.statusCode}`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('📄 响应长度:', data.length, '字符');
    if (data.includes('TTS API Server')) {
      console.log('🎉 健康检查成功！');
    }
    
    // 测试API信息端点
    testApiInfo();
  });
});

req.on('error', (err) => {
  console.error('❌ 连接失败:', err.message);
  console.log('请确保服务正在运行: npm run dev');
});

function testApiInfo() {
  const req2 = http.get('http://localhost:3000/api', (res) => {
    console.log(`✅ API信息状态码: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      try {
        const json = JSON.parse(data);
        console.log('📋 API名称:', json.name);
        console.log('🔢 版本:', json.version);
        console.log('🎉 API信息获取成功！');
        
        console.log('\n📊 测试总结:');
        console.log('✅ Express服务器: 正常运行');
        console.log('✅ 基础路由: 工作正常');
        console.log('✅ JSON响应: 格式正确');
        console.log('⚠️  Redis: 未连接 (需要安装)');
        console.log('⚠️  PostgreSQL: 未连接 (需要安装)');
        
        console.log('\n🚀 下一步:');
        console.log('1. 安装PostgreSQL和Redis');
        console.log('2. 运行: npm run migrate:create');
        console.log('3. 测试完整功能');
        
      } catch (e) {
        console.log('📄 响应数据:', data);
      }
    });
  });
  
  req2.on('error', (err) => {
    console.error('❌ API信息请求失败:', err.message);
  });
}
