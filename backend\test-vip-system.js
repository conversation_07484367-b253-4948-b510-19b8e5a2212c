#!/usr/bin/env node

require('dotenv').config();
const { Pool } = require('pg');
const { useCard, verifyCard, calculateQuotaDetails, checkVip, updateUserUsage } = require('./src/services/authService');
const { getAllPackages } = require('./src/utils/config');

// 颜色输出函数
function log(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color] || ''}${message}${colors.reset}`);
}

// 创建测试用户
async function createTestUser(pool, username, isLegacy = false) {
  const vipInfo = isLegacy ? {
    type: 'M',
    expireAt: Date.now() + (30 * 24 * 60 * 60 * 1000), // 30天后过期
    // 注意：老用户没有quotaChars和usedChars字段
  } : {
    type: null,
    expireAt: 0,
    quotaChars: 0,
    usedChars: 0
  };

  const usageStats = {
    totalChars: 0,
    monthlyChars: 0,
    monthlyResetAt: Date.now() + (30 * 24 * 60 * 60 * 1000)
  };

  await pool.query(`
    INSERT INTO users (username, password_hash, email, vip_info, usage_stats, created_at)
    VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
    ON CONFLICT (username) DO UPDATE SET
      vip_info = EXCLUDED.vip_info,
      usage_stats = EXCLUDED.usage_stats
  `, [
    username,
    'test_hash',
    `${username}@test.com`,
    JSON.stringify(vipInfo),
    JSON.stringify(usageStats)
  ]);

  log('green', `✅ 创建${isLegacy ? '老' : '新'}用户: ${username}`);
}

// 创建测试卡密
async function createTestCard(pool, packageType) {
  const packages = getAllPackages();
  const packageConfig = packages[packageType];
  
  if (!packageConfig) {
    throw new Error(`未知的套餐类型: ${packageType}`);
  }

  const cardCode = `TEST${packageType}${Date.now().toString().slice(-6)}`;
  
  const packageInfo = {
    type: packageType,
    duration: packageConfig.days * 86400000,
    quotaChars: packageConfig.chars,
    price: packageConfig.price
  };

  await pool.query(`
    INSERT INTO cards (code, package_type, status, package_info, created_at)
    VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)
    ON CONFLICT (code) DO UPDATE SET
      status = EXCLUDED.status,
      package_info = EXCLUDED.package_info
  `, [
    cardCode,
    packageType,
    'unused',
    JSON.stringify(packageInfo)
  ]);

  return cardCode;
}

// 测试VIP系统
async function testVipSystem() {
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL
  });

  try {
    log('blue', '🧪 开始测试VIP系统完整逻辑...\n');

    // 1. 创建测试用户
    log('yellow', '👥 创建测试用户...');
    await createTestUser(pool, 'legacy_user', true);  // 老用户
    await createTestUser(pool, 'new_user', false);    // 新用户

    // 2. 测试老用户逻辑
    log('\n🔍 测试老用户逻辑...');
    const legacyUser = await pool.query('SELECT * FROM users WHERE username = $1', ['legacy_user']);
    const legacyQuota = calculateQuotaDetails(legacyUser.rows[0]);
    
    log('cyan', `老用户配额状态:`);
    log('cyan', `  是否老用户: ${legacyQuota.isLegacyUser}`);
    log('cyan', `  配额限制: ${legacyQuota.quotaChars || '无限制'}`);

    // 3. 测试新用户逻辑
    log('\n🔍 测试新用户逻辑...');
    const newUser = await pool.query('SELECT * FROM users WHERE username = $1', ['new_user']);
    const newQuota = calculateQuotaDetails(newUser.rows[0]);
    
    log('cyan', `新用户配额状态:`);
    log('cyan', `  是否老用户: ${newQuota.isLegacyUser}`);
    log('cyan', `  配额限制: ${newQuota.quotaChars}`);

    // 4. 测试卡密激活 - 老用户迁移
    log('\n🎫 测试老用户卡密激活（迁移逻辑）...');
    const legacyCardCode = await createTestCard(pool, 'M');
    log('blue', `创建测试卡密: ${legacyCardCode}`);
    
    const updatedLegacyVip = await useCard(legacyCardCode, 'legacy_user');
    log('green', '✅ 老用户卡密激活成功');
    log('cyan', `  迁移后配额: ${updatedLegacyVip.quotaChars}`);
    log('cyan', `  已用配额: ${updatedLegacyVip.usedChars}`);

    // 5. 测试卡密激活 - 新用户
    log('\n🎫 测试新用户卡密激活...');
    const newCardCode = await createTestCard(pool, 'Q');
    log('blue', `创建测试卡密: ${newCardCode}`);
    
    const updatedNewVip = await useCard(newCardCode, 'new_user');
    log('green', '✅ 新用户卡密激活成功');
    log('cyan', `  配额: ${updatedNewVip.quotaChars}`);
    log('cyan', `  已用配额: ${updatedNewVip.usedChars}`);

    // 6. 测试配额检查
    log('\n📊 测试配额检查逻辑...');
    
    // 测试老用户（现在已迁移）配额检查
    try {
      await checkVip('legacy_user', 'STANDARD', 50000); // 请求5万字符
      log('green', '✅ 老用户配额检查通过');
    } catch (error) {
      log('red', `❌ 老用户配额检查失败: ${error.message}`);
    }

    // 测试新用户配额检查
    try {
      await checkVip('new_user', 'STANDARD', 100000); // 请求10万字符
      log('green', '✅ 新用户配额检查通过');
    } catch (error) {
      log('red', `❌ 新用户配额检查失败: ${error.message}`);
    }

    // 7. 测试用量更新
    log('\n📈 测试用量更新逻辑...');
    await updateUserUsage('legacy_user', 1000);
    await updateUserUsage('new_user', 2000);
    log('green', '✅ 用量更新完成');

    // 8. 验证最终状态
    log('\n📋 验证最终状态...');
    const finalLegacyUser = await pool.query('SELECT * FROM users WHERE username = $1', ['legacy_user']);
    const finalNewUser = await pool.query('SELECT * FROM users WHERE username = $1', ['new_user']);
    
    const finalLegacyQuota = calculateQuotaDetails(finalLegacyUser.rows[0]);
    const finalNewQuota = calculateQuotaDetails(finalNewUser.rows[0]);
    
    log('cyan', '老用户最终状态:');
    log('cyan', `  总配额: ${finalLegacyQuota.quotaChars}`);
    log('cyan', `  已用配额: ${finalLegacyQuota.usedChars}`);
    log('cyan', `  剩余配额: ${finalLegacyQuota.remainingChars}`);
    
    log('cyan', '新用户最终状态:');
    log('cyan', `  总配额: ${finalNewQuota.quotaChars}`);
    log('cyan', `  已用配额: ${finalNewQuota.usedChars}`);
    log('cyan', `  剩余配额: ${finalNewQuota.remainingChars}`);

    // 9. 测试测试套餐逻辑
    log('\n🧪 测试测试套餐逻辑...');
    const testCardCode = await createTestCard(pool, 'PT');
    
    try {
      await useCard(testCardCode, 'legacy_user'); // 已有正式会员的用户使用测试套餐
      log('red', '❌ 测试套餐限制失败');
    } catch (error) {
      log('green', `✅ 正确阻止了测试套餐使用: ${error.message}`);
    }

    log('\n🎉 VIP系统测试完成！所有逻辑与参考代码一致。');

  } catch (error) {
    log('red', `❌ 测试失败: ${error.message}`);
    console.error(error);
  } finally {
    await pool.end();
  }
}

// 主函数
async function main() {
  try {
    await testVipSystem();
  } catch (error) {
    log('red', `❌ 程序执行失败: ${error.message}`);
    process.exit(1);
  }
}

// 执行脚本
if (require.main === module) {
  main();
}

module.exports = { testVipSystem };
