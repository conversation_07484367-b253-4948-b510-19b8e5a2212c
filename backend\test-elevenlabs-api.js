#!/usr/bin/env node

/**
 * ElevenLabs API 测试脚本
 * 用于验证修改后的 generateSpeech 函数是否正常工作
 */

const { generateSpeech } = require('./src/utils/ttsUtils');

// 测试配置
const TEST_CONFIG = {
  text: '这是一个测试文本，用于验证ElevenLabs API的免费接口是否正常工作。',
  voiceId: 'pNInz6obpgDQGcFmaJgB', // Adam voice ID
  modelId: 'eleven_turbo_v2',
  stability: 0.5,
  similarity_boost: 0.75,
  style: 0.5,
  speed: 1.0
};

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 测试函数
async function testElevenLabsAPI() {
  log('blue', '🚀 开始测试ElevenLabs免费API...\n');
  
  try {
    log('blue', '📝 测试配置:');
    console.log(`   文本: ${TEST_CONFIG.text.substring(0, 50)}...`);
    console.log(`   语音ID: ${TEST_CONFIG.voiceId}`);
    console.log(`   模型: ${TEST_CONFIG.modelId}`);
    console.log(`   稳定性: ${TEST_CONFIG.stability}`);
    console.log(`   相似度增强: ${TEST_CONFIG.similarity_boost}`);
    console.log(`   风格: ${TEST_CONFIG.style}`);
    console.log(`   语速: ${TEST_CONFIG.speed}\n`);
    
    log('yellow', '⏳ 正在调用ElevenLabs API...');
    
    const startTime = Date.now();
    const audioBuffer = await generateSpeech(
      TEST_CONFIG.text,
      TEST_CONFIG.voiceId,
      TEST_CONFIG.modelId,
      TEST_CONFIG.stability,
      TEST_CONFIG.similarity_boost,
      TEST_CONFIG.style,
      TEST_CONFIG.speed
    );
    const endTime = Date.now();
    
    log('green', '✅ API调用成功！');
    console.log(`   响应时间: ${endTime - startTime}ms`);
    console.log(`   音频大小: ${audioBuffer.byteLength} bytes`);
    console.log(`   音频大小: ${(audioBuffer.byteLength / 1024).toFixed(2)} KB`);
    console.log(`   字符/字节比: ${(audioBuffer.byteLength / TEST_CONFIG.text.length).toFixed(2)} bytes/char`);
    
    // 验证音频数据
    if (audioBuffer.byteLength > 0) {
      log('green', '✅ 音频数据验证通过');
      
      // 可选：保存音频文件用于测试
      const fs = require('fs');
      const testAudioPath = './test-audio.mp3';
      fs.writeFileSync(testAudioPath, Buffer.from(audioBuffer));
      log('blue', `💾 测试音频已保存到: ${testAudioPath}`);
      
    } else {
      log('red', '❌ 音频数据为空');
      return false;
    }
    
    log('green', '\n🎉 ElevenLabs免费API测试成功！');
    log('blue', '📋 测试结果:');
    console.log('   ✅ URL格式正确 (使用 allow_unauthenticated=1)');
    console.log('   ✅ 无需API Key认证');
    console.log('   ✅ voice_settings参数正确');
    console.log('   ✅ 音频生成成功');
    
    return true;
    
  } catch (error) {
    log('red', '❌ API调用失败！');
    console.error(`   错误信息: ${error.message}`);
    
    // 分析错误类型
    if (error.message.includes('401')) {
      log('yellow', '💡 可能的原因: 免费配额已用完或API参数错误');
    } else if (error.message.includes('403')) {
      log('yellow', '💡 可能的原因: 内容违规或地区限制');
    } else if (error.message.includes('429')) {
      log('yellow', '💡 可能的原因: 请求频率过高，请稍后重试');
    } else if (error.message.includes('fetch')) {
      log('yellow', '💡 可能的原因: 网络连接问题');
    }
    
    return false;
  }
}

// 主函数
async function main() {
  const success = await testElevenLabsAPI();
  
  if (success) {
    log('green', '\n🎊 所有测试通过！修改后的代码工作正常。');
  } else {
    log('red', '\n💥 测试失败，请检查代码修改或网络连接。');
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testElevenLabsAPI };
