// API配置和服务层
// 兼容 Cloudflare Pages 部署

// API配置
const API_CONFIG = {
  // 生产环境使用你的 Cloudflare Worker 域名
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'https://tts-api.panxuchao19951206.workers.dev',
  // 备用API配置 - 支持多个备用API（逗号分隔）
  BACKUP_URL: process.env.NEXT_PUBLIC_BACKUP_API_URL || null,
  BACKUP_URLS: process.env.NEXT_PUBLIC_BACKUP_API_URL
    ? process.env.NEXT_PUBLIC_BACKUP_API_URL.split(',').map(url => url.trim()).filter(url => url.length > 0)
    : [],
  ENABLE_BACKUP: process.env.NEXT_PUBLIC_ENABLE_BACKUP_API === 'true',
  TIMEOUT: 30000, // 30秒超时
}

// API端点定义
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/api/auth/login',
    REGISTER: '/api/auth/register',
    REFRESH: '/api/auth/refresh',
    SEND_VERIFICATION: '/api/auth/send-verification',
    VERIFY_EMAIL: '/api/auth/verify-email',
    CHANGE_PASSWORD: '/api/auth/change-password',
    FORGOT_PASSWORD: '/api/auth/forgot-password',
    RESET_PASSWORD: '/api/auth/reset-password',
  },
  // TTS相关
  TTS: {
    GENERATE: '/api/tts/generate',
    STATUS: '/api/tts/status',
    STREAM: '/api/tts/stream',     // 新增：音频流媒体播放
    DOWNLOAD: '/api/tts/download', // 修改：专用于文件下载
  },
  // 用户相关
  USER: {
    QUOTA: '/api/user/quota',
  },
  // 卡密相关
  CARD: {
    USE: '/api/card/use',
  },
  // 自动标注相关
  AUTO_TAG: {
    PROCESS: '/api/auto-tag/process',
    STATUS: '/api/auto-tag/status',
    ADMIN_STATS: '/api/auto-tag/admin/stats',
  },
}

// 请求类型定义
export interface ApiResponse<T = any> {
  success?: boolean
  data?: T
  error?: string
  message?: string
}

export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  access_token: string
  refresh_token: string
  expires_in: number
}

export interface RegisterRequest {
  username: string
  password: string
}

export interface SendVerificationRequest {
  email: string
  username: string
  password: string
}

export interface SendVerificationResponse {
  message: string
  email: string
}

export interface VerifyEmailRequest {
  username: string
  email: string
  code: string
}

export interface VerifyEmailResponse {
  message: string
  access_token: string
  refresh_token: string
  expires_in: number
}

export interface UserQuotaResponse {
  // 原有字段（保持向后兼容）
  isVip: boolean
  expireAt: number
  type?: string
  remainingTime?: string | null

  // 新增配额相关字段
  quotaChars?: number      // 总配额（老用户为undefined）
  usedChars?: number       // 已用配额（老用户为undefined）
  remainingChars?: number  // 剩余配额（老用户为undefined）
  usagePercentage: number  // 使用百分比
  isLegacyUser: boolean    // 是否为老用户
}

export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
}

export interface ChangePasswordResponse {
  message: string
}

export interface ForgotPasswordRequest {
  email: string
}

export interface ForgotPasswordResponse {
  message: string
  email: string
}

export interface ResetPasswordRequest {
  email: string
  code: string
  newPassword: string
}

export interface ResetPasswordResponse {
  message: string
}

// TTS异步任务相关类型
export interface TTSTaskResponse {
  taskId: string
  status: 'processing' | 'complete' | 'failed'
  message?: string
}

export interface TTSStatusResponse {
  taskId: string
  status: 'processing' | 'complete' | 'failed'
  createdAt?: number
  completedAt?: number
  audioUrl?: string
  audioSize?: number
  chunksProcessed?: number
  totalChunks?: number
  error?: string
}

export interface TTSGenerateRequest {
  input: string
  voice: string
  stability?: number
  similarity_boost?: number
  style?: number
  speed?: number
}

// Token管理
export class TokenManager {
  private static readonly ACCESS_TOKEN_KEY = 'access_token'
  private static readonly REFRESH_TOKEN_KEY = 'refresh_token'
  private static readonly USER_EMAIL_KEY = 'userEmail'

  static getAccessToken(): string | null {
    if (typeof window === 'undefined') return null
    return localStorage.getItem(this.ACCESS_TOKEN_KEY)
  }

  static getRefreshToken(): string | null {
    if (typeof window === 'undefined') return null
    return localStorage.getItem(this.REFRESH_TOKEN_KEY)
  }

  static getUserEmail(): string | null {
    if (typeof window === 'undefined') return null
    return localStorage.getItem(this.USER_EMAIL_KEY)
  }

  static setTokens(accessToken: string, refreshToken: string, email?: string): void {
    if (typeof window === 'undefined') return
    localStorage.setItem(this.ACCESS_TOKEN_KEY, accessToken)
    localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken)
    if (email) {
      localStorage.setItem(this.USER_EMAIL_KEY, email)
    }
    // 保持兼容性
    localStorage.setItem('isLoggedIn', 'true')
  }

  static clearTokens(): void {
    if (typeof window === 'undefined') return
    localStorage.removeItem(this.ACCESS_TOKEN_KEY)
    localStorage.removeItem(this.REFRESH_TOKEN_KEY)
    localStorage.removeItem(this.USER_EMAIL_KEY)
    // 保持兼容性
    localStorage.removeItem('isLoggedIn')
  }

  static isLoggedIn(): boolean {
    return !!this.getAccessToken()
  }
}

// HTTP客户端类
export class ApiClient {
  private baseURL: string
  private backupURL: string | null  // 保持向后兼容
  private backupURLs: string[]      // 新增：多个备用API支持
  private enableBackup: boolean
  private timeout: number

  constructor() {
    this.baseURL = API_CONFIG.BASE_URL
    this.backupURL = API_CONFIG.BACKUP_URL
    this.backupURLs = API_CONFIG.BACKUP_URLS
    this.enableBackup = API_CONFIG.ENABLE_BACKUP
    this.timeout = API_CONFIG.TIMEOUT
  }

  // 获取当前可用的API URL（主要用于外部访问）
  getCurrentApiUrl(useBackup: boolean = false, backupIndex: number = 0): string {
    if (useBackup && this.enableBackup) {
      // 优先使用多个备用API配置
      if (this.backupURLs.length > 0 && backupIndex >= 0 && backupIndex < this.backupURLs.length) {
        return this.backupURLs[backupIndex]
      }
      // 向后兼容：使用单个备用API配置
      if (this.backupURL) {
        return this.backupURL
      }
    }
    return this.baseURL
  }

  // 检查备用API是否可用
  isBackupApiAvailable(): boolean {
    return this.enableBackup && (this.backupURLs.length > 0 || !!this.backupURL)
  }

  // 获取备用API数量
  getBackupApiCount(): number {
    if (!this.enableBackup) return 0
    return this.backupURLs.length > 0 ? this.backupURLs.length : (this.backupURL ? 1 : 0)
  }

  // 获取指定索引的备用API URL
  getBackupApiUrl(index: number): string | null {
    if (!this.enableBackup) return null

    // 优先使用多个备用API配置
    if (this.backupURLs.length > 0) {
      return (index >= 0 && index < this.backupURLs.length) ? this.backupURLs[index] : null
    }

    // 向后兼容：使用单个备用API配置
    return (index === 0 && this.backupURL) ? this.backupURL : null
  }

  // 创建请求头
  private createHeaders(includeAuth: boolean = false): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    }

    if (includeAuth) {
      const token = TokenManager.getAccessToken()
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }
    }

    return headers
  }

  // 处理响应
  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`
      let errorCode: string | null = null

      try {
        const errorData = await response.json()
        errorMessage = errorData.error || errorData.message || errorMessage
        errorCode = errorData.code || null // 【新增】提取错误码
      } catch {
        // 如果无法解析JSON，使用默认错误消息
      }

      // 【新增】创建带有错误码的自定义错误对象
      const error = new Error(errorMessage)
      if (errorCode) {
        (error as any).code = errorCode
      }

      throw error
    }

    return await response.json()
  }

  // 通用请求方法
  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
    includeAuth: boolean = false
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    const headers = this.createHeaders(includeAuth)

    const config: RequestInit = {
      ...options,
      headers: {
        ...headers,
        ...options.headers,
      },
    }

    // 创建AbortController用于超时控制
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), this.timeout)

    try {
      const response = await fetch(url, {
        ...config,
        signal: controller.signal,
      })

      clearTimeout(timeoutId)
      return await this.handleResponse<T>(response)
    } catch (error) {
      clearTimeout(timeoutId)

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('请求超时，请检查网络连接')
        }
        throw error
      }

      throw new Error('网络请求失败')
    }
  }

  // GET请求
  async get<T>(endpoint: string, includeAuth: boolean = false): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' }, includeAuth)
  }

  // POST请求
  async post<T>(
    endpoint: string,
    data?: any,
    includeAuth: boolean = false
  ): Promise<T> {
    return this.request<T>(
      endpoint,
      {
        method: 'POST',
        body: data ? JSON.stringify(data) : undefined,
      },
      includeAuth
    )
  }

  // PUT请求
  async put<T>(
    endpoint: string,
    data?: any,
    includeAuth: boolean = false
  ): Promise<T> {
    return this.request<T>(
      endpoint,
      {
        method: 'PUT',
        body: data ? JSON.stringify(data) : undefined,
      },
      includeAuth
    )
  }

  // DELETE请求
  async delete<T>(endpoint: string, includeAuth: boolean = false): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' }, includeAuth)
  }
}

// 创建全局API客户端实例
export const apiClient = new ApiClient()
