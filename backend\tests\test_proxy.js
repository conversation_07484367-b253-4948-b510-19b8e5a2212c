const axios = require('axios');
const { SocksProxyAgent } = require('socks-proxy-agent');

// 您想测试的 sing-box worker 端口
const PROXY_PORT = 1081; 
const proxyAgent = new SocksProxyAgent(`socks5h://127.0.0.1:${PROXY_PORT}`);

// 一个可以查询您出口 IP 的 API
const IP_CHECK_API = 'https://api.ipify.org?format=json';

async function runTest() {
    console.log("--- 测试 1: 直接连接 (不通过代理) ---");
    try {
        const directResponse = await axios.get(IP_CHECK_API);
        console.log("您的真实公网 IP 是:", directResponse.data.ip);
    } catch (e) {
        console.error("直接连接失败:", e.message);
    }

    console.log(`\n--- 测试 2: 通过本地 SOCKS5 代理 (端口 ${PROXY_PORT}) ---`);
    console.log("请确保 sing-box 正在另一个终端中运行...");
    try {
        const proxyResponse = await axios.get(IP_CHECK_API, {
            httpsAgent: proxyAgent,
            httpAgent: proxyAgent, // 同时支持 http 和 https
            timeout: 20000
        });
        console.log("通过代理后的出口 IP 是:", proxyResponse.data.ip);
        console.log("\n✅ 验证成功！如果两个 IP 地址不同，说明代理工作正常。");
    } catch (e) {
        console.error(`通过代理连接失败: ${e.message}`);
        console.log("❌ 验证失败。请检查 sing-box 是否正在运行，以及端口号是否正确。");
    }
}

runTest();