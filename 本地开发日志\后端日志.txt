[INFO] [2025-07-26T01:25:23.946Z] [user:system] [task:N/A] - Single TTS WebSocket connection established {"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
Starting single TTS task 7fffda8a-06e1-4895-8466-9074f54aaf5a for user 0723
[WEBSOCKET-MANAGER] Starting TTS processing for taskId: 7fffda8a-06e1-4895-8466-9074f54aaf5a
[WEBSOCKET-MANAGER] Token available: YES
[WEBSOCKET-MANAGER] Token length: 178
[WEBSOCKET-MANAGER] Username: 0723
[INTERNAL-PROGRESS] 7fffda8a-06e1-4895-8466-9074f54aaf5a: 任务初始化...
Executed query {
  text: 'SELECT vip_info, usage_stats FROM users WHERE username = $1',
  duration: 1,
  rows: 1
}
[QUOTA-CHECK] User 0723 is under new quota rule. Checking quota...
Executed query {
  text: 'SELECT voice_id FROM voice_mappings WHERE voice_name = $1',
  duration: 3,
  rows: 0
}
[INTERNAL-PROGRESS] 7fffda8a-06e1-4895-8466-9074f54aaf5a: 文本分割中...
[INTERNAL-PROGRESS] 7fffda8a-06e1-4895-8466-9074f54aaf5a: 文本已分割为 3 个片段
[INTERNAL-PROGRESS] 7fffda8a-06e1-4895-8466-9074f54aaf5a: 正在生成 3 个音频片段...
[WORKER-POOL] Processing 3 chunks with 3 worker pool concurrency
Processing chunk 1/3, length: 957
[INFO] [2025-07-26T01:25:24.418Z] [user:system] [task:N/A] - [TTS-ROUTE] Route decision: gateway {"decision":"gateway","networkMode":"gateway","gatewayEnabled":true}
[INFO] [2025-07-26T01:25:24.421Z] [user:system] [task:N/A] - [TTS-GATEWAY] Starting gateway request {"action":"Starting gateway request","textLength":957,"voiceId":"pNInz6obpgDQGcFmaJgB","modelId":"eleven_multilingual_v2"}
[INFO] [2025-07-26T01:25:24.421Z] [user:system] [task:N/A] - [TTS-GATEWAY] Sending request via network manager {"action":"Sending request via network manager","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1","payloadSize":1113}
[INFO] [2025-07-26T01:25:24.422Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Initializing NetworkManager...
[DEBUG] [2025-07-26T01:25:24.422Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Creating network client for mode: gateway
[INFO] [2025-07-26T01:25:24.429Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Initializing ProxyGateway...
[DEBUG] [2025-07-26T01:25:24.429Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Initializing WorkerPoolController...
[INFO] [2025-07-26T01:25:24.430Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker pool initialized {"poolSize":10,"portRange":"1081-1090","selectorPrefix":"worker-selector"}
[INFO] [2025-07-26T01:25:24.430Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Initializing WorkerPoolController with Clash API...     
[DEBUG] [2025-07-26T01:25:24.430Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Loading nodes from sing-box Clash API...
Processing chunk 2/3, length: 918
[INFO] [2025-07-26T01:25:24.454Z] [user:system] [task:N/A] - [TTS-ROUTE] Route decision: gateway {"decision":"gateway","networkMode":"gateway","gatewayEnabled":true}
[INFO] [2025-07-26T01:25:24.454Z] [user:system] [task:N/A] - [TTS-GATEWAY] Starting gateway request {"action":"Starting gateway request","textLength":918,"voiceId":"pNInz6obpgDQGcFmaJgB","modelId":"eleven_multilingual_v2"}
[INFO] [2025-07-26T01:25:24.454Z] [user:system] [task:N/A] - [TTS-GATEWAY] Sending request via network manager {"action":"Sending request via network manager","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1","payloadSize":1073}
Processing chunk 3/3, length: 631
[INFO] [2025-07-26T01:25:24.455Z] [user:system] [task:N/A] - [TTS-ROUTE] Route decision: gateway {"decision":"gateway","networkMode":"gateway","gatewayEnabled":true}
[INFO] [2025-07-26T01:25:24.455Z] [user:system] [task:N/A] - [TTS-GATEWAY] Starting gateway request {"action":"Starting gateway request","textLength":631,"voiceId":"pNInz6obpgDQGcFmaJgB","modelId":"eleven_multilingual_v2"}
[INFO] [2025-07-26T01:25:24.455Z] [user:system] [task:N/A] - [TTS-GATEWAY] Sending request via network manager {"action":"Sending request via network manager","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1","payloadSize":767}
[WARN] [2025-07-26T01:25:24.467Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Skipping invalid node name: tw-tw-idx-97
[WARN] [2025-07-26T01:25:24.467Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Skipping invalid node name: sg-sg-idx-114
[WARN] [2025-07-26T01:25:24.468Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Skipping invalid node name: jp-jp-idx-118
[INFO] [2025-07-26T01:25:24.468Z] [user:system] [task:N/A] - [TTS-NODE] Loaded nodes for worker pool {"action":"Loaded nodes for worker pool","totalNodes":116,"validNodes":116,"nodeList":["sg-sg-013x-idx-0","sg-sg-023x-idx-1","sg-sg-033x-idx-2","sg-sg-043x-idx-3","sg-sg-053x-idx-4","sg-sg-063x-idx-5","sg-sg-073x-idx-6","sg-sg-083x-idx-7","sg-sg-093x-idx-8","jp-jp-013x-idx-9","...106 more"],"validNodeDetails":[{"name":"sg-sg-013x-idx-0","type":"Shadowsocks"},{"name":"sg-sg-023x-idx-1","type":"Shadowsocks"},{"name":"sg-sg-033x-idx-2","type":"Shadowsocks"},{"name":"sg-sg-043x-idx-3","type":"Trojan"},{"name":"sg-sg-053x-idx-4","type":"Trojan"},{"name":"sg-sg-063x-idx-5","type":"Trojan"},{"name":"sg-sg-073x-idx-6","type":"Trojan"},{"name":"sg-sg-083x-idx-7","type":"Trojan"},{"name":"sg-sg-093x-idx-8","type":"Trojan"},{"name":"jp-jp-013x-idx-9","type":"Shadowsocks"},{"name":"...106 more nodes","type":"truncated"}]}        
[INFO] [2025-07-26T01:25:24.468Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Loaded nodes from sing-box Clash API {"totalNodes":116,"validNodes":116,"nodeList":["sg-sg-013x-idx-0","sg-sg-023x-idx-1","sg-sg-033x-idx-2","sg-sg-043x-idx-3","sg-sg-053x-idx-4","sg-sg-063x-idx-5","sg-sg-073x-idx-6","sg-sg-083x-idx-7","sg-sg-093x-idx-8","jp-jp-013x-idx-9","...106 more"]}
[INFO] [2025-07-26T01:25:24.476Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Loaded quarantine data for 2 nodes
[DEBUG] [2025-07-26T01:25:24.476Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Validating worker selectors...
[INFO] [2025-07-26T01:25:24.492Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] All worker selectors validated successfully
[INFO] [2025-07-26T01:25:24.492Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] WorkerPoolController initialized successfully {"totalNodes":116,"healthyNodes":116,"quarantinedNodes":0,"totalWorkers":10,"apiType":"clash"}
[DEBUG] [2025-07-26T01:25:24.493Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] WorkerPoolController created and initialized (singleton)
[DEBUG] [2025-07-26T01:25:24.493Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Initializing NetworkAdapter...
[DEBUG] [2025-07-26T01:25:24.493Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] NetworkAdapter initialized
[INFO] [2025-07-26T01:25:24.494Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Starting health check with 30000ms interval
[INFO] [2025-07-26T01:25:24.494Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Starting quarantine check with 600000ms interval
[INFO] [2025-07-26T01:25:24.494Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] ProxyGateway initialized successfully {"mode":"gateway","singboxEnabled":true,"healthCheckEnabled":true}
[DEBUG] [2025-07-26T01:25:24.495Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Network client created for mode: gateway
[INFO] [2025-07-26T01:25:24.495Z] [user:system] [task:N/A] - [NETWORK-MANAGER] NetworkManager initialized successfully {"mode":"gateway","gatewayEnabled":true}
[DEBUG] [2025-07-26T01:25:24.495Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Making network request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-26T01:25:24.496Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-26T01:25:24.496Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node sg-sg-013x-idx-0 (attempt 1/114)
[DEBUG] [2025-07-26T01:25:24.505Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Making network request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-26T01:25:24.506Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-26T01:25:24.506Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node sg-sg-023x-idx-1 (attempt 1/114)
[DEBUG] [2025-07-26T01:25:24.507Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Making network request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-26T01:25:24.507Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-26T01:25:24.507Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node sg-sg-033x-idx-2 (attempt 1/114)
[DEBUG] [2025-07-26T01:25:24.631Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-033x-idx-2 {"nodeTag":"sg-sg-033x-idx-2","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-26T01:25:24.632Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node sg-sg-033x-idx-2 passed health check 
[DEBUG] [2025-07-26T01:25:24.632Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-5 to switch to node: sg-sg-033x-idx-2
[INFO] [2025-07-26T01:25:24.633Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-5","targetNode":"sg-sg-033x-idx-2","originalNodeTag":"sg-sg-033x-idx-2","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-5","requestBody":{"name":"sg-sg-033x-idx-2"}}
[DEBUG] [2025-07-26T01:25:24.637Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-5     
[INFO] [2025-07-26T01:25:24.637Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-5","newNode":"sg-sg-033x-idx-2","fixedNodeTag":"sg-sg-033x-idx-2"}
[DEBUG] [2025-07-26T01:25:24.637Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-5 successfully switched to node: sg-sg-033x-idx-2
[DEBUG] [2025-07-26T01:25:24.638Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":5,"port":1085,"selector":"worker-selector-5","assignedNode":"sg-sg-033x-idx-2"}
[INFO] [2025-07-26T01:25:24.638Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":5,"workerPort":1085,"assignedNode":"sg-sg-033x-idx-2","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1","attempt":1,"maxAttempts":2}
[INFO] [2025-07-26T01:25:24.774Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1085","workerId":5,"assignedNode":"sg-sg-033x-idx-2","method":"POST","hasProxy":true,"proxyType":"dispatcher","attempt":1}
[DEBUG] [2025-07-26T01:25:24.779Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-023x-idx-1 {"nodeTag":"sg-sg-023x-idx-1","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-26T01:25:24.779Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node sg-sg-023x-idx-1 passed health check 
[DEBUG] [2025-07-26T01:25:24.779Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-3 to switch to node: sg-sg-023x-idx-1
[INFO] [2025-07-26T01:25:24.779Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-3","targetNode":"sg-sg-023x-idx-1","originalNodeTag":"sg-sg-023x-idx-1","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-3","requestBody":{"name":"sg-sg-023x-idx-1"}}
[DEBUG] [2025-07-26T01:25:24.781Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-013x-idx-0 {"nodeTag":"sg-sg-013x-idx-0","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-26T01:25:24.781Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node sg-sg-013x-idx-0 passed health check 
[DEBUG] [2025-07-26T01:25:24.781Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-1 to switch to node: sg-sg-013x-idx-0
[INFO] [2025-07-26T01:25:24.782Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-1","targetNode":"sg-sg-013x-idx-0","originalNodeTag":"sg-sg-013x-idx-0","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-1","requestBody":{"name":"sg-sg-013x-idx-0"}}
[DEBUG] [2025-07-26T01:25:24.784Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-3
[INFO] [2025-07-26T01:25:24.785Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-3","newNode":"sg-sg-023x-idx-1","fixedNodeTag":"sg-sg-023x-idx-1"}
[DEBUG] [2025-07-26T01:25:24.785Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-3 successfully switched to node: sg-sg-023x-idx-1
[DEBUG] [2025-07-26T01:25:24.785Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":3,"port":1083,"selector":"worker-selector-3","assignedNode":"sg-sg-023x-idx-1"}
[INFO] [2025-07-26T01:25:24.785Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":3,"workerPort":1083,"assignedNode":"sg-sg-023x-idx-1","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1","attempt":1,"maxAttempts":2}
[INFO] [2025-07-26T01:25:24.786Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1083","workerId":3,"assignedNode":"sg-sg-023x-idx-1","method":"POST","hasProxy":true,"proxyType":"dispatcher","attempt":1}
[DEBUG] [2025-07-26T01:25:24.787Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-1     
[INFO] [2025-07-26T01:25:24.788Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-1","newNode":"sg-sg-013x-idx-0","fixedNodeTag":"sg-sg-013x-idx-0"}
[DEBUG] [2025-07-26T01:25:24.788Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-1 successfully switched to node: sg-sg-013x-idx-0
[DEBUG] [2025-07-26T01:25:24.788Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":1,"port":1081,"selector":"worker-selector-1","assignedNode":"sg-sg-013x-idx-0"}
[INFO] [2025-07-26T01:25:24.789Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":1,"workerPort":1081,"assignedNode":"sg-sg-013x-idx-0","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1","attempt":1,"maxAttempts":2}
[INFO] [2025-07-26T01:25:24.789Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1081","workerId":1,"assignedNode":"sg-sg-013x-idx-0","method":"POST","hasProxy":true,"proxyType":"dispatcher","attempt":1}
[DEBUG] [2025-07-26T01:25:29.501Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Starting quarantine pool check...
[INFO] [2025-07-26T01:25:29.502Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Checking 2 quarantined nodes
[DEBUG] [2025-07-26T01:25:29.502Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Checking node: sg-sg-043x-idx-3 (temporary)
[DEBUG] [2025-07-26T01:25:29.634Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-043x-idx-3 {"nodeTag":"sg-sg-043x-idx-3","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-26T01:25:29.635Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Node sg-sg-043x-idx-3 health check passed but needs more successes {"consecutiveSuccesses":1,"requiredSuccesses":2,"quarantineType":"temporary"}
[DEBUG] [2025-07-26T01:25:29.635Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Node sg-sg-043x-idx-3 health check passed but needs more successes
[DEBUG] [2025-07-26T01:25:29.635Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Checking node: sg-sg-063x-idx-5 (temporary)
[DEBUG] [2025-07-26T01:25:29.764Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-063x-idx-5 {"nodeTag":"sg-sg-063x-idx-5","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-26T01:25:29.765Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Node sg-sg-063x-idx-5 health check passed but needs more successes {"consecutiveSuccesses":1,"requiredSuccesses":2,"quarantineType":"temporary"}
[DEBUG] [2025-07-26T01:25:29.765Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Node sg-sg-063x-idx-5 health check passed but needs more successes
[INFO] [2025-07-26T01:25:29.765Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Check completed: 2 checked, 0 recovered
[INFO] [2025-07-26T01:25:32.161Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker pool response received {"action":"Worker pool response received","status":200,"ok":true,"workerId":5,"assignedNode":"sg-sg-033x-idx-2","attempt":1}
[DEBUG] [2025-07-26T01:25:32.161Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":5,"port":1085,"currentNode":"sg-sg-033x-idx-2"}
[INFO] [2025-07-26T01:25:32.162Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":5,"assignedNode":"sg-sg-033x-idx-2","attempt":1}
[WARN] [2025-07-26T01:25:32.162Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Slow network request: POST https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1 {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1","status":200,"duration":"7655ms"}
[INFO] [2025-07-26T01:25:32.162Z] [user:system] [task:N/A] - [TTS-NETWORK] POST https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB","status":200,"duration":"7707ms","responseOk":true}
[INFO] [2025-07-26T01:25:32.357Z] [user:system] [task:N/A] - [TTS-GATEWAY] Request completed successfully {"action":"Request completed successfully","audioSize":635342,"totalDuration":"7902ms","requestDuration":"7707ms"}
[INFO] [2025-07-26T01:25:32.357Z] [user:system] [task:N/A] - [TTS-SUCCESS] Generated via gateway {"mode":"gateway","audioSize":635342,"duration":"7902ms"} 
[INFO] [2025-07-26T01:25:36.608Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker pool response received {"action":"Worker pool response received","status":200,"ok":true,"workerId":3,"assignedNode":"sg-sg-023x-idx-1","attempt":1}
[DEBUG] [2025-07-26T01:25:36.608Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":3,"port":1083,"currentNode":"sg-sg-023x-idx-1"}
[INFO] [2025-07-26T01:25:36.608Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":3,"assignedNode":"sg-sg-023x-idx-1","attempt":1}
[WARN] [2025-07-26T01:25:36.608Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Slow network request: POST https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1 {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1","status":200,"duration":"12102ms"}
[INFO] [2025-07-26T01:25:36.608Z] [user:system] [task:N/A] - [TTS-NETWORK] POST https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB","status":200,"duration":"12154ms","responseOk":true}
[INFO] [2025-07-26T01:25:36.858Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker pool response received {"action":"Worker pool response received","status":200,"ok":true,"workerId":1,"assignedNode":"sg-sg-013x-idx-0","attempt":1}
[DEBUG] [2025-07-26T01:25:36.859Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":1,"port":1081,"currentNode":"sg-sg-013x-idx-0"}
[INFO] [2025-07-26T01:25:36.859Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":1,"assignedNode":"sg-sg-013x-idx-0","attempt":1}
[WARN] [2025-07-26T01:25:36.859Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Slow network request: POST https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1 {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1","status":200,"duration":"12364ms"}
[INFO] [2025-07-26T01:25:36.859Z] [user:system] [task:N/A] - [TTS-NETWORK] POST https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB","status":200,"duration":"12438ms","responseOk":true}
[INFO] [2025-07-26T01:25:37.127Z] [user:system] [task:N/A] - [TTS-GATEWAY] Request completed successfully {"action":"Request completed successfully","audioSize":1108472,"totalDuration":"12708ms","requestDuration":"12438ms"}
[INFO] [2025-07-26T01:25:37.128Z] [user:system] [task:N/A] - [TTS-SUCCESS] Generated via gateway {"mode":"gateway","audioSize":1108472,"duration":"12711ms"}
[INFO] [2025-07-26T01:25:37.357Z] [user:system] [task:N/A] - [TTS-GATEWAY] Request completed successfully {"action":"Request completed successfully","audioSize":1043271,"totalDuration":"12903ms","requestDuration":"12154ms"}
[INFO] [2025-07-26T01:25:37.358Z] [user:system] [task:N/A] - [TTS-SUCCESS] Generated via gateway {"mode":"gateway","audioSize":1043271,"duration":"12905ms"}
[INTERNAL-PROGRESS] 7fffda8a-06e1-4895-8466-9074f54aaf5a: 正在合并音频...
[INTERNAL-PROGRESS] 7fffda8a-06e1-4895-8466-9074f54aaf5a: 正在保存音频文件...
Audio file stored: \var\data\tts-app\audios\7fffda8a-06e1-4895-8466-9074f54aaf5a.mp3, size: 2787085 bytes
Updated usage for user 0723: +2508 chars
[TTS-PROCESSOR] Generating secure URLs for taskId: 7fffda8a-06e1-4895-8466-9074f54aaf5a
[TTS-PROCESSOR] Token received: YES
[TTS-PROCESSOR] Token length: 178
[TTS-PROCESSOR] Generated secure streamUrl: http://localhost:3001/api/tts/stream/7fffda8a-06e1-4895-8466-9074f54aaf5a
[TTS-PROCESSOR] Generated secure downloadUrl: http://localhost:3001/api/tts/download/7fffda8a-06e1-4895-8466-9074f54aaf5a
[TTS-PROCESSOR] Token will be passed via Authorization header for security
[WEBSOCKET-MANAGER] Task 7fffda8a-06e1-4895-8466-9074f54aaf5a finished with type: complete, scheduling connection close
[WEBSOCKET-MANAGER] Closing connection for task 7fffda8a-06e1-4895-8466-9074f54aaf5a, code: 1000, reason: Task finished: complete
[WEBSOCKET-MANAGER] Cleaned up connection for task 7fffda8a-06e1-4895-8466-9074f54aaf5a
WebSocket closed for task 7fffda8a-06e1-4895-8466-9074f54aaf5a
CORS: Development mode - allowing all origins (current: http://localhost:4000)
CORS: Development mode - allowing all origins (current: http://localhost:4000)
[STREAM] Request for taskId: 7fffda8a-06e1-4895-8466-9074f54aaf5a, token source: header
[STREAM] Token verified for user: 0723, taskId: 7fffda8a-06e1-4895-8466-9074f54aaf5a
[STREAM] Checking file path: \var\data\tts-app\audios\7fffda8a-06e1-4895-8466-9074f54aaf5a.mp3
[STREAM] File found, size: 2787085 bytes
[INFO] [2025-07-26T01:25:38.415Z] [user:system] [task:N/A] - GET /stream/7fffda8a-06e1-4895-8466-9074f54aaf5a {"status":200,"duration":"10ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::1"}
[DEBUG] [2025-07-26T01:25:54.495Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"GET","url":"https://httpbin.org/ip","mode":"gateway"}
[DEBUG] [2025-07-26T01:25:54.496Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node sg-sg-053x-idx-4 (attempt 1/114)
[DEBUG] [2025-07-26T01:25:54.623Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-053x-idx-4 {"nodeTag":"sg-sg-053x-idx-4","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-26T01:25:54.624Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node sg-sg-053x-idx-4 passed health check 
[DEBUG] [2025-07-26T01:25:54.624Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-4 to switch to node: sg-sg-053x-idx-4
[INFO] [2025-07-26T01:25:54.624Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-4","targetNode":"sg-sg-053x-idx-4","originalNodeTag":"sg-sg-053x-idx-4","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-4","requestBody":{"name":"sg-sg-053x-idx-4"}}
[DEBUG] [2025-07-26T01:25:54.626Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-4     
[INFO] [2025-07-26T01:25:54.626Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-4","newNode":"sg-sg-053x-idx-4","fixedNodeTag":"sg-sg-053x-idx-4"}
[DEBUG] [2025-07-26T01:25:54.627Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-4 successfully switched to node: sg-sg-053x-idx-4
[DEBUG] [2025-07-26T01:25:54.627Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":4,"port":1084,"selector":"worker-selector-4","assignedNode":"sg-sg-053x-idx-4"}
[INFO] [2025-07-26T01:25:54.627Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":4,"workerPort":1084,"assignedNode":"sg-sg-053x-idx-4","method":"GET","url":"https://httpbin.org/ip","attempt":1,"maxAttempts":2}
[INFO] [2025-07-26T01:25:54.627Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1084","workerId":4,"assignedNode":"sg-sg-053x-idx-4","method":"GET","hasProxy":true,"proxyType":"dispatcher","attempt":1}
[ERROR] [2025-07-26T01:25:54.668Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"fetch failed","method":"GET","url":"https://httpbin.org/ip","workerId":4,"assignedNode":"sg-sg-053x-idx-4","attempt":1,"maxAttempts":2} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":4,\"assignedNode\":\"sg-sg-053x-idx-4\",\"attempt\":1,\"maxAttempts\":2}","method":"GET","url":"https://httpbin.org/ip","workerId":4,"assignedNode":"sg-sg-053x-idx-4","attempt":1,"maxAttempts":2,"stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":4,\"assignedNode\":\"sg-sg-053x-idx-4\",\"attempt\":1,\"maxAttempts\":2}\n    at TTSRouteLogger.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:270:18)\n    at Object.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (D:\\myaitts\\backend\\src\\gateway\\adapters\\NetworkAdapter.js:207:23)\n    at p"}
[WARN] [2025-07-26T01:25:54.668Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Worker pool network connectivity error {"error":"fetch failed","url":"https://httpbin.org/ip","workerId":4,"nodeTag":"sg-sg-053x-idx-4"}
[WARN] [2025-07-26T01:25:54.669Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: sg-sg-053x-idx-4 {"reason":"Network error: fetch failed"}
[INFO] [2025-07-26T01:25:54.669Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"sg-sg-053x-idx-4","reason":"Network error: fetch failed","quarantineType":"temporary","remainingHealthyNodes":113,"totalQuarantinedNodes":3}
[INFO] [2025-07-26T01:25:54.669Z] [user:system] [task:N/A] - [TTS-GATEWAY] Gateway request failed, retrying with new node {"action":"Gateway request failed, retrying with new node","attempt":1,"maxAttempts":2,"error":"fetch failed","failedNode":"sg-sg-053x-idx-4","willRetry":true}
[DEBUG] [2025-07-26T01:25:54.670Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":4,"port":1084,"currentNode":"sg-sg-053x-idx-4"}
[INFO] [2025-07-26T01:25:54.670Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":4,"assignedNode":"sg-sg-053x-idx-4","attempt":1}
[DEBUG] [2025-07-26T01:25:54.670Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node sg-sg-083x-idx-7 (attempt 1/113)
[DEBUG] [2025-07-26T01:25:54.671Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Saved quarantine data for 3 nodes
[DEBUG] [2025-07-26T01:25:54.750Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-083x-idx-7 {"nodeTag":"sg-sg-083x-idx-7","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-26T01:25:54.751Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node sg-sg-083x-idx-7 passed health check 
[DEBUG] [2025-07-26T01:25:54.751Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-5 to switch to node: sg-sg-083x-idx-7
[INFO] [2025-07-26T01:25:54.751Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-5","targetNode":"sg-sg-083x-idx-7","originalNodeTag":"sg-sg-083x-idx-7","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-5","requestBody":{"name":"sg-sg-083x-idx-7"}}
[DEBUG] [2025-07-26T01:25:54.752Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-5     
[INFO] [2025-07-26T01:25:54.752Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-5","newNode":"sg-sg-083x-idx-7","fixedNodeTag":"sg-sg-083x-idx-7"}
[DEBUG] [2025-07-26T01:25:54.752Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-5 successfully switched to node: sg-sg-083x-idx-7
[DEBUG] [2025-07-26T01:25:54.753Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":5,"port":1085,"selector":"worker-selector-5","assignedNode":"sg-sg-083x-idx-7"}
[INFO] [2025-07-26T01:25:54.753Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":5,"workerPort":1085,"assignedNode":"sg-sg-083x-idx-7","method":"GET","url":"https://httpbin.org/ip","attempt":2,"maxAttempts":2}
[INFO] [2025-07-26T01:25:54.753Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1085","workerId":5,"assignedNode":"sg-sg-083x-idx-7","method":"GET","hasProxy":true,"proxyType":"dispatcher","attempt":2}
[ERROR] [2025-07-26T01:25:54.810Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"fetch failed","method":"GET","url":"https://httpbin.org/ip","workerId":5,"assignedNode":"sg-sg-083x-idx-7","attempt":2,"maxAttempts":2} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":5,\"assignedNode\":\"sg-sg-083x-idx-7\",\"attempt\":2,\"maxAttempts\":2}","method":"GET","url":"https://httpbin.org/ip","workerId":5,"assignedNode":"sg-sg-083x-idx-7","attempt":2,"maxAttempts":2,"stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":5,\"assignedNode\":\"sg-sg-083x-idx-7\",\"attempt\":2,\"maxAttempts\":2}\n    at TTSRouteLogger.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:270:18)\n    at Object.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (D:\\myaitts\\backend\\src\\gateway\\adapters\\NetworkAdapter.js:207:23)\n    at p"}
[WARN] [2025-07-26T01:25:54.810Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Worker pool network connectivity error {"error":"fetch failed","url":"https://httpbin.org/ip","workerId":5,"nodeTag":"sg-sg-083x-idx-7"}
[WARN] [2025-07-26T01:25:54.810Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: sg-sg-083x-idx-7 {"reason":"Network error: fetch failed"}
[INFO] [2025-07-26T01:25:54.811Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"sg-sg-083x-idx-7","reason":"Network error: fetch failed","quarantineType":"temporary","remainingHealthyNodes":112,"totalQuarantinedNodes":4}
[DEBUG] [2025-07-26T01:25:54.811Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":5,"port":1085,"currentNode":"sg-sg-083x-idx-7"}
[INFO] [2025-07-26T01:25:54.811Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":5,"assignedNode":"sg-sg-083x-idx-7","attempt":2}
[ERROR] [2025-07-26T01:25:54.811Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[WARN] [2025-07-26T01:25:54.811Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Health check failed {"error":"fetch failed"}
[WARN] [2025-07-26T01:25:54.812Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Network adapter health check failed
[DEBUG] [2025-07-26T01:25:54.812Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Health check completed {"networkHealthy":false,"timestamp":1753493154495}
[DEBUG] [2025-07-26T01:25:54.813Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Saved quarantine data for 4 nodes
[DEBUG] [2025-07-26T01:26:24.508Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"GET","url":"https://httpbin.org/ip","mode":"gateway"}
[DEBUG] [2025-07-26T01:26:24.508Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node jp-jp-013x-idx-9 (attempt 1/112)
[DEBUG] [2025-07-26T01:26:24.639Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node jp-jp-013x-idx-9 {"nodeTag":"jp-jp-013x-idx-9","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-26T01:26:24.639Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node jp-jp-013x-idx-9 passed health check 
[DEBUG] [2025-07-26T01:26:24.640Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-6 to switch to node: jp-jp-013x-idx-9
[INFO] [2025-07-26T01:26:24.640Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-6","targetNode":"jp-jp-013x-idx-9","originalNodeTag":"jp-jp-013x-idx-9","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-6","requestBody":{"name":"jp-jp-013x-idx-9"}}
[DEBUG] [2025-07-26T01:26:24.642Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-6     
[INFO] [2025-07-26T01:26:24.642Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-6","newNode":"jp-jp-013x-idx-9","fixedNodeTag":"jp-jp-013x-idx-9"}
[DEBUG] [2025-07-26T01:26:24.643Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-6 successfully switched to node: jp-jp-013x-idx-9
[DEBUG] [2025-07-26T01:26:24.643Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":6,"port":1086,"selector":"worker-selector-6","assignedNode":"jp-jp-013x-idx-9"}
[INFO] [2025-07-26T01:26:24.643Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":6,"workerPort":1086,"assignedNode":"jp-jp-013x-idx-9","method":"GET","url":"https://httpbin.org/ip","attempt":1,"maxAttempts":2}
[INFO] [2025-07-26T01:26:24.643Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1086","workerId":6,"assignedNode":"jp-jp-013x-idx-9","method":"GET","hasProxy":true,"proxyType":"dispatcher","attempt":1}
[INFO] [2025-07-26T01:26:25.587Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker pool response received {"action":"Worker pool response received","status":200,"ok":true,"workerId":6,"assignedNode":"jp-jp-013x-idx-9","attempt":1}
[DEBUG] [2025-07-26T01:26:25.588Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":6,"port":1086,"currentNode":"jp-jp-013x-idx-9"}
[INFO] [2025-07-26T01:26:25.588Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":6,"assignedNode":"jp-jp-013x-idx-9","attempt":1}
[DEBUG] [2025-07-26T01:26:25.588Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Network request: GET https://httpbin.org/ip {"method":"GET","url":"https://httpbin.org/ip","status":200,"duration":"1081ms"}
[DEBUG] [2025-07-26T01:26:25.588Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Health check completed {"mode":"gateway","healthy":true,"status":200}
[DEBUG] [2025-07-26T01:26:25.589Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Health check completed {"networkHealthy":true,"timestamp":1753493184507}
[INFO] [2025-07-26T01:26:27.410Z] [user:system] [task:N/A] - SIGINT received, shutting down gracefully
[WEBSOCKET-MANAGER] Cleanup timer stopped
[INFO] [2025-07-26T01:26:27.412Z] [user:system] [task:N/A] - All connections closed successfully