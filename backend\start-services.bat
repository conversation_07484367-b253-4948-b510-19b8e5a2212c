@echo off
chcp 65001 >nul
title TTS应用服务启动器

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🚀 TTS应用服务启动器                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM ========== 配置路径 ==========
REM 🔧 请修改以下路径为您的实际路径
set REDIS_PATH=C:\Users\<USER>\Downloads\Redis-x64-5.0.14.1
set POSTGRES_PATH=C:\Users\<USER>\Downloads\postgresql-17.5-3-windows-x64-binaries\pgsql\bin

echo 📋 当前配置：
echo    Redis路径: %REDIS_PATH%
echo    PostgreSQL路径: %POSTGRES_PATH%
echo.

echo ❓ 请选择操作：
echo    1. 🔴 启动Redis服务
echo    2. 🐘 启动PostgreSQL服务
echo    3. 🧪 测试服务连接
echo    4. 🗄️ 初始化数据库
echo    5. ⚡ 启动TTS应用
echo    6. 🛠️ 修改服务路径
echo    0. ❌ 退出
echo.

set /p choice=请输入选项 (0-6):

if "%choice%"=="1" goto start_redis
if "%choice%"=="2" goto start_postgres
if "%choice%"=="3" goto test_services
if "%choice%"=="4" goto init_database
if "%choice%"=="5" goto start_app
if "%choice%"=="6" goto config_paths
if "%choice%"=="0" goto exit
goto main_menu

:start_redis
echo.
echo 🔴 启动Redis服务...
if not exist "%REDIS_PATH%\redis-server.exe" (
    echo ❌ 错误: 找不到Redis服务器文件
    echo    请检查路径: %REDIS_PATH%\redis-server.exe
    pause
    goto main_menu
)

echo 📂 切换到Redis目录: %REDIS_PATH%
cd /d "%REDIS_PATH%"

echo 🚀 启动Redis服务器...
echo    (按Ctrl+C可以停止Redis服务)
echo.
start "Redis Server" cmd /k "redis-server.exe"

echo ✅ Redis服务已在新窗口中启动
echo    端口: 6379
echo    测试命令: redis-cli ping
echo.
pause
goto main_menu

:start_postgres
echo.
echo 🐘 启动PostgreSQL服务...
if not exist "%POSTGRES_PATH%\bin\pg_ctl.exe" (
    echo ❌ 错误: 找不到PostgreSQL文件
    echo    请检查路径: %POSTGRES_PATH%\bin\pg_ctl.exe
    pause
    goto main_menu
)

echo 📂 切换到PostgreSQL bin目录
cd /d "%POSTGRES_PATH%\bin"

echo 🔍 检查数据目录...
if not exist "..\data\postgresql.conf" (
    echo ⚠️  数据目录不存在，需要先初始化数据库
    echo    请选择选项4进行初始化
    pause
    goto main_menu
)

echo 🚀 启动PostgreSQL服务...
pg_ctl.exe -D ..\data start

if %errorlevel%==0 (
    echo ✅ PostgreSQL服务启动成功
    echo    端口: 5432
    echo    连接命令: psql -U postgres
) else (
    echo ❌ PostgreSQL启动失败
    echo    请检查日志或重新初始化数据库
)
echo.
pause
goto main_menu

:test_services
echo.
echo 🧪 测试服务连接...
echo.

echo 🔴 测试Redis连接...
if exist "%REDIS_PATH%\redis-cli.exe" (
    cd /d "%REDIS_PATH%"
    redis-cli.exe ping
    if %errorlevel%==0 (
        echo ✅ Redis连接成功
    ) else (
        echo ❌ Redis连接失败，请确保Redis服务已启动
    )
) else (
    echo ❌ 找不到redis-cli.exe
)

echo.
echo 🐘 测试PostgreSQL连接...
if exist "%POSTGRES_PATH%\bin\psql.exe" (
    cd /d "%POSTGRES_PATH%\bin"
    echo 正在测试PostgreSQL连接...
    psql.exe -U postgres -c "SELECT version();" 2>nul
    if %errorlevel%==0 (
        echo ✅ PostgreSQL连接成功
    ) else (
        echo ❌ PostgreSQL连接失败，请确保服务已启动
    )
) else (
    echo ❌ 找不到psql.exe
)

echo.
pause
goto main_menu

:init_database
echo.
echo 🗄️ 初始化PostgreSQL数据库...
if not exist "%POSTGRES_PATH%\bin\initdb.exe" (
    echo ❌ 错误: 找不到initdb.exe
    pause
    goto main_menu
)

cd /d "%POSTGRES_PATH%\bin"

echo 📁 创建数据目录...
if not exist "..\data" mkdir "..\data"

echo 🔧 初始化数据库...
echo    用户名: postgres
echo    请设置postgres用户的密码
echo.
initdb.exe -D ..\data -U postgres -W

if %errorlevel%==0 (
    echo ✅ 数据库初始化成功
    echo    现在可以启动PostgreSQL服务了
) else (
    echo ❌ 数据库初始化失败
)

echo.
pause
goto main_menu

:start_app
echo.
echo ⚡ 启动TTS应用...
echo.

echo 🔍 检查服务状态...
REM 简单检查Redis和PostgreSQL是否运行
tasklist /fi "imagename eq redis-server.exe" 2>nul | find /i "redis-server.exe" >nul
if %errorlevel%==0 (
    echo ✅ Redis服务正在运行
) else (
    echo ⚠️  Redis服务未运行，建议先启动Redis
)

tasklist /fi "imagename eq postgres.exe" 2>nul | find /i "postgres.exe" >nul
if %errorlevel%==0 (
    echo ✅ PostgreSQL服务正在运行
) else (
    echo ⚠️  PostgreSQL服务未运行，建议先启动PostgreSQL
)

echo.
echo 🚀 启动TTS应用开发服务器...
cd /d "%~dp0"
npm run dev

pause
goto main_menu

:config_paths
echo.
echo 🛠️ 配置服务路径...
echo.
echo 当前配置：
echo    Redis路径: %REDIS_PATH%
echo    PostgreSQL路径: %POSTGRES_PATH%
echo.

set /p new_redis_path=请输入Redis路径 (回车保持当前):
if not "%new_redis_path%"=="" set REDIS_PATH=%new_redis_path%

set /p new_postgres_path=请输入PostgreSQL路径 (回车保持当前):
if not "%new_postgres_path%"=="" set POSTGRES_PATH=%new_postgres_path%

echo.
echo ✅ 路径配置已更新：
echo    Redis路径: %REDIS_PATH%
echo    PostgreSQL路径: %POSTGRES_PATH%
echo.
pause
goto main_menu

:exit
echo.
echo 👋 感谢使用TTS应用服务启动器！
echo.
exit /b 0

:main_menu
cls
goto :eof
