require('dotenv').config();
const { selectRandomProxyUrl, callTtsProxy, callDirectElevenLabs, generateSpeech } = require('./src/utils/ttsUtils');
const { getTTSProxyConfig } = require('./src/utils/config');

/**
 * 测试多代理随机选择功能
 */
async function testMultiProxyFeatures() {
  console.log('🚀 开始测试多代理随机选择功能...\n');

  // 1. 测试配置读取
  console.log('📋 1. 测试配置读取');
  const proxyConfig = getTTSProxyConfig();
  console.log('代理配置:', {
    ENABLE_TTS_PROXY: proxyConfig.ENABLE_TTS_PROXY,
    TTS_PROXY_MODE: proxyConfig.TTS_PROXY_MODE,
    TTS_PROXY_URLS: proxyConfig.TTS_PROXY_URLS,
    TTS_PROXY_SECRET: proxyConfig.TTS_PROXY_SECRET ? '***已配置***' : '未配置',
    TTS_PROXY_TIMEOUT: proxyConfig.TTS_PROXY_TIMEOUT,
    ENABLE_PROXY_DEBUG: proxyConfig.ENABLE_PROXY_DEBUG
  });
  console.log('✅ 配置读取成功\n');

  // 2. 测试随机URL选择
  console.log('🎲 2. 测试随机URL选择');
  if (proxyConfig.TTS_PROXY_URLS && proxyConfig.TTS_PROXY_URLS.length > 0) {
    console.log(`可用代理URL数量: ${proxyConfig.TTS_PROXY_URLS.length}`);
    console.log('代理URL列表:', proxyConfig.TTS_PROXY_URLS);
    
    // 测试多次随机选择
    console.log('\n进行10次随机选择测试:');
    const selectionCounts = {};
    for (let i = 0; i < 10; i++) {
      const selectedUrl = selectRandomProxyUrl(proxyConfig.TTS_PROXY_URLS);
      console.log(`第${i + 1}次选择: ${selectedUrl}`);
      selectionCounts[selectedUrl] = (selectionCounts[selectedUrl] || 0) + 1;
    }
    
    console.log('\n选择统计:');
    Object.entries(selectionCounts).forEach(([url, count]) => {
      console.log(`${url}: ${count}次`);
    });
    console.log('✅ 随机选择功能正常\n');
  } else {
    console.log('❌ 没有配置代理URL');
    return;
  }

  // 3. 测试模式切换逻辑
  console.log('🔄 3. 测试模式切换逻辑');
  console.log(`当前模式: ${proxyConfig.TTS_PROXY_MODE}`);
  
  if (proxyConfig.TTS_PROXY_MODE === 'proxy') {
    console.log('✅ 仅代理模式 - generateSpeech将直接使用随机代理');
  } else if (proxyConfig.TTS_PROXY_MODE === 'fallback') {
    console.log('✅ 故障转移模式 - generateSpeech将先尝试直连，失败后使用随机代理');
  } else {
    console.log('✅ 直连模式 - generateSpeech将只使用直连');
  }
  console.log();

  // 4. 测试实际音频生成（使用短文本避免配额消耗）
  console.log('🎵 4. 测试实际音频生成');
  const testText = 'Hello';
  const testVoiceId = 'pNInz6obpgDQGcFmaJgB'; // Adam voice
  const testModel = 'eleven_turbo_v2';
  
  try {
    console.log(`测试文本: "${testText}"`);
    console.log(`语音ID: ${testVoiceId}`);
    console.log(`模型: ${testModel}`);
    console.log('开始生成音频...');
    
    const startTime = Date.now();
    const audioBuffer = await generateSpeech(
      testText,
      testVoiceId,
      testModel,
      0.5,  // stability
      0.75, // similarity_boost
      0.5,  // style
      1.0   // speed
    );
    const endTime = Date.now();
    
    console.log(`✅ 音频生成成功!`);
    console.log(`音频大小: ${audioBuffer.byteLength} bytes`);
    console.log(`生成耗时: ${endTime - startTime}ms`);
    console.log();
    
  } catch (error) {
    console.log(`❌ 音频生成失败: ${error.message}`);
    console.log('这可能是由于网络问题或代理配置问题导致的');
    console.log();
  }

  console.log('🎉 多代理随机选择功能测试完成!');
}

/**
 * 测试不同模式的行为
 */
async function testDifferentModes() {
  console.log('\n🔧 测试不同代理模式的行为...\n');
  
  const originalMode = process.env.TTS_PROXY_MODE;
  const testText = 'Test';
  const testVoiceId = 'pNInz6obpgDQGcFmaJgB';
  
  // 测试直连模式
  console.log('📡 测试直连模式 (TTS_PROXY_MODE=direct)');
  process.env.TTS_PROXY_MODE = 'direct';
  try {
    const audioBuffer = await generateSpeech(testText, testVoiceId, 'eleven_turbo_v2');
    console.log(`✅ 直连模式成功，音频大小: ${audioBuffer.byteLength} bytes`);
  } catch (error) {
    console.log(`❌ 直连模式失败: ${error.message}`);
  }
  
  // 测试仅代理模式
  console.log('\n🔀 测试仅代理模式 (TTS_PROXY_MODE=proxy)');
  process.env.TTS_PROXY_MODE = 'proxy';
  try {
    const audioBuffer = await generateSpeech(testText, testVoiceId, 'eleven_turbo_v2');
    console.log(`✅ 仅代理模式成功，音频大小: ${audioBuffer.byteLength} bytes`);
  } catch (error) {
    console.log(`❌ 仅代理模式失败: ${error.message}`);
  }
  
  // 测试故障转移模式
  console.log('\n🔄 测试故障转移模式 (TTS_PROXY_MODE=fallback)');
  process.env.TTS_PROXY_MODE = 'fallback';
  try {
    const audioBuffer = await generateSpeech(testText, testVoiceId, 'eleven_turbo_v2');
    console.log(`✅ 故障转移模式成功，音频大小: ${audioBuffer.byteLength} bytes`);
  } catch (error) {
    console.log(`❌ 故障转移模式失败: ${error.message}`);
  }
  
  // 恢复原始模式
  process.env.TTS_PROXY_MODE = originalMode;
  console.log(`\n🔙 已恢复原始模式: ${originalMode}`);
}

// 运行测试
async function runTests() {
  try {
    await testMultiProxyFeatures();
    await testDifferentModes();
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runTests();
}

module.exports = {
  testMultiProxyFeatures,
  testDifferentModes,
  runTests
};
