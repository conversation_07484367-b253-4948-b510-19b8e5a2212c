"use client"

import React, { useRef, useEffect, useCallback } from 'react';

// 自定义Hook，用于管理 contentEditable div 中的光标位置
const useCaretPosition = (editableRef: React.RefObject<HTMLDivElement | null>) => {
  const getPosition = useCallback(() => {
    const el = editableRef.current;
    if (!el) return 0;

    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const preCaretRange = range.cloneRange();
      preCaretRange.selectNodeContents(el);
      preCaretRange.setEnd(range.endContainer, range.endOffset);
      return preCaretRange.toString().length;
    }
    return 0;
  }, [editableRef]);

  const setPosition = useCallback((position: number) => {
    const el = editableRef.current;
    if (!el || position < 0) return;

    const selection = window.getSelection();
    const range = document.createRange();

    let charCount = 0;
    let found = false;

    function traverse(node: Node) {
      if (found) return;

      if (node.nodeType === Node.TEXT_NODE) {
        const textNode = node as Text;
        const nextCharCount = charCount + textNode.length;
        if (position <= nextCharCount) {
          range.setStart(textNode, position - charCount);
          range.setEnd(textNode, position - charCount);
          found = true;
        } else {
          charCount = nextCharCount;
        }
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        const children = Array.from(node.childNodes);
        for (const child of children) {
          traverse(child);
          if (found) return;
        }
      }
    }
    
    // 如果div为空，直接设置光标
    if (el.childNodes.length === 0) {
      el.focus();
      return;
    }

    traverse(el);
    
    // 如果遍历后找不到位置（例如，位置超出内容长度），则将光标置于末尾
    if (!found) {
       range.selectNodeContents(el);
       range.collapse(false); // false 表示折叠到末尾
    }

    if (selection) {
      selection.removeAllRanges();
      selection.addRange(range);
    }
  }, [editableRef]);

  return { getPosition, setPosition };
};

// 情感标注词高亮函数
const highlightText = (text: string): string => {
  if (!text) return '';
  // 正则表达式匹配 [word] 格式的标签
  const regex = /\[([a-zA-Z\s]+)\]/g;
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(regex, '<span class="emotion-tag">[$1]</span>');
};

interface RichTextInputProps {
  value: string;
  onChange: (value: string) => void;
  onFocus: () => void;
  onBlur: () => void;
  placeholder?: string;
  className?: string;
  maxLength: number;
  onMaxLengthExceeded?: (currentLength: number, maxLength: number) => void;
}

const RichTextInput: React.FC<RichTextInputProps> = ({
  value,
  onChange,
  onFocus,
  onBlur,
  placeholder,
  className,
  maxLength,
  onMaxLengthExceeded,
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const valueRef = useRef(value);
  const { getPosition, setPosition } = useCaretPosition(editorRef);

  useEffect(() => {
    // 之前的条件 `value !== editorRef.current.innerText` 是错误的，它阻止了高亮更新。
    // 新的逻辑比较当前 `value` prop 和上一次渲染的 `value`，确保在值变化时才更新DOM。
    if (editorRef.current && value !== valueRef.current) {
      const caretPosition = getPosition();
      editorRef.current.innerHTML = highlightText(value);
      setPosition(caretPosition);
    }
    // 在检查之后更新ref，为下一次渲染做准备。
    valueRef.current = value;
  }, [value, getPosition, setPosition]);


  const handleInput = useCallback(() => {
    if (editorRef.current) {
      const plainText = editorRef.current.innerText;

      if (plainText.length > maxLength) {
        // 自动截取到最大长度
        const truncatedText = plainText.substring(0, maxLength);
        const caretPosition = getPosition();

        // 更新DOM显示截取后的内容
        editorRef.current.innerHTML = highlightText(truncatedText);
        // 将光标定位到合理的位置
        setPosition(Math.min(maxLength, caretPosition));

        // 通知父组件超过字符限制，并传递截取后的文本
        if (onMaxLengthExceeded) {
          onMaxLengthExceeded(plainText.length, maxLength);
        }

        // 调用onChange更新父组件的状态
        onChange(truncatedText);
        return;
      }

      // 如果输入有效，则调用onChange。useEffect将处理高亮显示。
      onChange(plainText);
    }
  }, [onChange, maxLength, value, getPosition, setPosition, onMaxLengthExceeded]);


  return (
    <>
      <style>{`
        .emotion-tag {
          color: #8B5CF6; /* a nice purple color */
          font-weight: 600;
          background-color: rgba(139, 92, 246, 0.05);
          padding: 2px 1px;
          border-radius: 4px;
        }
      `}</style>
      <div
        ref={editorRef}
        contentEditable={true}
        onInput={handleInput}
        onFocus={onFocus}
        onBlur={onBlur}
        className={className}
        data-placeholder={placeholder}
        suppressContentEditableWarning={true}
        style={{
          whiteSpace: 'pre-wrap', // 保持换行
          wordWrap: 'break-word',   // 自动换行
        }}
      />
    </>
  );
};

export default RichTextInput; 