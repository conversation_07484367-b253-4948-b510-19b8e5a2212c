"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { auth } from "@/lib/auth-service"
import { TokenManager } from "@/lib/api"
import { useToast } from "@/hooks/use-toast"
import { handleAuthError } from "@/lib/error-utils"
import { Clock, LogOut } from "lucide-react"

export default function TestAuthFlowPage() {
  const [result, setResult] = useState<string>("")
  const [showAuthDialog, setShowAuthDialog] = useState(false)
  const { toast } = useToast()

  const simulateTokenExpired = async () => {
    try {
      setResult("开始测试...")
      
      // 设置无效的access token和refresh token
      TokenManager.setTokens("invalid_access_token", "invalid_refresh_token", "<EMAIL>")
      
      setResult("已设置无效token，正在调用getUserQuota...")
      
      // 尝试获取用户配额 - 这会触发withTokenRefresh逻辑
      const data = await auth.getUserQuota()
      setResult("意外成功: " + JSON.stringify(data))
      
    } catch (error: any) {
      console.error('测试错误:', error)
      setResult(`捕获错误: ${error.message}\n错误码: ${error.code || "无"}\nshouldRedirect: ${error.shouldRedirect || "无"}`)
      
      // 使用错误处理函数
      const { isAuthError: isAuth, shouldRedirect } = handleAuthError(error, () => {
        console.log("认证错误回调被触发")
        setShowAuthDialog(true)
        toast({
          title: "认证失败",
          description: "会话已过期，正在跳转到登录页面...",
          variant: "destructive",
        })
      })
      
      console.log("错误处理结果:", { isAuth, shouldRedirect })
      
      if (isAuth && shouldRedirect) {
        console.log("将在2秒后跳转到登录页面")
        setTimeout(() => {
          console.log("执行跳转")
          window.location.href = '/login'
        }, 2000)
      }
    }
  }

  const clearTokens = () => {
    TokenManager.clearTokens()
    setResult("已清除所有token")
    setShowAuthDialog(false)
  }

  const checkCurrentTokens = () => {
    const accessToken = TokenManager.getAccessToken()
    const refreshToken = TokenManager.getRefreshToken()
    const email = TokenManager.getUserEmail()
    
    setResult(`当前token状态:
Access Token: ${accessToken ? `${accessToken.substring(0, 20)}...` : "无"}
Refresh Token: ${refreshToken ? `${refreshToken.substring(0, 20)}...` : "无"}
Email: ${email || "无"}`)
  }

  return (
    <div className="container mx-auto p-4">
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>认证流程完整测试</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Button onClick={checkCurrentTokens} variant="outline">
              检查当前Token状态
            </Button>
            <Button onClick={simulateTokenExpired} variant="destructive">
              模拟Token过期（完整流程）
            </Button>
            <Button onClick={clearTokens} variant="secondary">
              清除所有Token
            </Button>
          </div>
          
          {result && (
            <div className="p-4 bg-gray-100 rounded-lg">
              <h3 className="font-semibold mb-2">测试结果:</h3>
              <pre className="text-sm whitespace-pre-wrap">{result}</pre>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 登录过期弹窗 */}
      <Dialog open={showAuthDialog} onOpenChange={setShowAuthDialog}>
        <DialogContent className="sm:max-w-md border-0 shadow-2xl bg-white/95 backdrop-blur-xl">
          <DialogHeader className="text-center space-y-4">
            <div className="mx-auto w-16 h-16 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full flex items-center justify-center">
              <Clock className="w-8 h-8 text-blue-500" />
            </div>
            <DialogTitle className="text-center text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              登录已过期
            </DialogTitle>
            <DialogDescription className="text-gray-600">
              <div className="flex items-center justify-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <span className="text-sm">您的会话已过期，请重新登录以继续。</span>
              </div>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-6">
            <Button
              onClick={async () => {
                setShowAuthDialog(false)
                await auth.logout()
                window.location.href = "/login"
              }}
              className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <LogOut className="w-4 h-4 mr-2" />
              重新登录
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
