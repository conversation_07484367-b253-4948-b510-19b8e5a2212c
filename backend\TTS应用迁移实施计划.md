# TTS应用迁移实施计划

## 目录

- [1. 项目概述](#1-项目概述)
- [2. 环境准备阶段](#2-环境准备阶段)
- [3. 数据迁移方案](#3-数据迁移方案)
- [4. 代码迁移指南](#4-代码迁移指南)
- [5. 配置管理](#5-配置管理)
- [6. 服务部署](#6-服务部署)
- [7. 测试验证](#7-测试验证)
- [8. 监控和日志](#8-监控和日志)
- [9. 故障排除](#9-故障排除)
- [10. 性能优化](#10-性能优化)

## 1. 项目概述

### 1.1 迁移目标
将基于Cloudflare Worker的TTS应用迁移至Ubuntu服务器，实现功能完全对等的生产级部署。

### 1.2 技术架构对比

| 组件 | Cloudflare Worker | Ubuntu部署 |
|------|------------------|------------|
| 运行时 | Cloudflare Worker Runtime | Node.js v18+ |
| 任务管理 | Durable Object | Redis + WebSocket |
| 数据存储 | KV Store | PostgreSQL |
| 文件存储 | R2 | 本地文件系统 + Nginx |
| 进程管理 | 自动 | PM2 |
| 负载均衡 | 自动 | PM2 Cluster |

### 1.3 迁移原则
- **功能对等性**：确保所有原有功能完全保留
- **数据完整性**：保证数据迁移过程中零丢失
- **向后兼容**：保持API接口完全兼容
- **性能优化**：利用单机优势提升性能

## 2. 环境准备阶段

### 2.1 系统要求

**硬件要求：**
- CPU: 4核心以上
- 内存: 8GB以上
- 存储: 100GB以上SSD
- 网络: 100Mbps以上带宽

**软件版本要求：**
- Ubuntu: 22.04 LTS
- Node.js: v18.x LTS
- PostgreSQL: 14.x
- Redis: 7.x
- Nginx: 1.18+
- PM2: 5.x

### 2.2 基础软件安装

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装基础依赖
sudo apt install -y curl wget gnupg2 software-properties-common apt-transport-https ca-certificates

# 安装Node.js (使用NodeSource仓库)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# 验证Node.js安装
node --version  # 应显示 v18.x.x
npm --version   # 应显示 9.x.x

# 安装PostgreSQL
sudo apt install -y postgresql postgresql-contrib

# 安装Redis
sudo apt install -y redis-server

# 安装Nginx
sudo apt install -y nginx

# 安装PM2
sudo npm install -g pm2

# 安装其他工具
sudo apt install -y git htop iotop nethogs
```

**验证标准：**
```bash
# 验证所有服务状态
sudo systemctl status postgresql
sudo systemctl status redis-server
sudo systemctl status nginx
pm2 --version
```

### 2.3 服务配置

**PostgreSQL配置：**
```bash
# 启动并启用PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql

# 创建数据库和用户
sudo -u postgres psql << EOF
CREATE DATABASE tts_app_db;
CREATE USER tts_app_user WITH PASSWORD 'TTS_DB_2024_SecurePass!';
GRANT ALL PRIVILEGES ON DATABASE tts_app_db TO tts_app_user;
ALTER USER tts_app_user CREATEDB;
\q
EOF

# 配置PostgreSQL连接
sudo sed -i "s/#listen_addresses = 'localhost'/listen_addresses = 'localhost'/" /etc/postgresql/14/main/postgresql.conf
sudo systemctl restart postgresql
```

**Redis配置：**
```bash
# 配置Redis
sudo cp /etc/redis/redis.conf /etc/redis/redis.conf.backup
sudo tee /etc/redis/redis.conf > /dev/null << EOF
bind 127.0.0.1
port 6379
timeout 0
tcp-keepalive 300
daemonize yes
supervised systemd
pidfile /var/run/redis/redis-server.pid
loglevel notice
logfile /var/log/redis/redis-server.log
databases 16
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /var/lib/redis
maxmemory 2gb
maxmemory-policy allkeys-lru
EOF

sudo systemctl restart redis-server
sudo systemctl enable redis-server
```

### 2.4 项目目录结构

```bash
# 创建项目目录
sudo mkdir -p /var/www/tts-app
sudo mkdir -p /var/data/tts-app/audios
sudo mkdir -p /var/log/tts-app

# 设置权限
sudo chown -R $USER:$USER /var/www/tts-app
sudo chown -R www-data:www-data /var/data/tts-app
sudo chown -R $USER:$USER /var/log/tts-app

# 创建项目结构
cd /var/www/tts-app
mkdir -p {src/{api,services,utils,middleware},config,scripts,tests,logs}
```

**验证标准：**
```bash
# 验证目录权限
ls -la /var/www/tts-app
ls -la /var/data/tts-app
ls -la /var/log/tts-app

# 验证服务连接
redis-cli ping  # 应返回 PONG
psql -h localhost -U tts_app_user -d tts_app_db -c "SELECT version();"
```

## 3. 数据迁移方案

### 3.1 数据结构映射

基于原代码分析，需要迁移以下KV数据：

| KV命名空间 | PostgreSQL表 | 主要字段 |
|-----------|--------------|----------|
| USERS | users | username, password_hash, email, vip_info, usage_stats |
| CARDS | cards | code, package_type, status, created_at, used_by |
| TTS_STATUS | task_status | task_id, status, result, created_at |
| VOICE_MAPPINGS | voice_mappings | voice_name, voice_id, model_support |

### 3.2 数据库表结构设计

```sql
-- 创建数据库表结构
-- /var/www/tts-app/scripts/create_tables.sql

-- 用户表
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- VIP信息 (JSON格式存储，保持与原KV结构兼容)
    vip_info JSONB DEFAULT '{}',
    
    -- 使用统计 (JSON格式存储)
    usage_stats JSONB DEFAULT '{"totalChars": 0, "monthlyChars": 0, "monthlyResetAt": 0}',
    
    -- 索引
    INDEX idx_users_username (username),
    INDEX idx_users_email (email)
);

-- 卡密表
CREATE TABLE cards (
    id SERIAL PRIMARY KEY,
    code VARCHAR(32) UNIQUE NOT NULL,
    package_type VARCHAR(10) NOT NULL, -- M, Q, H, PM, PQ, PH, PT
    status VARCHAR(20) DEFAULT 'unused', -- unused, used, expired
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    used_at TIMESTAMP NULL,
    used_by VARCHAR(50) NULL,
    
    -- 套餐信息 (JSON格式)
    package_info JSONB NOT NULL,
    
    -- 索引
    INDEX idx_cards_code (code),
    INDEX idx_cards_status (status),
    INDEX idx_cards_used_by (used_by)
);

-- 任务状态表
CREATE TABLE task_status (
    id SERIAL PRIMARY KEY,
    task_id VARCHAR(36) UNIQUE NOT NULL,
    username VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL, -- initialized, processing, complete, failed
    
    -- 任务详情 (JSON格式存储)
    task_data JSONB DEFAULT '{}',
    
    -- 结果信息 (JSON格式存储)
    result_data JSONB DEFAULT '{}',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    
    -- 索引
    INDEX idx_task_status_task_id (task_id),
    INDEX idx_task_status_username (username),
    INDEX idx_task_status_status (status),
    INDEX idx_task_status_created_at (created_at)
);

-- 语音映射表
CREATE TABLE voice_mappings (
    id SERIAL PRIMARY KEY,
    voice_name VARCHAR(100) UNIQUE NOT NULL,
    voice_id VARCHAR(100) NOT NULL,
    model_support JSONB DEFAULT '[]', -- 支持的模型列表
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 索引
    INDEX idx_voice_mappings_name (voice_name),
    INDEX idx_voice_mappings_id (voice_id)
);

-- 【遗漏补充】验证码表（替代KV存储验证码）
CREATE TABLE verification_codes (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    code VARCHAR(10) NOT NULL,
    expire_time TIMESTAMP NOT NULL,
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 5,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 索引
    INDEX idx_verification_codes_email (email),
    INDEX idx_verification_codes_expire (expire_time)
);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_task_status_updated_at BEFORE UPDATE ON task_status
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### 3.3 数据迁移脚本

```javascript
// /var/www/tts-app/scripts/migrate_data.js
const { Pool } = require('pg');

// 数据迁移配置
const MIGRATION_CONFIG = {
  // Cloudflare API配置 (需要从Cloudflare获取)
  CF_ACCOUNT_ID: process.env.CF_ACCOUNT_ID,
  CF_API_TOKEN: process.env.CF_API_TOKEN,
  
  // KV命名空间ID (从wrangler.toml获取 - 已更新为实际ID)
  KV_NAMESPACES: {
    USERS: '8341ec47189543b48818f57e9ca4e5e0',
    CARDS: '69d6e32b35dd4a0bb996584ebf3f5b27',
    TTS_STATUS: '0ae5fbcb1ed34dab9357ae1a838b34f3',
    VOICE_MAPPINGS: '065bf81a6ad347d19709b402659608f5'
  },
  
  // PostgreSQL连接
  PG_CONFIG: {
    connectionString: process.env.DATABASE_URL
  }
};

class DataMigrator {
  constructor() {
    this.pgPool = new Pool(MIGRATION_CONFIG.PG_CONFIG);
  }

  // 从Cloudflare KV获取所有数据
  async fetchKVData(namespaceId) {
    const response = await fetch(
      `https://api.cloudflare.com/client/v4/accounts/${MIGRATION_CONFIG.CF_ACCOUNT_ID}/storage/kv/namespaces/${namespaceId}/keys`,
      {
        headers: {
          'Authorization': `Bearer ${MIGRATION_CONFIG.CF_API_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    const { result } = await response.json();
    const data = {};
    
    // 获取每个key的值
    for (const key of result) {
      const valueResponse = await fetch(
        `https://api.cloudflare.com/client/v4/accounts/${MIGRATION_CONFIG.CF_ACCOUNT_ID}/storage/kv/namespaces/${namespaceId}/values/${key.name}`,
        {
          headers: {
            'Authorization': `Bearer ${MIGRATION_CONFIG.CF_API_TOKEN}`
          }
        }
      );
      data[key.name] = await valueResponse.text();
    }
    
    return data;
  }

  // 迁移用户数据
  async migrateUsers() {
    console.log('开始迁移用户数据...');
    const userData = await this.fetchKVData(MIGRATION_CONFIG.KV_NAMESPACES.USERS);
    
    for (const [key, value] of Object.entries(userData)) {
      if (key.startsWith('user:')) {
        const username = key.replace('user:', '');
        const userInfo = JSON.parse(value);
        
        await this.pgPool.query(`
          INSERT INTO users (username, password_hash, email, vip_info, usage_stats)
          VALUES ($1, $2, $3, $4, $5)
          ON CONFLICT (username) DO UPDATE SET
            password_hash = EXCLUDED.password_hash,
            email = EXCLUDED.email,
            vip_info = EXCLUDED.vip_info,
            usage_stats = EXCLUDED.usage_stats,
            updated_at = CURRENT_TIMESTAMP
        `, [
          username,
          userInfo.password || '',
          userInfo.email || null,
          JSON.stringify(userInfo.vip || {}),
          JSON.stringify(userInfo.usage || {})
        ]);
      }
    }
    console.log('用户数据迁移完成');
  }

  // 迁移卡密数据
  async migrateCards() {
    console.log('开始迁移卡密数据...');
    const cardData = await this.fetchKVData(MIGRATION_CONFIG.KV_NAMESPACES.CARDS);
    
    for (const [key, value] of Object.entries(cardData)) {
      if (key.startsWith('card:')) {
        const code = key.replace('card:', '');
        const cardInfo = JSON.parse(value);
        
        await this.pgPool.query(`
          INSERT INTO cards (code, package_type, status, created_at, used_at, used_by, package_info)
          VALUES ($1, $2, $3, $4, $5, $6, $7)
          ON CONFLICT (code) DO UPDATE SET
            status = EXCLUDED.status,
            used_at = EXCLUDED.used_at,
            used_by = EXCLUDED.used_by
        `, [
          code,
          cardInfo.type || 'M',
          cardInfo.used ? 'used' : 'unused',
          new Date(cardInfo.createdAt || Date.now()),
          cardInfo.usedAt ? new Date(cardInfo.usedAt) : null,
          cardInfo.usedBy || null,
          JSON.stringify(cardInfo)
        ]);
      }
    }
    console.log('卡密数据迁移完成');
  }

  // 迁移语音映射数据
  async migrateVoiceMappings() {
    console.log('开始迁移语音映射数据...');
    const voiceData = await this.fetchKVData(MIGRATION_CONFIG.KV_NAMESPACES.VOICE_MAPPINGS);
    
    if (voiceData['voices_v1']) {
      const mappings = JSON.parse(voiceData['voices_v1']);
      
      for (const [voiceName, voiceId] of Object.entries(mappings)) {
        await this.pgPool.query(`
          INSERT INTO voice_mappings (voice_name, voice_id, model_support)
          VALUES ($1, $2, $3)
          ON CONFLICT (voice_name) DO UPDATE SET
            voice_id = EXCLUDED.voice_id,
            model_support = EXCLUDED.model_support
        `, [
          voiceName,
          voiceId,
          JSON.stringify(['eleven_turbo_v2', 'eleven_turbo_v2_5', 'eleven_v3'])
        ]);
      }
    }
    console.log('语音映射数据迁移完成');
  }

  // 执行完整迁移
  async migrate() {
    try {
      await this.migrateUsers();
      await this.migrateCards();
      await this.migrateVoiceMappings();
      console.log('所有数据迁移完成！');
    } catch (error) {
      console.error('数据迁移失败:', error);
      throw error;
    } finally {
      await this.pgPool.end();
    }
  }
}

// 执行迁移
if (require.main === module) {
  const migrator = new DataMigrator();
  migrator.migrate().catch(console.error);
}

module.exports = DataMigrator;
```

**验证标准：**
```bash
# 执行数据库表创建
psql -h localhost -U tts_app_user -d tts_app_db -f /var/www/tts-app/scripts/create_tables.sql

# 验证表结构
psql -h localhost -U tts_app_user -d tts_app_db -c "\dt"

# 执行数据迁移 (需要先配置Cloudflare API凭据)
cd /var/www/tts-app
npm install pg
export CF_ACCOUNT_ID="your_account_id"
export CF_API_TOKEN="your_api_token"
export DATABASE_URL="postgresql://tts_app_user:TTS_DB_2024_SecurePass!@localhost:5432/tts_app_db"
node scripts/migrate_data.js

# 验证数据迁移结果
psql -h localhost -U tts_app_user -d tts_app_db -c "SELECT COUNT(*) FROM users;"
psql -h localhost -U tts_app_user -d tts_app_db -c "SELECT COUNT(*) FROM cards;"
psql -h localhost -U tts_app_user -d tts_app_db -c "SELECT COUNT(*) FROM voice_mappings;"
```

## 4. 代码迁移指南

### 4.1 核心函数迁移清单

基于参考代码`worker.js`分析，需要迁移以下核心函数：

**认证相关函数：**
- `verifyToken()` - JWT令牌验证
- `checkVip()` - VIP权限检查
- `getAuthConfig()` - 认证配置获取

**TTS处理函数：**
- `splitText()` - 文本分割（支持SSML）
- `processChunks()` - 并发音频生成
- `generateSpeech()` - 单个音频片段生成
- `storeAudioFile()` - 音频文件存储

**业务逻辑函数：**
- `useCard()` - 卡密使用
- `verifyCard()` - 卡密验证
- `updateUserUsage()` - 用户使用量更新

**工具函数：**
- `createLogger()` - 日志系统
- `getTTSProxyConfig()` - 代理配置
- `getVoiceIdMapping()` - 语音映射

### 4.2 项目文件结构

```
/var/www/tts-app/
├── src/
│   ├── app.js                 # Express应用主入口
│   ├── api/                   # API路由
│   │   ├── auth.js           # 认证相关API
│   │   ├── tts.js            # TTS相关API
│   │   ├── user.js           # 用户相关API
│   │   └── admin.js          # 管理员API
│   ├── services/             # 核心服务
│   │   ├── redisClient.js    # Redis客户端
│   │   ├── dbClient.js       # PostgreSQL客户端
│   │   ├── ttsProcessor.js   # TTS处理服务
│   │   ├── websocketManager.js # WebSocket管理
│   │   ├── authService.js    # 认证服务
│   │   └── storageService.js # 存储服务
│   ├── utils/                # 工具函数
│   │   ├── logger.js         # 日志工具
│   │   ├── config.js         # 配置管理
│   │   ├── helpers.js        # 辅助函数
│   │   └── validators.js     # 验证器
│   └── middleware/           # 中间件
│       ├── auth.js           # 认证中间件
│       ├── cors.js           # CORS中间件
│       └── errorHandler.js   # 错误处理中间件
├── config/
│   ├── database.js           # 数据库配置
│   ├── redis.js              # Redis配置
│   └── app.js                # 应用配置
├── scripts/                  # 脚本文件
├── tests/                    # 测试文件
├── logs/                     # 日志文件
├── .env                      # 环境变量
├── package.json              # 项目依赖
└── ecosystem.config.js       # PM2配置
```

### 4.3 核心服务实现

**数据库客户端 (src/services/dbClient.js)：**
```javascript
const { Pool } = require('pg');

class DatabaseClient {
  constructor() {
    this.pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      max: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    });

    this.pool.on('error', (err) => {
      console.error('Unexpected error on idle client', err);
    });
  }

  async query(text, params) {
    const start = Date.now();
    try {
      const res = await this.pool.query(text, params);
      const duration = Date.now() - start;
      if (process.env.DEBUG === 'true') {
        console.log('Executed query', { text, duration, rows: res.rowCount });
      }
      return res;
    } catch (error) {
      console.error('Database query error:', { text, error: error.message });
      throw error;
    }
  }

  async getClient() {
    return await this.pool.connect();
  }

  async end() {
    await this.pool.end();
  }
}

module.exports = new DatabaseClient();
```

**Redis客户端 (src/services/redisClient.js)：**
```javascript
const IORedis = require('ioredis');

class RedisClient {
  constructor() {
    this.client = new IORedis(process.env.REDIS_URL, {
      retryDelayOnFailover: 100,
      enableReadyCheck: false,
      maxRetriesPerRequest: null,
    });

    this.client.on('error', (err) => {
      console.error('Redis Client Error:', err);
    });

    this.client.on('connect', () => {
      console.log('Redis connected');
    });

    this.client.on('ready', () => {
      console.log('Redis ready');
    });
  }

  // 任务状态管理
  async setTaskStatus(taskId, status) {
    const key = `tts:task:${taskId}`;
    await this.client.hset(key, 'status', status, 'updatedAt', Date.now());
    await this.client.expire(key, 24 * 60 * 60); // 24小时过期
  }

  async getTaskStatus(taskId) {
    const key = `tts:task:${taskId}`;
    return await this.client.hgetall(key);
  }

  async setTaskData(taskId, data) {
    const key = `tts:task:${taskId}`;
    const serializedData = {};
    for (const [field, value] of Object.entries(data)) {
      serializedData[field] = typeof value === 'object' ? JSON.stringify(value) : value;
    }
    await this.client.hset(key, serializedData);
    await this.client.expire(key, 24 * 60 * 60);
  }

  // 进度更新发布
  async publishProgress(taskId, progress) {
    await this.client.publish(`tts:progress:${taskId}`, JSON.stringify(progress));
  }

  // 订阅进度更新
  async subscribeProgress(taskId, callback) {
    const subscriber = this.client.duplicate();
    await subscriber.subscribe(`tts:progress:${taskId}`);
    subscriber.on('message', (channel, message) => {
      if (channel === `tts:progress:${taskId}`) {
        callback(JSON.parse(message));
      }
    });
    return subscriber;
  }

  async disconnect() {
    await this.client.disconnect();
  }
}

module.exports = new RedisClient();
```

**WebSocket管理器 (src/services/websocketManager.js)：**
```javascript
const { v4: uuidv4 } = require('uuid');
const redisClient = require('./redisClient');
const ttsProcessor = require('./ttsProcessor');
const { verifyToken } = require('./authService');

class WebSocketManager {
  constructor() {
    this.connections = new Map(); // taskId -> WebSocket连接
  }

  async handleConnection(ws, req) {
    const taskId = uuidv4();

    try {
      // 初始化任务状态
      await redisClient.setTaskData(taskId, {
        status: 'initialized',
        taskId,
        createdAt: Date.now()
      });

      // 存储连接
      this.connections.set(taskId, ws);

      // 发送初始化消息
      ws.send(JSON.stringify({
        type: 'initialized',
        message: 'Connection successful. Task is ready to be started.',
        taskId
      }));

      // 订阅进度更新
      const subscriber = await redisClient.subscribeProgress(taskId, (progress) => {
        if (ws.readyState === ws.OPEN) {
          ws.send(JSON.stringify({
            type: 'progress',
            ...progress
          }));
        }
      });

      // 处理消息
      ws.on('message', async (message) => {
        try {
          const data = JSON.parse(message);
          await this.handleMessage(taskId, data, ws);
        } catch (error) {
          console.error('WebSocket message error:', error);
          ws.send(JSON.stringify({
            type: 'error',
            message: 'Invalid message format'
          }));
        }
      });

      // 处理连接关闭
      ws.on('close', async () => {
        this.connections.delete(taskId);
        await subscriber.disconnect();
        console.log(`WebSocket closed for task ${taskId}`);
      });

      ws.on('error', (error) => {
        console.error(`WebSocket error for task ${taskId}:`, error);
      });

    } catch (error) {
      console.error('WebSocket connection error:', error);
      ws.close(1011, 'Failed to initialize connection');
    }
  }

  async handleMessage(taskId, data, ws) {
    if (data.action === 'start') {
      // 验证token
      try {
        const username = await verifyToken(data.token);

        // 更新任务状态
        await redisClient.setTaskData(taskId, {
          ...data,
          status: 'processing',
          taskId,
          username,
          startedAt: Date.now()
        });

        // 启动TTS处理
        ttsProcessor.start(taskId, data, username).catch(async (error) => {
          console.error(`TTS processing failed for task ${taskId}:`, error);
          await redisClient.setTaskData(taskId, {
            status: 'failed',
            error: error.message,
            failedAt: Date.now()
          });

          if (ws.readyState === ws.OPEN) {
            ws.send(JSON.stringify({
              type: 'error',
              message: error.message
            }));
          }
        });

      } catch (authError) {
        ws.send(JSON.stringify({
          type: 'error',
          message: 'Authentication failed'
        }));
      }
    }
  }

  // 广播消息到特定任务
  async broadcast(taskId, message) {
    const ws = this.connections.get(taskId);
    if (ws && ws.readyState === ws.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }
}

module.exports = new WebSocketManager();
```

**TTS处理服务 (src/services/ttsProcessor.js)：**
```javascript
const redisClient = require('./redisClient');
const { splitText, processChunks, storeAudioFile } = require('../utils/ttsUtils');
const { checkVip, updateUserUsage } = require('./authService');
const { generateDateBasedFilename } = require('../utils/helpers');
const path = require('path');
const fs = require('fs').promises;

class TtsProcessor {
  async start(taskId, taskData, username) {
    try {
      // 发布进度更新
      await this.publishProgress(taskId, '任务初始化...');

      // 检查VIP权限和配额
      const charCount = taskData.input.length;
      await checkVip(username, 'STANDARD', charCount);

      // 文本分割
      await this.publishProgress(taskId, '文本分割中...');
      const chunks = await splitText(taskData.input);
      await this.publishProgress(taskId, `文本已分割为 ${chunks.length} 个片段`);

      // 音频生成
      await this.publishProgress(taskId, `正在生成 ${chunks.length} 个音频片段...`);
      const audioDataList = await processChunks(
        chunks,
        taskData.voice,
        taskData.model || 'eleven_turbo_v2',
        taskData.stability,
        taskData.similarity_boost,
        taskData.style,
        taskData.speed,
        { taskId, username }
      );

      // 音频合并
      await this.publishProgress(taskId, '正在合并音频...');
      const totalLength = audioDataList.reduce((acc, curr) => acc + curr.byteLength, 0);
      const combinedAudioData = new Uint8Array(totalLength);
      let offset = 0;
      for (const audioData of audioDataList) {
        combinedAudioData.set(new Uint8Array(audioData), offset);
        offset += audioData.byteLength;
      }

      // 存储音频文件
      await this.publishProgress(taskId, '正在保存音频文件...');
      const filePath = await this.storeAudioFile(taskId, combinedAudioData.buffer);

      // 更新用户使用量
      await updateUserUsage(username, charCount);

      // 任务完成
      const finalStatus = {
        status: 'complete',
        downloadUrl: `/media/${taskId}.mp3`,
        audioSize: totalLength,
        username: username,
        completedAt: Date.now(),
        taskId: taskId
      };

      await redisClient.setTaskData(taskId, finalStatus);
      await this.publishProgress(taskId, {
        type: 'complete',
        ...finalStatus
      });

      return finalStatus;

    } catch (error) {
      console.error(`TTS processing failed for task ${taskId}:`, error);

      const errorStatus = {
        status: 'failed',
        error: error.message,
        username: username,
        failedAt: Date.now()
      };

      await redisClient.setTaskData(taskId, errorStatus);
      await this.publishProgress(taskId, {
        type: 'error',
        message: error.message
      });

      throw error;
    }
  }

  async publishProgress(taskId, progress) {
    if (typeof progress === 'string') {
      progress = { message: progress };
    }
    await redisClient.publishProgress(taskId, progress);
  }

  async storeAudioFile(taskId, audioBuffer) {
    const fileName = `${taskId}.mp3`;
    const filePath = path.join(process.env.AUDIO_STORAGE_PATH, fileName);

    await fs.writeFile(filePath, Buffer.from(audioBuffer));

    console.log(`Audio file stored: ${filePath}, size: ${audioBuffer.byteLength} bytes`);
    return filePath;
  }
}

module.exports = new TtsProcessor();
```

### 4.4 认证服务迁移

**认证服务 (src/services/authService.js)：**
```javascript
const crypto = require('crypto');
const dbClient = require('./dbClient');

// JWT相关函数 (从worker.js迁移)
async function hmacSha256(data, key) {
  return crypto.createHmac('sha256', key).update(data).digest();
}

function btoa(str) {
  return Buffer.from(str).toString('base64');
}

function atob(str) {
  return Buffer.from(str, 'base64').toString();
}

async function verifyToken(token, allowRefresh = false) {
  const JWT_SECRET = process.env.JWT_SECRET;

  try {
    const [header, payload, signature] = token.split('.');
    const expectedSignature = btoa(
      await hmacSha256(`${header}.${payload}`, JWT_SECRET)
    );

    if (signature !== expectedSignature) {
      throw new Error('Invalid signature');
    }

    const decoded = JSON.parse(atob(payload));

    // 检查 token 类型
    if (!allowRefresh && decoded.type === 'refresh') {
      throw new Error('Invalid token type');
    }

    if (Date.now() > decoded.exp) {
      throw new Error('Token expired');
    }

    return decoded.sub;
  } catch (error) {
    throw new Error('Invalid token');
  }
}

async function checkVip(username, requiredTier = 'STANDARD', requestedChars = 0) {
  const result = await dbClient.query(
    'SELECT vip_info, usage_stats FROM users WHERE username = $1',
    [username]
  );

  if (result.rows.length === 0) {
    throw new Error('用户不存在', { cause: 'quota' });
  }

  const { vip_info: vip, usage_stats: usage } = result.rows[0];

  // 基础检查：是否有会员资格
  if (!vip || Object.keys(vip).length === 0) {
    throw new Error('请先开通会员', { cause: 'quota' });
  }

  // 时间检查：会员是否已过期
  if (Date.now() > vip.expireAt) {
    throw new Error('会员已过期，请续费', { cause: 'quota' });
  }

  // 字符数配额检查 (新规则用户)
  const isNewRuleUser = vip.quotaChars !== undefined;

  if (isNewRuleUser && requestedChars > 0) {
    console.log(`[QUOTA-CHECK] User ${username} is under new quota rule. Checking quota...`);

    const currentUsed = vip.usedChars || 0;
    const totalQuota = vip.quotaChars || 0;

    if (currentUsed + requestedChars > totalQuota) {
      const remaining = Math.max(0, totalQuota - currentUsed);
      throw new Error(`字符数配额不足。剩余 ${remaining} 字符，本次需要 ${requestedChars} 字符。请升级或续费套餐。`, { cause: 'quota' });
    }
  } else if (requestedChars > 0) {
    console.log(`[QUOTA-CHECK] User ${username} is a legacy user. Skipping quota check.`);
  }

  // 等级检查：如果要求PRO权限
  if (requiredTier === 'PRO') {
    const userTier = vip.type;
    if (!userTier || !userTier.startsWith('P')) {
      throw new Error('此功能需要PRO会员权限', { cause: 'quota' });
    }
  }

  // 测试套餐的特殊逻辑
  if (vip.type === 'T') {
    const remainingTime = Math.max(0, vip.expireAt - Date.now()) / 1000;
    if (remainingTime <= 0) {
      throw new Error('测试时间已用完，请充值', { cause: 'quota' });
    }
    console.log(`测试套餐剩余时间: ${remainingTime.toFixed(1)}秒`);
  }
}

async function updateUserUsage(username, charCount) {
  const client = await dbClient.getClient();

  try {
    await client.query('BEGIN');

    // 获取当前用户数据
    const result = await client.query(
      'SELECT vip_info, usage_stats FROM users WHERE username = $1 FOR UPDATE',
      [username]
    );

    if (result.rows.length === 0) {
      throw new Error('用户不存在');
    }

    const { vip_info: vip, usage_stats: usage } = result.rows[0];

    // 更新VIP使用量 (如果是新规则用户)
    if (vip.quotaChars !== undefined) {
      vip.usedChars = (vip.usedChars || 0) + charCount;
    }

    // 更新使用统计
    usage.totalChars = (usage.totalChars || 0) + charCount;
    usage.monthlyChars = (usage.monthlyChars || 0) + charCount;

    // 检查月度重置
    const now = Date.now();
    if (now >= (usage.monthlyResetAt || 0)) {
      usage.monthlyChars = charCount;
      usage.monthlyResetAt = getNextMonthResetTimestamp();
    }

    // 更新数据库
    await client.query(
      'UPDATE users SET vip_info = $1, usage_stats = $2, updated_at = CURRENT_TIMESTAMP WHERE username = $3',
      [JSON.stringify(vip), JSON.stringify(usage), username]
    );

    await client.query('COMMIT');
    console.log(`Updated usage for user ${username}: +${charCount} chars`);

  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

function getNextMonthResetTimestamp() {
  const now = new Date();
  const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
  return nextMonth.getTime();
}

// 【重要遗漏】卡密验证函数
async function verifyCard(code) {
  const result = await dbClient.query(
    'SELECT * FROM cards WHERE code = $1',
    [code]
  );

  if (result.rows.length === 0) {
    return null;
  }

  const card = result.rows[0];

  // 检查卡密状态
  if (card.status !== 'unused') {
    return null;
  }

  return {
    code: card.code,
    type: card.package_type,
    packageInfo: card.package_info
  };
}

// 【重要遗漏】卡密使用函数
async function useCard(code, username) {
  const client = await dbClient.getClient();

  try {
    await client.query('BEGIN');

    // 获取卡密信息
    const cardResult = await client.query(
      'SELECT * FROM cards WHERE code = $1 FOR UPDATE',
      [code]
    );

    if (cardResult.rows.length === 0) {
      throw new Error('卡密不存在');
    }

    const card = cardResult.rows[0];

    if (card.status !== 'unused') {
      throw new Error('卡密已被使用或已过期');
    }

    // 获取用户信息
    const userResult = await client.query(
      'SELECT * FROM users WHERE username = $1 FOR UPDATE',
      [username]
    );

    if (userResult.rows.length === 0) {
      throw new Error('用户不存在');
    }

    const userData = userResult.rows[0];
    const vip = userData.vip_info || {};
    const packageInfo = card.package_info;

    // 计算新的过期时间和配额
    const now = Date.now();
    let newExpireAt = Math.max(now, vip.expireAt || 0) + (packageInfo.duration || 0);
    let newQuotaChars = (vip.quotaChars || 0) + (packageInfo.quotaChars || 0);

    // 更新VIP信息
    const updatedVip = {
      ...vip,
      type: packageInfo.type || card.package_type,
      expireAt: newExpireAt,
      quotaChars: newQuotaChars,
      usedChars: vip.usedChars || 0
    };

    // 更新用户数据
    await client.query(
      'UPDATE users SET vip_info = $1, updated_at = CURRENT_TIMESTAMP WHERE username = $2',
      [JSON.stringify(updatedVip), username]
    );

    // 标记卡密为已使用
    await client.query(
      'UPDATE cards SET status = $1, used_at = CURRENT_TIMESTAMP, used_by = $2 WHERE code = $3',
      ['used', username, code]
    );

    await client.query('COMMIT');

    console.log(`Card ${code} used successfully by ${username}`);
    return updatedVip;

  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

// 【重要遗漏】密码加密函数（从worker.js迁移）
async function bcrypt(password) {
  const crypto = require('crypto');
  const data = password + process.env.JWT_SECRET;
  const hash = crypto.createHash('sha256').update(data).digest();
  return Buffer.from(hash).toString('base64');
}

// 【重要遗漏】生成验证码
function generateVerificationCode() {
  return Math.floor(100000 + Math.random() * 900000).toString(); // 6位数字验证码
}

// 【重要遗漏】邮件发送相关函数
async function sendEmailViaTencentSES(toEmail, templateData) {
  // 简化版本，实际应该实现完整的腾讯云SES API调用
  console.log(`Sending email to ${toEmail} with template data:`, templateData);

  // 这里应该实现完整的腾讯云SES API调用逻辑
  // 包括签名生成、API请求等
  // 为了简化，这里只是记录日志

  return { success: true, messageId: 'mock_message_id' };
}

// 【重要遗漏】存储验证码
async function storeVerificationCode(email, code) {
  const dbClient = require('./dbClient');

  const verificationData = {
    code,
    expireTime: Date.now() + 10 * 60 * 1000, // 10分钟过期
    attempts: 0,
    maxAttempts: 5
  };

  // 使用PostgreSQL存储验证码（替代KV）
  await dbClient.query(
    'INSERT INTO verification_codes (email, code, expire_time, attempts, max_attempts) VALUES ($1, $2, $3, $4, $5) ON CONFLICT (email) DO UPDATE SET code = $2, expire_time = $3, attempts = 0',
    [email, code, new Date(verificationData.expireTime), verificationData.attempts, verificationData.maxAttempts]
  );
}

// 【重要遗漏】验证邮箱验证码
async function verifyEmailCode(email, inputCode) {
  const dbClient = require('./dbClient');

  const result = await dbClient.query(
    'SELECT * FROM verification_codes WHERE email = $1',
    [email]
  );

  if (result.rows.length === 0) {
    throw new Error('验证码不存在或已过期');
  }

  const data = result.rows[0];

  // 检查是否过期
  if (Date.now() > data.expire_time.getTime()) {
    await dbClient.query('DELETE FROM verification_codes WHERE email = $1', [email]);
    throw new Error('验证码已过期');
  }

  // 检查尝试次数
  if (data.attempts >= data.max_attempts) {
    await dbClient.query('DELETE FROM verification_codes WHERE email = $1', [email]);
    throw new Error('验证码尝试次数过多，请重新获取');
  }

  // 验证码错误，增加尝试次数
  if (data.code !== inputCode) {
    await dbClient.query(
      'UPDATE verification_codes SET attempts = attempts + 1 WHERE email = $1',
      [email]
    );
    throw new Error('验证码错误');
  }

  // 验证成功，删除验证码
  await dbClient.query('DELETE FROM verification_codes WHERE email = $1', [email]);
  return true;
}

module.exports = {
  verifyToken,
  checkVip,
  updateUserUsage,
  verifyCard,
  useCard,
  bcrypt,
  generateVerificationCode,
  sendEmailViaTencentSES,
  storeVerificationCode,
  verifyEmailCode,
  hmacSha256,
  btoa,
  atob
};
```

**【遗漏补充】TTS工具函数 (src/utils/ttsUtils.js)：**
```javascript
const crypto = require('crypto');

// 【重要】从worker.js迁移的核心函数
function generateUUID() {
  return crypto.randomUUID();
}

// 文本分割函数（支持SSML）
async function splitText(text) {
  // 简化版本，实际应该从worker.js完整迁移splitText函数
  const maxChunkSize = 2500; // 字符
  const chunks = [];

  if (text.length <= maxChunkSize) {
    return [text];
  }

  // 按句号、问号、感叹号分割
  const sentences = text.split(/([.!?。！？])/);
  let currentChunk = '';

  for (let i = 0; i < sentences.length; i += 2) {
    const sentence = sentences[i] + (sentences[i + 1] || '');

    if (currentChunk.length + sentence.length > maxChunkSize) {
      if (currentChunk) {
        chunks.push(currentChunk.trim());
        currentChunk = sentence;
      } else {
        // 单个句子太长，强制分割
        chunks.push(sentence.substring(0, maxChunkSize));
        currentChunk = sentence.substring(maxChunkSize);
      }
    } else {
      currentChunk += sentence;
    }
  }

  if (currentChunk.trim()) {
    chunks.push(currentChunk.trim());
  }

  return chunks;
}

// 获取语音ID映射
async function getVoiceIdMapping(voiceName, dbClient) {
  try {
    const result = await dbClient.query(
      'SELECT voice_id FROM voice_mappings WHERE voice_name = $1',
      [voiceName]
    );

    return result.rows[0]?.voice_id || voiceName;
  } catch (error) {
    console.error('Failed to get voice mapping:', error);
    return voiceName; // 回退到原始名称
  }
}

// 生成基于日期的文件名
function generateDateBasedFilename(taskId, extension = 'mp3') {
  const now = new Date();
  const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD
  return `${dateStr}_${taskId}.${extension}`;
}

// 获取下个月重置时间戳
function getNextMonthResetTimestamp() {
  const now = new Date();
  const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
  return nextMonth.getTime();
}

// 【重要】并发音频生成函数（从worker.js迁移）
async function processChunks(chunks, voiceId, modelId, stability, similarity_boost, style, speed, context = {}) {
  const pLimit = require('p-limit');

  // 动态并发控制
  const optimalConcurrency = Math.min(4, chunks.length); // Ubuntu环境下保守设置
  const limiter = pLimit(optimalConcurrency);

  console.log(`Processing ${chunks.length} chunks with ${optimalConcurrency} concurrency`);

  // 并发处理所有chunks
  const promises = chunks.map((chunk, index) =>
    limiter(async () => {
      try {
        console.log(`Processing chunk ${index + 1}/${chunks.length}, length: ${chunk.length}`);

        // 调用ElevenLabs API生成音频
        const audioBuffer = await generateSpeech(chunk, voiceId, modelId, stability, similarity_boost, style, speed);

        return {
          index,
          success: true,
          audioData: audioBuffer,
          chunk
        };
      } catch (error) {
        console.error(`Chunk ${index + 1} failed:`, error.message);
        return {
          index,
          success: false,
          error: error.message,
          chunk
        };
      }
    })
  );

  const results = await Promise.all(promises);

  // 检查失败的chunks
  const failedResults = results.filter(r => !r.success);
  if (failedResults.length > 0) {
    console.warn(`${failedResults.length} chunks failed, attempting retry...`);

    // 简单重试逻辑
    for (const failedResult of failedResults) {
      try {
        await new Promise(resolve => setTimeout(resolve, 2000)); // 2秒延迟
        const audioBuffer = await generateSpeech(
          failedResult.chunk, voiceId, modelId, stability, similarity_boost, style, speed
        );

        results[failedResult.index] = {
          ...failedResult,
          success: true,
          audioData: audioBuffer
        };
        console.log(`Retry successful for chunk ${failedResult.index + 1}`);
      } catch (retryError) {
        console.error(`Retry failed for chunk ${failedResult.index + 1}:`, retryError.message);
        throw new Error(`音频生成失败: ${retryError.message}`);
      }
    }
  }

  // 返回按顺序排列的音频数据
  return results
    .sort((a, b) => a.index - b.index)
    .map(r => r.audioData);
}

// 【重要】单个音频生成函数（调用ElevenLabs API）
async function generateSpeech(text, voiceId, modelId, stability, similarity_boost, style, speed) {
  const url = `https://api.elevenlabs.io/v1/text-to-speech/${voiceId}`;

  const payload = {
    text,
    model_id: modelId || 'eleven_turbo_v2',
    voice_settings: {
      stability: stability || 0.5,
      similarity_boost: similarity_boost || 0.75,
      style: style || 0,
      use_speaker_boost: true
    }
  };

  if (speed !== undefined) {
    payload.voice_settings.speed = speed;
  }

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Accept': 'audio/mpeg',
      'Content-Type': 'application/json',
      'xi-api-key': process.env.ELEVENLABS_API_KEY
    },
    body: JSON.stringify(payload)
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`ElevenLabs API error: ${response.status} ${errorText}`);
  }

  return await response.arrayBuffer();
}

module.exports = {
  generateUUID,
  splitText,
  getVoiceIdMapping,
  generateDateBasedFilename,
  getNextMonthResetTimestamp,
  processChunks,
  generateSpeech
};
```

## 5. 配置管理

### 5.1 完整环境变量清单

基于参考代码分析，创建完整的`.env`配置文件。

**注意：以下配置已移除Cloudflare特有功能**
- ❌ 已移除：`ENABLE_DO_LOCATION_HINT`（Durable Object位置提示）
- ❌ 已移除：`DO_ANALYTICS`、`PROXY_ANALYTICS`（Analytics Engine）
- ❌ 已移除：`R2_PATH_PREFIX`、`R2_DIRECT_DOMAIN`（R2存储配置）
- ✅ 替换：`TTS_R2_STORAGE_TIMEOUT` → `TTS_FILE_STORAGE_TIMEOUT`

```bash
# /var/www/tts-app/.env

# ========== 基础配置 ==========
NODE_ENV=production
PORT=3000
DEBUG=false

# ========== 数据库配置 ==========
DATABASE_URL="postgresql://tts_app_user:TTS_DB_2024_SecurePass!@localhost:5432/tts_app_db"
REDIS_URL="redis://localhost:6379"

# ========== JWT认证配置 ==========
JWT_SECRET="X8k9#mP2$vL5nQ7@jR3wY6*tZ4uH9kL3mN6pQ8rS1tV4wX7zA2bC5dE8fG1hI4jK"
ACCESS_TOKEN_EXPIRE=7200
REFRESH_TOKEN_EXPIRE=604800
SALT_ROUNDS=10

# ========== 管理员配置 ==========
ADMIN_USERS="admin1,admin2"

# ========== ElevenLabs API配置 ==========
ELEVENLABS_API_KEY="your_elevenlabs_api_key"

# ========== 腾讯云SES邮件配置 ==========
TENCENT_SECRET="your_tencent_secret_id"
TENCENT_SECRET_KEY="your_tencent_secret_key"
SES_REGION="ap-guangzhou"
FROM_EMAIL="<EMAIL>"
FROM_EMAIL_NAME="TTS Service"
VERIFICATION_TEMPLATE_ID="your_template_id"

# ========== 文件存储配置 ==========
AUDIO_STORAGE_PATH="/var/data/tts-app/audios"

# ========== TTS代理配置 ==========
ENABLE_TTS_PROXY=true
TTS_PROXY_URLS="https://proxy1.example.com,https://proxy2.example.com"
TTS_PROXY_SECRET="your_proxy_secret"
TTS_PROXY_MODE="fallback"
TTS_PROXY_TIMEOUT=45000
TTS_PROXY_RETRY_COUNT=2
TTS_PROXY_BALANCE_RATIO=0.3
TTS_FALLBACK_THRESHOLD=2
TTS_FALLBACK_WINDOW=300

# ========== 日志配置 ==========
LOG_DIR="/var/log/tts-app"

# ========== 集群级重试配置 ==========
TTS_CLUSTER_RETRY_COUNT=3
TTS_CLUSTER_MAX_DELAY=8000
TTS_SINGLE_MAX_DELAY=5000
TTS_DIRECT_MAX_DELAY=8000
TTS_ENABLE_BACKOFF=true
TTS_PROXY_SELECTION_STRATEGY="sequential"

# ========== 任务级重试配置 ==========
ENABLE_TASK_RETRY=true
MAX_TASK_RETRIES=2
TASK_RETRY_DELAY_1=6000
TASK_RETRY_DELAY_2=12000
TASK_ABSOLUTE_TIMEOUT=600000
ENABLE_TASK_RETRY_DEBUG=false

# ========== 智能超时配置 ==========
TTS_INIT_TIMEOUT=30000
TTS_TEXT_PROCESSING_TIMEOUT=60000
TTS_AUDIO_MERGING_TIMEOUT=120000
TTS_FILE_STORAGE_TIMEOUT=60000
TTS_DEFAULT_TIMEOUT=300000
TTS_CHUNK_TIMEOUT=40000
TTS_MIN_TIMEOUT=120000
TTS_MAX_TIMEOUT=900000
TTS_ENABLE_COMPLEXITY_ADJUSTMENT=true
TTS_LARGE_CHUNK_THRESHOLD=10
TTS_HUGE_CHUNK_THRESHOLD=20
TTS_LARGE_TEXT_THRESHOLD=5000
TTS_HUGE_TEXT_THRESHOLD=10000
TTS_ENABLE_TIMEOUT_DEBUG=false

# ========== 进度消息配置 ==========
ENABLE_PROGRESS_MESSAGES=true
ENABLE_DEBUG_PROGRESS=false

# ========== 代理统计配置 ==========
ENABLE_PROXY_STATS=true
ENABLE_PROXY_DEBUG=false
```

### 5.2 应用配置文件

**主应用入口 (src/app.js)：**
```javascript
require('dotenv').config();
const express = require('express');
const enableWs = require('express-ws');
const cors = require('cors');
const path = require('path');

// 导入服务和中间件
const websocketManager = require('./services/websocketManager');
const authRoutes = require('./api/auth');
const ttsRoutes = require('./api/tts');
const userRoutes = require('./api/user');
const adminRoutes = require('./api/admin');
const corsMiddleware = require('./middleware/cors');
const errorHandler = require('./middleware/errorHandler');

const app = express();

// 启用WebSocket支持
enableWs(app);

// 中间件配置
app.use(corsMiddleware);
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// 静态文件服务 (用于健康检查等)
app.use('/health', express.static(path.join(__dirname, '../public')));

// WebSocket路由
app.ws('/api/tts/ws/generate', (ws, req) => {
  websocketManager.handleConnection(ws, req);
});

// API路由
app.use('/api/auth', authRoutes);
app.use('/api/tts', ttsRoutes);
app.use('/api/user', userRoutes);
app.use('/api/admin', adminRoutes);

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.env.npm_package_version || '1.0.0'
  });
});

// 错误处理中间件
app.use(errorHandler);

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.originalUrl} not found`
  });
});

const PORT = process.env.PORT || 3000;

// 优雅关闭处理
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully');

  // 关闭数据库连接
  const dbClient = require('./services/dbClient');
  const redisClient = require('./services/redisClient');

  await dbClient.end();
  await redisClient.disconnect();

  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('SIGINT received, shutting down gracefully');

  const dbClient = require('./services/dbClient');
  const redisClient = require('./services/redisClient');

  await dbClient.end();
  await redisClient.disconnect();

  process.exit(0);
});

if (require.main === module) {
  app.listen(PORT, () => {
    console.log(`TTS Application running on port ${PORT}`);
    console.log(`Environment: ${process.env.NODE_ENV}`);
    console.log(`WebSocket endpoint: ws://localhost:${PORT}/api/tts/ws/generate`);
  });
}

module.exports = app;
```

**【遗漏补充】API路由实现 (src/api/user.js)：**
```javascript
const express = require('express');
const router = express.Router();
const { verifyToken, verifyCard, useCard } = require('../services/authService');
const dbClient = require('../services/dbClient');

// 获取用户配额信息
router.get('/quota', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    if (!token) {
      return res.status(401).json({ error: 'Token required' });
    }

    const username = await verifyToken(token);
    const result = await dbClient.query(
      'SELECT vip_info, usage_stats FROM users WHERE username = $1',
      [username]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    const { vip_info: vip, usage_stats: usage } = result.rows[0];

    res.json({
      quotaChars: vip?.quotaChars || 0,
      usedChars: vip?.usedChars || 0,
      remainingChars: Math.max(0, (vip?.quotaChars || 0) - (vip?.usedChars || 0)),
      expireAt: vip?.expireAt || 0,
      type: vip?.type || null,
      monthlyChars: usage?.monthlyChars || 0,
      totalChars: usage?.totalChars || 0
    });
  } catch (error) {
    console.error('Get quota error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 使用卡密
router.post('/use-card', async (req, res) => {
  try {
    const { code } = req.body;
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({ error: 'Token required' });
    }

    if (!code) {
      return res.status(400).json({ error: 'Card code required' });
    }

    const username = await verifyToken(token);

    // 验证卡密
    const card = await verifyCard(code);
    if (!card) {
      return res.status(400).json({ error: '卡密无效或已被使用' });
    }

    // 使用卡密
    const updatedVip = await useCard(code, username);

    res.json({
      success: true,
      message: '卡密使用成功',
      vip: updatedVip
    });
  } catch (error) {
    console.error('Use card error:', error);
    res.status(400).json({ error: error.message });
  }
});

module.exports = router;
```

**【遗漏补充】TTS API路由 (src/api/tts.js)：**
```javascript
const express = require('express');
const router = express.Router();
const { verifyToken } = require('../services/authService');
const redisClient = require('../services/redisClient');
const path = require('path');
const fs = require('fs');

// 获取任务状态
router.get('/status/:taskId', async (req, res) => {
  try {
    const { taskId } = req.params;
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({ error: 'Token required' });
    }

    await verifyToken(token); // 验证token

    const taskData = await redisClient.getTaskStatus(taskId);

    if (!taskData || Object.keys(taskData).length === 0) {
      return res.status(404).json({ error: 'Task not found' });
    }

    res.json({
      taskId,
      status: taskData.status,
      progress: taskData.progress || '',
      downloadUrl: taskData.downloadUrl || null,
      error: taskData.error || null,
      createdAt: taskData.createdAt ? parseInt(taskData.createdAt) : null,
      completedAt: taskData.completedAt ? parseInt(taskData.completedAt) : null
    });
  } catch (error) {
    console.error('Get status error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 下载音频文件
router.get('/download/:taskId', async (req, res) => {
  try {
    const { taskId } = req.params;
    const token = req.query.token || req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({ error: 'Token required' });
    }

    await verifyToken(token); // 验证token

    const filePath = path.join(process.env.AUDIO_STORAGE_PATH, `${taskId}.mp3`);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: 'Audio file not found' });
    }

    const stat = fs.statSync(filePath);

    res.setHeader('Content-Type', 'audio/mpeg');
    res.setHeader('Content-Length', stat.size);
    res.setHeader('Content-Disposition', `attachment; filename="${taskId}.mp3"`);
    res.setHeader('Accept-Ranges', 'bytes');

    // 支持Range请求（断点续传）
    const range = req.headers.range;
    if (range) {
      const parts = range.replace(/bytes=/, "").split("-");
      const start = parseInt(parts[0], 10);
      const end = parts[1] ? parseInt(parts[1], 10) : stat.size - 1;
      const chunksize = (end - start) + 1;

      res.status(206);
      res.setHeader('Content-Range', `bytes ${start}-${end}/${stat.size}`);
      res.setHeader('Content-Length', chunksize);

      const stream = fs.createReadStream(filePath, { start, end });
      stream.pipe(res);
    } else {
      const stream = fs.createReadStream(filePath);
      stream.pipe(res);
    }
  } catch (error) {
    console.error('Download error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
```

**【遗漏补充】认证API路由 (src/api/auth.js)：**
```javascript
const express = require('express');
const router = express.Router();
const { verifyToken, bcrypt, generateVerificationCode, sendEmailViaTencentSES, storeVerificationCode, verifyEmailCode } = require('../services/authService');
const dbClient = require('../services/dbClient');

// 用户登录
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    // 支持邮箱登录
    let actualUsername = username;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (emailRegex.test(username)) {
      const result = await dbClient.query(
        'SELECT username FROM users WHERE email = $1',
        [username]
      );

      if (result.rows.length === 0) {
        return res.status(400).json({ error: '用户名或密码错误' });
      }

      actualUsername = result.rows[0].username;
    }

    // 验证用户密码
    const result = await dbClient.query(
      'SELECT * FROM users WHERE username = $1',
      [actualUsername]
    );

    if (result.rows.length === 0) {
      return res.status(400).json({ error: '用户名或密码错误' });
    }

    const user = result.rows[0];
    const hashedPassword = await bcrypt(password);

    if (hashedPassword !== user.password_hash) {
      return res.status(400).json({ error: '用户名或密码错误' });
    }

    // 生成JWT token（需要实现完整的JWT生成逻辑）
    const accessToken = 'generated_access_token';
    const refreshToken = 'generated_refresh_token';

    res.json({
      access_token: accessToken,
      refresh_token: refreshToken,
      expires_in: 7200
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 发送邮箱验证码
router.post('/send-verification', async (req, res) => {
  try {
    const { email, username, password } = req.body;

    // 检查用户名和邮箱是否已存在
    const existingUser = await dbClient.query(
      'SELECT id FROM users WHERE username = $1 OR email = $2',
      [username, email]
    );

    if (existingUser.rows.length > 0) {
      return res.status(400).json({ error: '用户名或邮箱已存在' });
    }

    // 生成并发送验证码
    const verificationCode = generateVerificationCode();
    await storeVerificationCode(email, verificationCode);

    const templateData = { code: verificationCode, username };
    await sendEmailViaTencentSES(email, templateData);

    res.json({
      message: '验证码已发送到您的邮箱，请查收',
      email: email
    });
  } catch (error) {
    console.error('Send verification error:', error);
    res.status(500).json({ error: error.message || '发送验证码失败' });
  }
});

// 忘记密码
router.post('/forgot-password', async (req, res) => {
  try {
    const { email } = req.body;

    const result = await dbClient.query(
      'SELECT username FROM users WHERE email = $1',
      [email]
    );

    if (result.rows.length === 0) {
      return res.status(400).json({ error: '该邮箱未注册' });
    }

    const verificationCode = generateVerificationCode();
    await storeVerificationCode(email, verificationCode);

    const templateData = { code: verificationCode, username: result.rows[0].username };
    await sendEmailViaTencentSES(email, templateData);

    res.json({
      message: '重置密码验证码已发送到您的邮箱，请查收',
      email: email
    });
  } catch (error) {
    console.error('Forgot password error:', error);
    res.status(500).json({ error: error.message || '发送验证码失败' });
  }
});

module.exports = router;
```

### 5.3 包依赖配置

**package.json：**
```json
{
  "name": "tts-app",
  "version": "1.0.0",
  "description": "TTS Application - Migrated from Cloudflare Worker",
  "main": "src/app.js",
  "scripts": {
    "start": "node src/app.js",
    "dev": "nodemon src/app.js",
    "test": "jest",
    "migrate": "node scripts/migrate_data.js",
    "setup-db": "psql -h localhost -U tts_app_user -d tts_app_db -f scripts/create_tables.sql"
  },
  "dependencies": {
    "express": "^4.18.2",
    "express-ws": "^5.0.2",
    "cors": "^2.8.5",
    "dotenv": "^16.3.1",
    "ioredis": "^5.3.2",
    "pg": "^8.11.3",
    "uuid": "^9.0.1",
    "bcrypt": "^5.1.1",
    "jsonwebtoken": "^9.0.2",
    "multer": "^1.4.5-lts.1",
    "helmet": "^7.0.0",
    "compression": "^1.7.4",
    "morgan": "^1.10.0"
  },
  "devDependencies": {
    "nodemon": "^3.0.1",
    "jest": "^29.7.0",
    "supertest": "^6.3.3",
    "@types/node": "^20.5.0"
  },
  "engines": {
    "node": ">=18.0.0",
    "npm": ">=9.0.0"
  },
  "keywords": [
    "tts",
    "text-to-speech",
    "elevenlabs",
    "nodejs",
    "express",
    "websocket"
  ],
  "author": "TTS Team",
  "license": "MIT"
}
```

## 6. 服务部署

### 6.1 PM2配置

**ecosystem.config.js：**
```javascript
module.exports = {
  apps: [
    {
      name: 'tts-app',
      script: 'src/app.js',
      instances: 'max', // 使用所有CPU核心
      exec_mode: 'cluster', // 集群模式
      autorestart: true,
      watch: false, // 生产环境不启用文件监控
      max_memory_restart: '1G', // 内存超过1GB时重启

      // 环境变量
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },

      // 日志配置
      log_file: '/var/log/tts-app/combined.log',
      out_file: '/var/log/tts-app/out.log',
      error_file: '/var/log/tts-app/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',

      // 进程管理
      min_uptime: '10s',
      max_restarts: 10,

      // 集群配置
      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 10000,

      // 健康检查
      health_check_grace_period: 3000
    }
  ],

  // 部署配置
  deploy: {
    production: {
      user: 'deploy',
      host: 'your-server.com',
      ref: 'origin/main',
      repo: '**************:your-org/tts-app.git',
      path: '/var/www/tts-app',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env production'
    }
  }
};
```

### 6.2 Nginx配置

**Nginx站点配置 (/etc/nginx/sites-available/tts-app)：**
```nginx
# 上游服务器配置
upstream tts_app {
    least_conn;
    server 127.0.0.1:3000 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

# 限流配置
limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=ws_limit:10m rate=5r/s;

server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # 日志配置
    access_log /var/log/nginx/tts-app.access.log;
    error_log /var/log/nginx/tts-app.error.log;

    # 客户端配置
    client_max_body_size 10M;
    client_body_timeout 60s;
    client_header_timeout 60s;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml;

    # API路由 (HTTP + WebSocket)
    location /api {
        # 限流
        limit_req zone=api_limit burst=20 nodelay;

        # WebSocket特殊处理
        if ($request_uri ~* "/api/tts/ws/") {
            limit_req zone=ws_limit burst=10 nodelay;
        }

        # 代理配置
        proxy_pass http://tts_app;
        proxy_http_version 1.1;

        # WebSocket支持
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;

        # 标准代理头
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s; # TTS任务可能需要较长时间

        # 缓存控制
        proxy_cache_bypass $http_upgrade;
        proxy_no_cache $http_upgrade;
    }

    # 音频文件服务
    location /media {
        alias /var/data/tts-app/audios;

        # 缓存配置
        expires 7d;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options "nosniff";

        # 安全配置
        location ~* \.(mp3|wav|ogg)$ {
            add_header Content-Disposition "attachment";
            add_header X-Robots-Tag "noindex, nofollow";
        }

        # 防止直接访问其他文件
        location ~* \.(php|pl|py|jsp|asp|sh|cgi)$ {
            deny all;
        }
    }

    # 健康检查
    location /health {
        proxy_pass http://tts_app;
        proxy_set_header Host $host;
        access_log off;
    }

    # 静态资源 (如果有)
    location /static {
        alias /var/www/tts-app/public;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }

    # 拒绝访问敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ \.(env|log|sql)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}

# WebSocket连接升级映射
map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}
```

### 6.3 服务启动脚本

**启动脚本 (scripts/deploy.sh)：**
```bash
#!/bin/bash

# TTS应用部署脚本
set -e

echo "=== TTS应用部署开始 ==="

# 检查环境
if [ "$NODE_ENV" != "production" ]; then
    echo "警告: NODE_ENV不是production环境"
fi

# 进入项目目录
cd /var/www/tts-app

# 安装依赖
echo "安装Node.js依赖..."
npm ci --only=production

# 创建必要目录
echo "创建必要目录..."
sudo mkdir -p /var/data/tts-app/audios
sudo mkdir -p /var/log/tts-app
sudo chown -R www-data:www-data /var/data/tts-app
sudo chown -R $USER:$USER /var/log/tts-app

# 数据库迁移 (如果需要)
echo "检查数据库连接..."
if ! psql -h localhost -U tts_app_user -d tts_app_db -c "SELECT 1;" > /dev/null 2>&1; then
    echo "错误: 无法连接到数据库"
    exit 1
fi

# Redis连接检查
echo "检查Redis连接..."
if ! redis-cli ping > /dev/null 2>&1; then
    echo "错误: 无法连接到Redis"
    exit 1
fi

# 配置Nginx
echo "配置Nginx..."
sudo cp /var/www/tts-app/config/nginx.conf /etc/nginx/sites-available/tts-app
sudo ln -sf /etc/nginx/sites-available/tts-app /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx

# 启动应用
echo "启动应用..."
pm2 delete tts-app 2>/dev/null || true
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup

# 验证部署
echo "验证部署..."
sleep 5

# 检查PM2状态
if ! pm2 list | grep -q "tts-app.*online"; then
    echo "错误: 应用启动失败"
    pm2 logs tts-app --lines 20
    exit 1
fi

# 检查健康状态
if ! curl -f http://localhost:3000/health > /dev/null 2>&1; then
    echo "错误: 健康检查失败"
    exit 1
fi

echo "=== TTS应用部署完成 ==="
echo "应用状态: $(pm2 list | grep tts-app)"
echo "访问地址: http://yourdomain.com"
echo "WebSocket: ws://yourdomain.com/api/tts/ws/generate"
```

**验证标准：**
```bash
# 使脚本可执行
chmod +x /var/www/tts-app/scripts/deploy.sh

# 执行部署
cd /var/www/tts-app
./scripts/deploy.sh

# 验证服务状态
pm2 status
sudo systemctl status nginx
sudo systemctl status postgresql
sudo systemctl status redis-server

# 验证端口监听
netstat -tlnp | grep :3000
netstat -tlnp | grep :80

# 验证健康检查
curl http://localhost/health
curl http://localhost:3000/health
```

## 7. 测试验证

### 7.1 功能对等性测试计划

**测试环境准备：**
```bash
# 安装测试依赖
cd /var/www/tts-app
npm install --save-dev jest supertest ws

# 创建测试配置
cat > jest.config.js << EOF
module.exports = {
  testEnvironment: 'node',
  testMatch: ['**/tests/**/*.test.js'],
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/app.js'
  ],
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js']
};
EOF
```

**核心功能测试 (tests/integration/tts.test.js)：**
```javascript
const request = require('supertest');
const WebSocket = require('ws');
const app = require('../../src/app');

describe('TTS功能测试', () => {
  let authToken;

  beforeAll(async () => {
    // 获取测试用户的认证token
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        username: 'testuser',
        password: 'testpass'
      });

    authToken = loginResponse.body.token;
  });

  test('健康检查', async () => {
    const response = await request(app)
      .get('/health')
      .expect(200);

    expect(response.body.status).toBe('healthy');
  });

  test('WebSocket连接建立', (done) => {
    const ws = new WebSocket('ws://localhost:3000/api/tts/ws/generate');

    ws.on('open', () => {
      ws.close();
      done();
    });

    ws.on('error', done);
  });

  test('TTS任务完整流程', (done) => {
    const ws = new WebSocket('ws://localhost:3000/api/tts/ws/generate');
    let taskId;

    ws.on('message', (data) => {
      const message = JSON.parse(data);

      if (message.type === 'initialized') {
        taskId = message.taskId;
        expect(taskId).toBeDefined();

        // 发送TTS任务
        ws.send(JSON.stringify({
          action: 'start',
          token: authToken,
          input: '这是一个测试文本',
          voice: 'Adam',
          model: 'eleven_turbo_v2',
          stability: 0.5,
          similarity_boost: 0.75
        }));
      } else if (message.type === 'complete') {
        expect(message.downloadUrl).toBeDefined();
        expect(message.audioSize).toBeGreaterThan(0);
        ws.close();
        done();
      } else if (message.type === 'error') {
        done(new Error(message.message));
      }
    });

    ws.on('error', done);
  }, 30000); // 30秒超时

  test('用户配额检查', async () => {
    const response = await request(app)
      .get('/api/user/quota')
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    expect(response.body).toHaveProperty('quotaChars');
    expect(response.body).toHaveProperty('usedChars');
  });

  test('任务状态查询', async () => {
    // 这里需要一个已知的taskId
    const taskId = 'test-task-id';

    const response = await request(app)
      .get(`/api/tts/status/${taskId}`)
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    expect(response.body).toHaveProperty('status');
  });
});
```

### 7.2 性能测试

**负载测试脚本 (tests/load/load-test.js)：**
```javascript
const WebSocket = require('ws');
const { performance } = require('perf_hooks');

class LoadTester {
  constructor(concurrency = 10, duration = 60000) {
    this.concurrency = concurrency;
    this.duration = duration;
    this.results = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      minResponseTime: Infinity,
      maxResponseTime: 0
    };
  }

  async runTest() {
    console.log(`开始负载测试: ${this.concurrency}并发, ${this.duration/1000}秒`);

    const promises = [];
    const startTime = Date.now();

    for (let i = 0; i < this.concurrency; i++) {
      promises.push(this.runWorker(startTime));
    }

    await Promise.all(promises);

    console.log('负载测试结果:', this.results);
    return this.results;
  }

  async runWorker(startTime) {
    while (Date.now() - startTime < this.duration) {
      try {
        const requestStart = performance.now();
        await this.performTTSRequest();
        const requestEnd = performance.now();

        const responseTime = requestEnd - requestStart;
        this.updateStats(responseTime, true);

        // 短暂休息避免过度负载
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        this.updateStats(0, false);
        console.error('请求失败:', error.message);
      }
    }
  }

  performTTSRequest() {
    return new Promise((resolve, reject) => {
      const ws = new WebSocket('ws://localhost:3000/api/tts/ws/generate');
      const timeout = setTimeout(() => {
        ws.close();
        reject(new Error('请求超时'));
      }, 30000);

      ws.on('message', (data) => {
        const message = JSON.parse(data);

        if (message.type === 'initialized') {
          ws.send(JSON.stringify({
            action: 'start',
            token: process.env.TEST_TOKEN,
            input: '这是负载测试文本',
            voice: 'Adam',
            model: 'eleven_turbo_v2'
          }));
        } else if (message.type === 'complete') {
          clearTimeout(timeout);
          ws.close();
          resolve();
        } else if (message.type === 'error') {
          clearTimeout(timeout);
          ws.close();
          reject(new Error(message.message));
        }
      });

      ws.on('error', (error) => {
        clearTimeout(timeout);
        reject(error);
      });
    });
  }

  updateStats(responseTime, success) {
    this.results.totalRequests++;

    if (success) {
      this.results.successfulRequests++;
      this.results.minResponseTime = Math.min(this.results.minResponseTime, responseTime);
      this.results.maxResponseTime = Math.max(this.results.maxResponseTime, responseTime);

      // 计算平均响应时间
      const totalTime = this.results.averageResponseTime * (this.results.successfulRequests - 1) + responseTime;
      this.results.averageResponseTime = totalTime / this.results.successfulRequests;
    } else {
      this.results.failedRequests++;
    }
  }
}

// 运行测试
if (require.main === module) {
  const tester = new LoadTester(5, 30000); // 5并发，30秒
  tester.runTest().catch(console.error);
}

module.exports = LoadTester;
```

**验证标准：**
```bash
# 运行单元测试
npm test

# 运行负载测试
export TEST_TOKEN="your_test_token"
node tests/load/load-test.js

# 验证测试覆盖率
npm run test -- --coverage
```

## 8. 监控和日志

### 8.1 日志配置

**日志工具 (src/utils/logger.js)：**
```javascript
const fs = require('fs');
const path = require('path');

class Logger {
  constructor() {
    this.logDir = process.env.LOG_DIR || '/var/log/tts-app';
    this.ensureLogDir();
  }

  ensureLogDir() {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  formatMessage(level, message, data = {}, context = {}) {
    const timestamp = new Date().toISOString();
    const username = context.username || 'system';
    const taskId = context.taskId || 'N/A';

    let contextParts = `[user:${username}] [task:${taskId}]`;
    if (context.chunkIndex) {
      contextParts += ` [chunk:${context.chunkIndex}]`;
    }

    const logEntry = {
      timestamp,
      level,
      message,
      context: contextParts,
      data,
      pid: process.pid,
      memory: process.memoryUsage(),
      uptime: process.uptime()
    };

    return JSON.stringify(logEntry);
  }

  writeLog(level, message, data, context) {
    const formattedMessage = this.formatMessage(level, message, data, context);

    // 控制台输出
    console.log(formattedMessage);

    // 文件输出
    const logFile = path.join(this.logDir, `${level.toLowerCase()}.log`);
    fs.appendFileSync(logFile, formattedMessage + '\n');

    // 综合日志
    const combinedFile = path.join(this.logDir, 'combined.log');
    fs.appendFileSync(combinedFile, formattedMessage + '\n');
  }

  info(message, data = {}, context = {}) {
    this.writeLog('INFO', message, data, context);
  }

  warn(message, data = {}, context = {}) {
    this.writeLog('WARN', message, data, context);
  }

  error(error, context = {}, additionalData = {}) {
    const message = error.message || 'Unknown error';
    const data = {
      ...additionalData,
      error: message,
      stack: error.stack?.substring(0, 500)
    };
    this.writeLog('ERROR', message, data, context);
  }

  debug(message, data = {}, context = {}) {
    if (process.env.DEBUG === 'true') {
      this.writeLog('DEBUG', message, data, context);
    }
  }
}

module.exports = new Logger();
```

### 8.2 系统监控脚本

**监控脚本 (scripts/monitor.sh)：**
```bash
#!/bin/bash

# TTS应用监控脚本
LOG_FILE="/var/log/tts-app/monitor.log"
ALERT_EMAIL="<EMAIL>"

log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> $LOG_FILE
}

check_service() {
    local service_name=$1
    local service_status=$(systemctl is-active $service_name)

    if [ "$service_status" != "active" ]; then
        log_message "ALERT: $service_name is not running"
        echo "Service $service_name is down" | mail -s "TTS Service Alert" $ALERT_EMAIL
        return 1
    fi
    return 0
}

check_port() {
    local port=$1
    local service_name=$2

    if ! netstat -tlnp | grep -q ":$port "; then
        log_message "ALERT: Port $port ($service_name) is not listening"
        echo "Port $port for $service_name is not accessible" | mail -s "TTS Port Alert" $ALERT_EMAIL
        return 1
    fi
    return 0
}

check_disk_space() {
    local threshold=80
    local usage=$(df /var/data/tts-app | awk 'NR==2 {print $5}' | sed 's/%//')

    if [ $usage -gt $threshold ]; then
        log_message "ALERT: Disk usage is ${usage}% (threshold: ${threshold}%)"
        echo "Disk usage is ${usage}%" | mail -s "TTS Disk Alert" $ALERT_EMAIL
        return 1
    fi
    return 0
}

check_memory() {
    local threshold=80
    local usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')

    if [ $usage -gt $threshold ]; then
        log_message "ALERT: Memory usage is ${usage}% (threshold: ${threshold}%)"
        echo "Memory usage is ${usage}%" | mail -s "TTS Memory Alert" $ALERT_EMAIL
        return 1
    fi
    return 0
}

check_pm2_processes() {
    local pm2_status=$(pm2 jlist | jq -r '.[] | select(.name=="tts-app") | .pm2_env.status')

    if [ "$pm2_status" != "online" ]; then
        log_message "ALERT: PM2 process tts-app is not online (status: $pm2_status)"
        echo "PM2 process tts-app status: $pm2_status" | mail -s "TTS PM2 Alert" $ALERT_EMAIL

        # 尝试重启
        log_message "Attempting to restart tts-app"
        pm2 restart tts-app
        return 1
    fi
    return 0
}

check_database_connection() {
    if ! psql -h localhost -U tts_app_user -d tts_app_db -c "SELECT 1;" > /dev/null 2>&1; then
        log_message "ALERT: Database connection failed"
        echo "Cannot connect to PostgreSQL database" | mail -s "TTS Database Alert" $ALERT_EMAIL
        return 1
    fi
    return 0
}

check_redis_connection() {
    if ! redis-cli ping > /dev/null 2>&1; then
        log_message "ALERT: Redis connection failed"
        echo "Cannot connect to Redis" | mail -s "TTS Redis Alert" $ALERT_EMAIL
        return 1
    fi
    return 0
}

# 主监控循环
main() {
    log_message "Starting monitoring check"

    local alerts=0

    # 检查系统服务
    check_service "nginx" || ((alerts++))
    check_service "postgresql" || ((alerts++))
    check_service "redis-server" || ((alerts++))

    # 检查端口
    check_port "80" "nginx" || ((alerts++))
    check_port "3000" "tts-app" || ((alerts++))
    check_port "5432" "postgresql" || ((alerts++))
    check_port "6379" "redis" || ((alerts++))

    # 检查资源使用
    check_disk_space || ((alerts++))
    check_memory || ((alerts++))

    # 检查应用状态
    check_pm2_processes || ((alerts++))
    check_database_connection || ((alerts++))
    check_redis_connection || ((alerts++))

    if [ $alerts -eq 0 ]; then
        log_message "All checks passed"
    else
        log_message "Monitoring completed with $alerts alerts"
    fi
}

# 运行监控
main

# 清理旧日志 (保留30天)
find /var/log/tts-app -name "*.log" -mtime +30 -delete
```

### 8.3 日志轮转配置

**Logrotate配置 (/etc/logrotate.d/tts-app)：**
```
/var/log/tts-app/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 0644 tts-app tts-app
    postrotate
        pm2 reloadLogs
    endscript
}
```

**验证标准：**
```bash
# 设置监控脚本
chmod +x /var/www/tts-app/scripts/monitor.sh

# 添加到crontab (每5分钟检查一次)
echo "*/5 * * * * /var/www/tts-app/scripts/monitor.sh" | crontab -

# 测试日志轮转
sudo logrotate -d /etc/logrotate.d/tts-app
sudo logrotate -f /etc/logrotate.d/tts-app

# 验证监控日志
tail -f /var/log/tts-app/monitor.log
```

## 9. 故障排除

### 9.1 常见问题和解决方案

**问题1: WebSocket连接失败**
```bash
# 症状
WebSocket connection failed: Error during WebSocket handshake

# 诊断步骤
1. 检查Nginx WebSocket配置
sudo nginx -t
grep -n "proxy_set_header Upgrade" /etc/nginx/sites-available/tts-app

2. 检查应用WebSocket支持
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" \
     -H "Sec-WebSocket-Key: test" -H "Sec-WebSocket-Version: 13" \
     http://localhost:3000/api/tts/ws/generate

3. 检查防火墙设置
sudo ufw status
sudo iptables -L

# 解决方案
- 确保Nginx配置包含WebSocket升级头
- 检查PM2进程是否正常运行
- 验证端口3000是否被正确监听
```

**问题2: 数据库连接池耗尽**
```bash
# 症状
Error: remaining connection slots are reserved for non-replication superuser connections

# 诊断步骤
1. 检查数据库连接数
psql -h localhost -U tts_app_user -d tts_app_db -c "SELECT count(*) FROM pg_stat_activity;"

2. 检查应用连接池配置
grep -n "max:" /var/www/tts-app/src/services/dbClient.js

3. 检查长时间运行的查询
psql -h localhost -U tts_app_user -d tts_app_db -c "
SELECT pid, now() - pg_stat_activity.query_start AS duration, query
FROM pg_stat_activity
WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes';"

# 解决方案
- 增加PostgreSQL max_connections配置
- 优化应用连接池大小
- 添加连接超时和空闲超时配置
```

**问题3: Redis内存不足**
```bash
# 症状
Redis OOM command not allowed when used memory > 'maxmemory'

# 诊断步骤
1. 检查Redis内存使用
redis-cli info memory

2. 检查Redis配置
grep -n "maxmemory" /etc/redis/redis.conf

3. 分析内存使用模式
redis-cli --bigkeys

# 解决方案
- 增加Redis maxmemory配置
- 配置合适的内存淘汰策略
- 清理过期的任务数据
```

**问题4: 音频文件存储空间不足**
```bash
# 症状
ENOSPC: no space left on device

# 诊断步骤
1. 检查磁盘使用情况
df -h /var/data/tts-app

2. 检查大文件
find /var/data/tts-app -type f -size +100M -exec ls -lh {} \;

3. 检查文件数量
find /var/data/tts-app -type f | wc -l

# 解决方案
- 实施音频文件自动清理策略
- 增加存储空间
- 配置文件压缩
```

### 9.2 故障排除工具脚本

**诊断脚本 (scripts/diagnose.sh)：**
```bash
#!/bin/bash

# TTS应用诊断脚本
echo "=== TTS应用系统诊断 ==="
echo "时间: $(date)"
echo

# 系统信息
echo "=== 系统信息 ==="
echo "操作系统: $(lsb_release -d | cut -f2)"
echo "内核版本: $(uname -r)"
echo "CPU核心数: $(nproc)"
echo "总内存: $(free -h | awk 'NR==2{print $2}')"
echo "可用内存: $(free -h | awk 'NR==2{print $7}')"
echo "磁盘使用: $(df -h / | awk 'NR==2{print $5}')"
echo

# 服务状态
echo "=== 服务状态 ==="
services=("nginx" "postgresql" "redis-server")
for service in "${services[@]}"; do
    status=$(systemctl is-active $service)
    echo "$service: $status"
done
echo

# 端口监听
echo "=== 端口监听状态 ==="
ports=("80:nginx" "3000:tts-app" "5432:postgresql" "6379:redis")
for port_service in "${ports[@]}"; do
    port=$(echo $port_service | cut -d: -f1)
    service=$(echo $port_service | cut -d: -f2)
    if netstat -tlnp | grep -q ":$port "; then
        echo "端口 $port ($service): 监听中"
    else
        echo "端口 $port ($service): 未监听"
    fi
done
echo

# PM2状态
echo "=== PM2进程状态 ==="
pm2 list
echo

# 数据库连接测试
echo "=== 数据库连接测试 ==="
if psql -h localhost -U tts_app_user -d tts_app_db -c "SELECT version();" > /dev/null 2>&1; then
    echo "PostgreSQL: 连接正常"
    # 显示连接数
    conn_count=$(psql -h localhost -U tts_app_user -d tts_app_db -t -c "SELECT count(*) FROM pg_stat_activity;")
    echo "当前连接数: $conn_count"
else
    echo "PostgreSQL: 连接失败"
fi

if redis-cli ping > /dev/null 2>&1; then
    echo "Redis: 连接正常"
    # 显示内存使用
    memory_used=$(redis-cli info memory | grep used_memory_human | cut -d: -f2 | tr -d '\r')
    echo "Redis内存使用: $memory_used"
else
    echo "Redis: 连接失败"
fi
echo

# 应用健康检查
echo "=== 应用健康检查 ==="
if curl -f http://localhost:3000/health > /dev/null 2>&1; then
    echo "应用健康检查: 通过"
    # 获取详细健康信息
    curl -s http://localhost:3000/health | jq .
else
    echo "应用健康检查: 失败"
fi
echo

# 日志文件大小
echo "=== 日志文件状态 ==="
if [ -d "/var/log/tts-app" ]; then
    find /var/log/tts-app -name "*.log" -exec ls -lh {} \; | head -10
else
    echo "日志目录不存在"
fi
echo

# 存储空间检查
echo "=== 存储空间检查 ==="
echo "音频存储目录:"
if [ -d "/var/data/tts-app/audios" ]; then
    du -sh /var/data/tts-app/audios
    file_count=$(find /var/data/tts-app/audios -type f | wc -l)
    echo "音频文件数量: $file_count"
else
    echo "音频存储目录不存在"
fi
echo

# 最近的错误日志
echo "=== 最近的错误日志 ==="
if [ -f "/var/log/tts-app/error.log" ]; then
    echo "最近10条错误日志:"
    tail -10 /var/log/tts-app/error.log
else
    echo "错误日志文件不存在"
fi

echo
echo "=== 诊断完成 ==="
```

### 9.3 自动恢复脚本

**自动恢复脚本 (scripts/auto-recovery.sh)：**
```bash
#!/bin/bash

# TTS应用自动恢复脚本
LOG_FILE="/var/log/tts-app/recovery.log"

log_recovery() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> $LOG_FILE
}

# 重启PM2进程
restart_pm2() {
    log_recovery "Restarting PM2 process: tts-app"
    pm2 restart tts-app
    sleep 5

    if pm2 list | grep -q "tts-app.*online"; then
        log_recovery "PM2 process restarted successfully"
        return 0
    else
        log_recovery "PM2 process restart failed"
        return 1
    fi
}

# 清理临时文件
cleanup_temp_files() {
    log_recovery "Cleaning up temporary files"

    # 清理超过24小时的音频文件
    find /var/data/tts-app/audios -name "*.mp3" -mtime +1 -delete

    # 清理Redis过期任务
    redis-cli --scan --pattern "tts:task:*" | while read key; do
        ttl=$(redis-cli ttl "$key")
        if [ "$ttl" -eq "-1" ]; then
            redis-cli del "$key"
        fi
    done

    log_recovery "Cleanup completed"
}

# 检查并修复权限
fix_permissions() {
    log_recovery "Fixing file permissions"

    sudo chown -R www-data:www-data /var/data/tts-app
    sudo chown -R $USER:$USER /var/log/tts-app
    sudo chmod -R 755 /var/data/tts-app
    sudo chmod -R 644 /var/log/tts-app/*.log

    log_recovery "Permissions fixed"
}

# 主恢复逻辑
main() {
    log_recovery "Starting auto-recovery process"

    # 检查应用健康状态
    if ! curl -f http://localhost:3000/health > /dev/null 2>&1; then
        log_recovery "Application health check failed, attempting recovery"

        # 尝试重启PM2进程
        if ! restart_pm2; then
            log_recovery "PM2 restart failed, trying full recovery"

            # 清理临时文件
            cleanup_temp_files

            # 修复权限
            fix_permissions

            # 再次尝试重启
            restart_pm2
        fi
    else
        log_recovery "Application is healthy, performing maintenance"
        cleanup_temp_files
    fi

    log_recovery "Auto-recovery process completed"
}

# 运行恢复
main
```

**验证标准：**
```bash
# 使脚本可执行
chmod +x /var/www/tts-app/scripts/diagnose.sh
chmod +x /var/www/tts-app/scripts/auto-recovery.sh

# 运行诊断
/var/www/tts-app/scripts/diagnose.sh

# 测试自动恢复
/var/www/tts-app/scripts/auto-recovery.sh

# 添加到crontab (每小时运行一次自动恢复)
echo "0 * * * * /var/www/tts-app/scripts/auto-recovery.sh" | crontab -
```

## 10. 性能优化

### 10.1 系统级优化

**内核参数优化 (/etc/sysctl.conf)：**
```bash
# 网络优化
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_max_tw_buckets = 5000

# 内存优化
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5

# 文件描述符限制
fs.file-max = 2097152

# 应用生效
sudo sysctl -p
```

**用户限制优化 (/etc/security/limits.conf)：**
```bash
# 添加以下行
* soft nofile 65535
* hard nofile 65535
* soft nproc 65535
* hard nproc 65535
```

### 10.2 数据库优化

**PostgreSQL优化 (/etc/postgresql/14/main/postgresql.conf)：**
```bash
# 内存配置
shared_buffers = 2GB                    # 系统内存的25%
effective_cache_size = 6GB              # 系统内存的75%
work_mem = 64MB                         # 每个查询操作的内存
maintenance_work_mem = 512MB            # 维护操作内存

# 连接配置
max_connections = 200                   # 最大连接数
shared_preload_libraries = 'pg_stat_statements'

# 日志配置
log_min_duration_statement = 1000      # 记录超过1秒的查询
log_checkpoints = on
log_connections = on
log_disconnections = on

# 性能配置
random_page_cost = 1.1                 # SSD优化
effective_io_concurrency = 200         # SSD并发IO
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100

# 应用配置
sudo systemctl restart postgresql
```

**数据库索引优化脚本 (scripts/optimize_db.sql)：**
```sql
-- 创建性能优化索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_vip_expire
ON users USING btree ((vip_info->>'expireAt'));

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_task_status_created_username
ON task_status USING btree (created_at, username);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_task_status_status_created
ON task_status USING btree (status, created_at);

-- 分析表统计信息
ANALYZE users;
ANALYZE cards;
ANALYZE task_status;
ANALYZE voice_mappings;

-- 查看慢查询
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;
```

### 10.3 Redis优化

**Redis配置优化 (/etc/redis/redis.conf)：**
```bash
# 内存优化
maxmemory 4gb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# 持久化优化 (根据需求调整)
save 900 1
save 300 10
save 60 10000
rdbcompression yes
rdbchecksum yes

# 网络优化
tcp-keepalive 300
timeout 0

# 性能优化
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# 重启Redis
sudo systemctl restart redis-server
```

### 10.4 应用级优化

**连接池优化配置：**
```javascript
// src/config/database.js
module.exports = {
  // PostgreSQL连接池优化
  postgresql: {
    max: 20,                    // 最大连接数
    min: 5,                     // 最小连接数
    acquire: 30000,             // 获取连接超时
    idle: 10000,                // 空闲超时
    evict: 1000,                // 检查间隔
    handleDisconnects: true,    // 自动重连
    acquireTimeoutMillis: 2000, // 获取连接超时
    createTimeoutMillis: 3000,  // 创建连接超时
    destroyTimeoutMillis: 5000, // 销毁连接超时
    reapIntervalMillis: 1000,   // 清理间隔
    createRetryIntervalMillis: 200 // 重试间隔
  },

  // Redis连接池优化
  redis: {
    maxRetriesPerRequest: 3,
    retryDelayOnFailover: 100,
    enableReadyCheck: false,
    lazyConnect: true,
    keepAlive: 30000,
    family: 4,
    connectTimeout: 10000,
    commandTimeout: 5000
  }
};
```

**并发处理优化：**
```javascript
// src/utils/concurrency.js
const pLimit = require('p-limit');

class ConcurrencyManager {
  constructor() {
    // 根据CPU核心数动态调整并发限制
    const cpuCount = require('os').cpus().length;
    this.ttsLimit = pLimit(Math.max(2, cpuCount - 1));
    this.dbLimit = pLimit(Math.max(5, cpuCount * 2));
    this.fileLimit = pLimit(Math.max(3, cpuCount));
  }

  // TTS请求限制
  limitTTS(fn) {
    return this.ttsLimit(fn);
  }

  // 数据库操作限制
  limitDB(fn) {
    return this.dbLimit(fn);
  }

  // 文件操作限制
  limitFile(fn) {
    return this.fileLimit(fn);
  }
}

module.exports = new ConcurrencyManager();
```

### 10.5 缓存策略

**应用级缓存实现：**
```javascript
// src/services/cacheService.js
const NodeCache = require('node-cache');

class CacheService {
  constructor() {
    // 语音映射缓存 (5分钟)
    this.voiceCache = new NodeCache({ stdTTL: 300 });

    // 用户信息缓存 (2分钟)
    this.userCache = new NodeCache({ stdTTL: 120 });

    // 配置缓存 (10分钟)
    this.configCache = new NodeCache({ stdTTL: 600 });
  }

  // 语音映射缓存
  async getVoiceMapping(voiceName) {
    const cached = this.voiceCache.get(voiceName);
    if (cached) return cached;

    // 从数据库获取
    const result = await dbClient.query(
      'SELECT voice_id FROM voice_mappings WHERE voice_name = $1',
      [voiceName]
    );

    const voiceId = result.rows[0]?.voice_id || voiceName;
    this.voiceCache.set(voiceName, voiceId);
    return voiceId;
  }

  // 用户VIP信息缓存
  async getUserVipInfo(username) {
    const cacheKey = `user:${username}`;
    const cached = this.userCache.get(cacheKey);
    if (cached) return cached;

    const result = await dbClient.query(
      'SELECT vip_info FROM users WHERE username = $1',
      [username]
    );

    const vipInfo = result.rows[0]?.vip_info || {};
    this.userCache.set(cacheKey, vipInfo);
    return vipInfo;
  }

  // 清除用户缓存
  clearUserCache(username) {
    this.userCache.del(`user:${username}`);
  }

  // 获取缓存统计
  getStats() {
    return {
      voice: this.voiceCache.getStats(),
      user: this.userCache.getStats(),
      config: this.configCache.getStats()
    };
  }
}

module.exports = new CacheService();
```

### 10.6 性能监控

**性能监控中间件：**
```javascript
// src/middleware/performance.js
const performanceMonitor = (req, res, next) => {
  const start = process.hrtime.bigint();

  res.on('finish', () => {
    const end = process.hrtime.bigint();
    const duration = Number(end - start) / 1000000; // 转换为毫秒

    // 记录慢请求 (超过1秒)
    if (duration > 1000) {
      console.warn(`Slow request: ${req.method} ${req.path} - ${duration.toFixed(2)}ms`);
    }

    // 记录到监控系统
    if (process.env.ENABLE_PERFORMANCE_MONITORING === 'true') {
      // 这里可以集成APM工具如New Relic, DataDog等
      recordMetric('request_duration', duration, {
        method: req.method,
        path: req.path,
        status: res.statusCode
      });
    }
  });

  next();
};

function recordMetric(name, value, tags = {}) {
  // 实现指标记录逻辑
  const metric = {
    name,
    value,
    tags,
    timestamp: Date.now()
  };

  // 可以发送到监控系统或写入日志
  console.log(`METRIC: ${JSON.stringify(metric)}`);
}

module.exports = performanceMonitor;
```

### 10.7 部署优化脚本

**性能优化部署脚本 (scripts/optimize.sh)：**
```bash
#!/bin/bash

echo "=== 开始性能优化配置 ==="

# 应用系统优化
echo "应用系统级优化..."
sudo sysctl -p

# 设置用户限制
echo "设置用户限制..."
sudo systemctl daemon-reload

# 优化数据库
echo "优化PostgreSQL配置..."
sudo systemctl restart postgresql
psql -h localhost -U tts_app_user -d tts_app_db -f /var/www/tts-app/scripts/optimize_db.sql

# 优化Redis
echo "优化Redis配置..."
sudo systemctl restart redis-server

# 重启应用以应用优化
echo "重启应用..."
pm2 restart tts-app

# 验证优化效果
echo "验证优化效果..."
sleep 10

# 检查连接数
echo "PostgreSQL连接数: $(psql -h localhost -U tts_app_user -d tts_app_db -t -c 'SELECT count(*) FROM pg_stat_activity;')"

# 检查Redis内存
echo "Redis内存使用: $(redis-cli info memory | grep used_memory_human | cut -d: -f2)"

# 检查应用响应时间
response_time=$(curl -o /dev/null -s -w '%{time_total}' http://localhost:3000/health)
echo "应用响应时间: ${response_time}秒"

echo "=== 性能优化完成 ==="
```

**验证标准：**
```bash
# 运行优化脚本
chmod +x /var/www/tts-app/scripts/optimize.sh
/var/www/tts-app/scripts/optimize.sh

# 性能基准测试
ab -n 1000 -c 10 http://localhost/health

# 监控系统资源
htop
iotop
nethogs

# 检查数据库性能
psql -h localhost -U tts_app_user -d tts_app_db -c "
SELECT schemaname,tablename,attname,n_distinct,correlation
FROM pg_stats
WHERE tablename IN ('users','task_status','cards');"
```

---

## 总结

本实施计划提供了将Cloudflare Worker TTS应用完整迁移到Ubuntu服务器的详细指南。通过遵循本计划，您可以实现：

1. **功能完全对等** - 所有原有功能都得到保留
2. **数据完整迁移** - 零丢失的数据迁移方案
3. **生产级部署** - 高可用、高性能的服务部署
4. **完善的监控** - 全面的监控和故障排除机制
5. **性能优化** - 针对单机环境的深度优化

**关键成功因素：**
- 严格按照步骤执行，每个阶段都要验证
- 在生产环境部署前，先在测试环境完整验证
- 保持原有API接口的完全兼容性
- 建立完善的备份和回滚机制

**预期收益：**
- 降低运营成本（相比Cloudflare Worker）
- 提高系统可控性和可定制性
- 获得更好的性能和稳定性
- 简化运维和故障排除流程

建议在实施过程中保持与开发团队的密切沟通，确保每个步骤都得到正确执行和验证。
```
```
```
```
```
