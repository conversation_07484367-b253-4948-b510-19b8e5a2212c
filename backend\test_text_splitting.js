// 测试新的文本分割功能
const { splitText, splitTextWithSSML, splitTextTraditional, smartSplitLongText } = require('./src/utils/ttsUtils');

async function testTextSplitting() {
  console.log('🧪 开始测试文本分割功能...\n');

  // 测试1: 普通文本分割
  console.log('📝 测试1: 普通文本分割');
  const normalText = '这是一个测试文本。它包含多个句子！我们来看看分割效果如何？这个句子比较长，用来测试智能分割功能是否能够正确处理各种标点符号和语言混合的情况。';
  const normalChunks = await splitText(normalText);
  console.log(`输入文本长度: ${normalText.length}`);
  console.log(`分割结果: ${normalChunks.length} 个片段`);
  normalChunks.forEach((chunk, index) => {
    console.log(`  片段${index + 1} (${chunk.length}字符): ${chunk.substring(0, 50)}${chunk.length > 50 ? '...' : ''}`);
  });
  console.log('');

  // 测试2: SSML文本分割
  console.log('📝 测试2: SSML文本分割');
  const ssmlText = '[calmly] 这是一个平静的开始。[excited] 然后变得兴奋起来！[whispering] 最后是悄悄话。这段文本包含了多种SSML指令，用来测试SSML感知的分割功能。';
  const ssmlChunks = await splitText(ssmlText);
  console.log(`输入文本长度: ${ssmlText.length}`);
  console.log(`分割结果: ${ssmlChunks.length} 个片段`);
  ssmlChunks.forEach((chunk, index) => {
    console.log(`  片段${index + 1} (${chunk.length}字符): ${chunk}`);
  });
  console.log('');

  // 测试3: 超长文本分割
  console.log('📝 测试3: 超长文本分割');
  const longText = '这是一个非常长的文本，'.repeat(100) + '用来测试智能分割功能在处理超长文本时的表现。';
  const longChunks = await splitText(longText);
  console.log(`输入文本长度: ${longText.length}`);
  console.log(`分割结果: ${longChunks.length} 个片段`);
  longChunks.forEach((chunk, index) => {
    console.log(`  片段${index + 1} (${chunk.length}字符): ${chunk.substring(0, 50)}${chunk.length > 50 ? '...' : ''}`);
  });
  console.log('');

  // 测试4: 英文文本分割
  console.log('📝 测试4: 英文文本分割');
  const englishText = 'This is a test of the English text splitting functionality. It should handle punctuation marks, spaces, and word boundaries correctly. The intelligent splitting algorithm should preserve word integrity while respecting the maximum chunk size limit.';
  const englishChunks = await splitText(englishText);
  console.log(`输入文本长度: ${englishText.length}`);
  console.log(`分割结果: ${englishChunks.length} 个片段`);
  englishChunks.forEach((chunk, index) => {
    console.log(`  片段${index + 1} (${chunk.length}字符): ${chunk}`);
  });
  console.log('');

  // 测试5: 混合语言和SSML
  console.log('📝 测试5: 混合语言和SSML');
  const mixedText = '[cheerful] Hello everyone! 大家好！[serious] This is a serious announcement. 这是一个严肃的公告。[playful] Let\'s have some fun! 让我们一起玩耍吧！';
  const mixedChunks = await splitText(mixedText);
  console.log(`输入文本长度: ${mixedText.length}`);
  console.log(`分割结果: ${mixedChunks.length} 个片段`);
  mixedChunks.forEach((chunk, index) => {
    console.log(`  片段${index + 1} (${chunk.length}字符): ${chunk}`);
  });

  console.log('\n✅ 文本分割功能测试完成！');
}

// 运行测试
testTextSplitting().catch(console.error);
