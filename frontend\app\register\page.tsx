"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Eye, EyeOff, Mail, Lock, User, Mic, ArrowRight, AlertCircle, CheckCircle, Shield, Send, Clock } from "lucide-react"
import { useEmailVerification } from "@/hooks/use-email-verification"
import { auth } from "@/lib/auth-service"

interface FormData {
  username: string
  email: string
  password: string
  confirmPassword: string
  agreeToTerms: boolean
  subscribeNewsletter: boolean
}

interface ValidationErrors {
  [key: string]: string
}

export default function RegisterPage() {
  const [formData, setFormData] = useState<FormData>({
    username: "",
    email: "",
    password: "",
    confirmPassword: "",
    agreeToTerms: false,
    subscribeNewsletter: false,
  })
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<ValidationErrors>({})
  const [registrationError, setRegistrationError] = useState("")
  const [isPageLoaded, setIsPageLoaded] = useState(false)
  const [passwordStrength, setPasswordStrength] = useState(0)
  const [showSuccess, setShowSuccess] = useState(false)
  const [isClient, setIsClient] = useState(false)

  // 邮箱验证相关状态
  const [verificationCode, setVerificationCode] = useState("")
  const [showVerificationStep, setShowVerificationStep] = useState(false)

  // 使用邮箱验证Hook
  const emailVerification = useEmailVerification({
    onSuccess: () => {
      setShowSuccess(true)
      // 2秒后跳转到主页
      setTimeout(() => {
        window.location.href = "/"
      }, 2000)
    },
    onError: (error) => {
      setRegistrationError(error)
    }
  })

  // 客户端挂载状态管理 - 解决水合失败问题
  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    setIsPageLoaded(true)
  }, [])

  useEffect(() => {
    // Calculate password strength
    const calculatePasswordStrength = (password: string): number => {
      let strength = 0
      if (password.length >= 8) strength += 1
      if (/[a-z]/.test(password)) strength += 1
      if (/[A-Z]/.test(password)) strength += 1
      if (/[0-9]/.test(password)) strength += 1
      if (/[^A-Za-z0-9]/.test(password)) strength += 1
      return strength
    }

    setPasswordStrength(calculatePasswordStrength(formData.password))
  }, [formData.password])

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const validateUsername = (username: string): boolean => {
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/
    return usernameRegex.test(username)
  }

  const validatePassword = (password: string): boolean => {
    return password.length >= 8 && /(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)
  }

  const validateForm = (): boolean => {
    const newErrors: ValidationErrors = {}

    // Username validation
    if (!formData.username.trim()) {
      newErrors.username = "用户名不能为空"
    } else if (!validateUsername(formData.username)) {
      newErrors.username = "用户名只能包含字母、数字和下划线，长度3-20位"
    }

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = "邮箱地址不能为空"
    } else if (!validateEmail(formData.email)) {
      newErrors.email = "请输入有效的邮箱地址"
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = "密码不能为空"
    } else if (!validatePassword(formData.password)) {
      newErrors.password = "密码至少8位，包含大小写字母和数字"
    }

    // Confirm password validation
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = "请确认密码"
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "两次输入的密码不一致"
    }

    // Terms agreement validation
    if (!formData.agreeToTerms) {
      newErrors.agreeToTerms = "请同意服务条款和隐私政策"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (field: keyof FormData, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
    // Clear specific field error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }))
    }
    // Clear registration error when user modifies form
    if (registrationError) {
      setRegistrationError("")
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    // 如果还没有发送验证码，先发送验证码
    if (!showVerificationStep) {
      setIsLoading(true)
      setRegistrationError("")

      try {
        await emailVerification.sendVerificationCode({
          email: formData.email,
          username: formData.username,
          password: formData.password,
        })

        // 切换到验证码输入步骤
        setShowVerificationStep(true)
      } catch (error) {
        // 错误已经在Hook中处理
      } finally {
        setIsLoading(false)
      }
    } else {
      // 验证邮箱并完成注册
      if (!verificationCode.trim()) {
        setRegistrationError("请输入验证码")
        return
      }

      setIsLoading(true)
      setRegistrationError("")

      try {
        await emailVerification.verifyEmailAndRegister(verificationCode)
        // 成功处理在Hook的onSuccess回调中
      } catch (error) {
        // 错误已经在Hook中处理
      } finally {
        setIsLoading(false)
      }
    }
  }

  // 返回到第一步
  const handleBackToFirstStep = () => {
    setShowVerificationStep(false)
    setVerificationCode("")
    emailVerification.reset()
    setRegistrationError("")
  }

  const getPasswordStrengthColor = (strength: number): string => {
    if (strength <= 1) return "bg-red-500"
    if (strength <= 2) return "bg-orange-500"
    if (strength <= 3) return "bg-yellow-500"
    if (strength <= 4) return "bg-blue-500"
    return "bg-green-500"
  }

  const getPasswordStrengthText = (strength: number): string => {
    if (strength <= 1) return "弱"
    if (strength <= 2) return "一般"
    if (strength <= 3) return "中等"
    if (strength <= 4) return "强"
    return "很强"
  }

  // 预定义的粒子位置和动画参数 - 解决水合失败问题
  const registerParticleConfigs = [
    { left: 18, top: 22, duration: 10 },
    { left: 72, top: 38, duration: 12 },
    { left: 42, top: 62, duration: 9 },
    { left: 82, top: 16, duration: 11 },
    { left: 28, top: 78, duration: 8 },
    { left: 62, top: 48, duration: 13 },
    { left: 52, top: 32, duration: 10 },
    { left: 38, top: 82, duration: 12 }
  ];

  const FloatingParticles = () => (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {registerParticleConfigs.map((config, i) => (
        <div
          key={i}
          className="absolute w-2 h-2 bg-gradient-to-r from-green-400 to-blue-400 rounded-full opacity-20 animate-float"
          style={{
            left: `${config.left}%`,
            top: `${config.top}%`,
            animationDelay: `${i * 2}s`,
            animationDuration: `${config.duration}s`,
          }}
        />
      ))}
    </div>
  )

  if (showSuccess) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-green-50/30 to-blue-50/20 flex items-center justify-center p-6 relative overflow-hidden">
        {isClient && <FloatingParticles />}

        <div className="w-full max-w-md">
          <Card className="border-0 shadow-2xl bg-white/90 backdrop-blur-xl relative overflow-hidden">
            <CardContent className="p-10 text-center">
              <div className="flex justify-center mb-6">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-blue-600 rounded-full blur-lg opacity-50 animate-pulse" />
                  <div className="relative p-4 bg-gradient-to-r from-green-500 to-blue-600 rounded-full shadow-xl">
                    <CheckCircle className="w-8 h-8 text-white" />
                  </div>
                </div>
              </div>
              <h2 className="text-3xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-4">
                注册成功！
              </h2>
              <p className="text-gray-600 text-lg mb-6">欢迎加入 AI 语音工作室，正在为您跳转到主页面...</p>
              <div className="flex justify-center">
                <div className="w-8 h-8 border-4 border-green-500 border-t-transparent rounded-full animate-spin" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-green-50/30 to-blue-50/20 flex items-center justify-center p-6 relative overflow-hidden">
      {isClient && <FloatingParticles />}

      {/* Animated Background Elements */}
      <div className="absolute top-20 right-20 w-72 h-72 bg-gradient-to-r from-green-200/20 to-blue-200/20 rounded-full blur-3xl animate-pulse" />
      <div
        className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-r from-blue-200/20 to-purple-200/20 rounded-full blur-3xl animate-pulse"
        style={{ animationDelay: "2s" }}
      />

      <div
        className={`w-full max-w-2xl transition-all duration-1000 ${isPageLoaded ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"}`}
      >
        <Card className="border-0 shadow-2xl bg-white/90 backdrop-blur-xl relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-green-500/5 to-blue-500/5" />

          <CardHeader className="text-center pb-8 relative">
            <div className="flex justify-center mb-6">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-blue-600 rounded-2xl blur-lg opacity-50 animate-pulse" />
                <div className="relative p-4 bg-gradient-to-r from-green-500 to-blue-600 rounded-2xl shadow-xl">
                  <Mic className="w-8 h-8 text-white" />
                </div>
              </div>
            </div>
            <CardTitle className="text-3xl font-bold bg-gradient-to-r from-gray-900 via-green-800 to-blue-800 bg-clip-text text-transparent mb-2">
              创建账户
            </CardTitle>
            <p className="text-gray-600 text-lg">加入 AI 语音工作室，开启您的创作之旅</p>
          </CardHeader>

          <CardContent className="relative">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Registration Error Message */}
              {(registrationError || emailVerification.hasError) && (
                <div className="p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-3 animate-fade-in">
                  <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0" />
                  <p className="text-red-700 text-sm">
                    {registrationError || emailVerification.sendError || emailVerification.verifyError}
                  </p>
                </div>
              )}

              {/* 验证码发送成功提示 */}
              {emailVerification.isCodeSent && !emailVerification.hasError && (
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg flex items-center gap-3 animate-fade-in">
                  <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                  <p className="text-green-700 text-sm">
                    验证码已发送到 {emailVerification.pendingRegistration?.email}，请查收邮件
                  </p>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Username Field */}
                <div className="space-y-2">
                  <label htmlFor="username" className="block text-sm font-semibold text-gray-700">
                    用户名 <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <User className="h-5 w-5 text-gray-400" />
                    </div>
                    <Input
                      id="username"
                      type="text"
                      value={formData.username}
                      onChange={(e) => handleInputChange("username", e.target.value)}
                      placeholder="请输入用户名"
                      className={`pl-10 h-12 text-lg border-2 transition-all duration-300 ${
                        errors.username
                          ? "border-red-400 focus:border-red-500 focus:ring-red-100"
                          : "border-gray-200 focus:border-green-400 focus:ring-green-100"
                      }`}
                      disabled={isLoading}
                    />
                  </div>
                  {errors.username && (
                    <p className="text-red-500 text-sm flex items-center gap-2 animate-fade-in">
                      <AlertCircle className="w-4 h-4" />
                      {errors.username}
                    </p>
                  )}
                </div>

                {/* Email Field */}
                <div className="space-y-2">
                  <label htmlFor="email" className="block text-sm font-semibold text-gray-700">
                    邮箱地址 <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Mail className="h-5 w-5 text-gray-400" />
                    </div>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange("email", e.target.value)}
                      placeholder="请输入邮箱地址"
                      className={`pl-10 h-12 text-lg border-2 transition-all duration-300 ${
                        errors.email
                          ? "border-red-400 focus:border-red-500 focus:ring-red-100"
                          : "border-gray-200 focus:border-green-400 focus:ring-green-100"
                      }`}
                      disabled={isLoading}
                    />
                  </div>
                  {errors.email && (
                    <p className="text-red-500 text-sm flex items-center gap-2 animate-fade-in">
                      <AlertCircle className="w-4 h-4" />
                      {errors.email}
                    </p>
                  )}
                </div>
              </div>

              {/* Password Field */}
              <div className="space-y-2">
                <label htmlFor="password" className="block text-sm font-semibold text-gray-700">
                  密码 <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-gray-400" />
                  </div>
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={formData.password}
                    onChange={(e) => handleInputChange("password", e.target.value)}
                    placeholder="请输入密码"
                    className={`pl-10 pr-12 h-12 text-lg border-2 transition-all duration-300 ${
                      errors.password
                        ? "border-red-400 focus:border-red-500 focus:ring-red-100"
                        : "border-gray-200 focus:border-green-400 focus:ring-green-100"
                    }`}
                    disabled={isLoading}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center hover:bg-gray-50 rounded-r-lg transition-colors duration-200"
                    disabled={isLoading}
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                    )}
                  </button>
                </div>

                {/* Password Strength Indicator */}
                {formData.password && (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="flex-1 bg-gray-200 rounded-full h-2 overflow-hidden">
                        <div
                          className={`h-full transition-all duration-300 ${getPasswordStrengthColor(passwordStrength)}`}
                          style={{ width: `${(passwordStrength / 5) * 100}%` }}
                        />
                      </div>
                      <span className="text-sm font-medium text-gray-600">
                        {getPasswordStrengthText(passwordStrength)}
                      </span>
                    </div>
                  </div>
                )}

                {errors.password && (
                  <p className="text-red-500 text-sm flex items-center gap-2 animate-fade-in">
                    <AlertCircle className="w-4 h-4" />
                    {errors.password}
                  </p>
                )}
              </div>

              {/* Confirm Password Field */}
              <div className="space-y-2">
                <label htmlFor="confirmPassword" className="block text-sm font-semibold text-gray-700">
                  确认密码 <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-gray-400" />
                  </div>
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    value={formData.confirmPassword}
                    onChange={(e) => handleInputChange("confirmPassword", e.target.value)}
                    placeholder="请再次输入密码"
                    className={`pl-10 pr-12 h-12 text-lg border-2 transition-all duration-300 ${
                      errors.confirmPassword
                        ? "border-red-400 focus:border-red-500 focus:ring-red-100"
                        : "border-gray-200 focus:border-green-400 focus:ring-green-100"
                    }`}
                    disabled={isLoading}
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center hover:bg-gray-50 rounded-r-lg transition-colors duration-200"
                    disabled={isLoading}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                    )}
                  </button>
                </div>
                {errors.confirmPassword && (
                  <p className="text-red-500 text-sm flex items-center gap-2 animate-fade-in">
                    <AlertCircle className="w-4 h-4" />
                    {errors.confirmPassword}
                  </p>
                )}
              </div>

              {/* 验证码输入字段 - 只在第二步显示 */}
              {showVerificationStep && (
                <div className="space-y-2">
                  <label htmlFor="verificationCode" className="block text-sm font-semibold text-gray-700">
                    邮箱验证码 <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Mail className="h-5 w-5 text-gray-400" />
                    </div>
                    <Input
                      id="verificationCode"
                      type="text"
                      value={verificationCode}
                      onChange={(e) => setVerificationCode(e.target.value)}
                      placeholder="请输入6位验证码"
                      className="pl-10 h-12 text-lg border-2 transition-all duration-300 border-gray-200 focus:border-green-400 focus:ring-green-100"
                      disabled={isLoading}
                      maxLength={6}
                    />
                  </div>

                  {/* 重新发送验证码按钮 */}
                  <div className="flex items-center justify-between">
                    <Button
                      type="button"
                      variant="ghost"
                      onClick={handleBackToFirstStep}
                      className="text-gray-600 hover:text-gray-800"
                      disabled={isLoading}
                    >
                      ← 返回修改信息
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={emailVerification.resendVerificationCode}
                      disabled={!emailVerification.canResend || emailVerification.isSending}
                      className="text-sm"
                    >
                      {emailVerification.isSending ? (
                        <div className="flex items-center gap-2">
                          <div className="w-4 h-4 border-2 border-green-500 border-t-transparent rounded-full animate-spin" />
                          发送中...
                        </div>
                      ) : (
                        emailVerification.getCountdownText()
                      )}
                    </Button>
                  </div>
                </div>
              )}

              {/* Terms and Newsletter - 只在第一步显示 */}
              {!showVerificationStep && (
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <Checkbox
                    id="agreeToTerms"
                    checked={formData.agreeToTerms}
                    onCheckedChange={(checked) => handleInputChange("agreeToTerms", checked as boolean)}
                    disabled={isLoading}
                    className="data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500 mt-1"
                  />
                  <div className="flex-1">
                    <label
                      htmlFor="agreeToTerms"
                      className="text-sm text-gray-700 cursor-pointer select-none leading-relaxed"
                    >
                      我已阅读并同意{" "}
                      <button
                        type="button"
                        className="text-green-600 hover:text-green-800 hover:underline font-semibold"
                        onClick={() => alert("服务条款页面")}
                      >
                        服务条款
                      </button>{" "}
                      和{" "}
                      <button
                        type="button"
                        className="text-green-600 hover:text-green-800 hover:underline font-semibold"
                        onClick={() => alert("隐私政策页面")}
                      >
                        隐私政策
                      </button>{" "}
                      <span className="text-red-500">*</span>
                    </label>
                    {errors.agreeToTerms && (
                      <p className="text-red-500 text-sm flex items-center gap-2 mt-2 animate-fade-in">
                        <AlertCircle className="w-4 h-4" />
                        {errors.agreeToTerms}
                      </p>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Checkbox
                    id="subscribeNewsletter"
                    checked={formData.subscribeNewsletter}
                    onCheckedChange={(checked) => handleInputChange("subscribeNewsletter", checked as boolean)}
                    disabled={isLoading}
                    className="data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"
                  />
                  <label htmlFor="subscribeNewsletter" className="text-sm text-gray-700 cursor-pointer select-none">
                    订阅产品更新和优惠信息
                  </label>
                </div>
              </div>
              )}

              {/* Register Button */}
              <Button
                type="submit"
                disabled={isLoading || (showVerificationStep && !verificationCode.trim())}
                className="w-full h-12 text-lg font-bold bg-gradient-to-r from-green-500 via-blue-500 to-purple-500 hover:from-green-600 hover:via-blue-600 hover:to-purple-600 disabled:from-gray-400 disabled:to-gray-500 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:scale-100 relative overflow-hidden group"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="relative z-10 flex items-center justify-center gap-3">
                  {isLoading ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      {showVerificationStep ? '验证中...' : '发送验证码中...'}
                    </>
                  ) : (
                    <>
                      {showVerificationStep ? (
                        <>
                          <CheckCircle className="w-5 h-5" />
                          完成注册
                        </>
                      ) : (
                        <>
                          <Send className="w-5 h-5" />
                          发送验证码
                        </>
                      )}
                    </>
                  )}
                </div>
              </Button>

              {/* Security Notice */}
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg flex items-start gap-3">
                <Shield className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
                <div className="text-sm text-blue-800">
                  <p className="font-semibold mb-1">安全提示</p>
                  <p>您的个人信息将被安全加密存储，我们承诺不会向第三方泄露您的隐私信息。</p>
                </div>
              </div>
            </form>

            {/* Login Link */}
            <div className="mt-8 text-center">
              <p className="text-gray-600">
                已有账户？{" "}
                <button
                  onClick={() => (window.location.href = "/login")}
                  className="text-green-600 hover:text-green-800 font-semibold hover:underline transition-colors duration-200"
                >
                  立即登录
                </button>
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Back to Home Link */}
        <div className="mt-6 text-center">
          <button
            onClick={() => (window.location.href = "/")}
            className="text-gray-600 hover:text-gray-800 text-sm hover:underline transition-colors duration-200"
          >
            ← 返回首页
          </button>
        </div>
      </div>

      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(180deg); }
        }
        @keyframes fade-in {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-float {
          animation: float 8s ease-in-out infinite;
        }
        .animate-fade-in {
          animation: fade-in 0.3s ease-out forwards;
        }
        /* 应用动画优化 */
        .animate-float {
          will-change: transform;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        }
        .animate-fade-in {
          will-change: transform, opacity;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        }
      `}</style>
    </div>
  )
}
