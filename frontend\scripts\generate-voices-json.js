const fs = require('fs');
const path = require('path');

const voicesCSVPath = path.join(__dirname, '..', 'voices_extracted.csv');
const urlsCSVPath = path.join(__dirname, '..', 'preview_urls_unique.csv');
const outputPath = path.join(__dirname, '..', 'lib', 'voices.json');
const outputDir = path.dirname(outputPath);

// Function to parse CSV data
function parseCSV(csvData) {
    const lines = csvData.trim().split(/\r?\n/);
    const headers = lines[0].split(',').map(h => h.trim());
    return lines.slice(1).map(line => {
        // This regex handles commas inside quoted fields
        const values = line.match(/(".*?"|[^",]+)(?=\s*,|\s*$)/g) || [];
        return headers.reduce((obj, header, index) => {
            let value = (values[index] || '').trim();
            if (value.startsWith('"') && value.endsWith('"')) {
                value = value.slice(1, -1);
            }
            obj[header] = value;
            return obj;
        }, {});
    });
}

try {
    // Read CSV files
    const voicesCSV = fs.readFileSync(voicesCSVPath, 'utf8');
    const urlsCSV = fs.readFileSync(urlsCSVPath, 'utf8');

    // Parse CSV data
    const voicesData = parseCSV(voicesCSV);
    const urlsData = urlsCSV.trim().split(/\r?\n/).slice(1).map(line => line.trim());

    // Create a map of voice_id to preview_url
    const urlMap = new Map();
    urlsData.forEach(url => {
        const match = url.match(/voices\/([a-zA-Z0-9]+)\//);
        if (match && match[1]) {
            urlMap.set(match[1], url);
        }
    });

    // Combine data
    const combinedVoices = voicesData.map(voice => {
        if (!voice.voice_id) return null;
        return {
            id: voice.voice_id,
            name: voice.name || 'Unknown',
            gender: voice.gender || 'neutral',
            description: voice.description || '',
            preview: urlMap.get(voice.voice_id) || null,
        };
    }).filter(Boolean); // Filter out any null entries

    // Ensure output directory exists
    if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
    }

    // Write to JSON file
    fs.writeFileSync(outputPath, JSON.stringify(combinedVoices, null, 2));

    console.log(`Successfully generated ${outputPath} with ${combinedVoices.length} voices.`);

} catch (error) {
    console.error('Error generating voices.json:', error);
    process.exit(1);
} 