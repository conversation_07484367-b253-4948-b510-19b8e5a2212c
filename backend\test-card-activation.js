#!/usr/bin/env node

require('dotenv').config();
const { Pool } = require('pg');
const { useCard, verifyCard, calculateQuotaDetails } = require('./src/services/authService');

// 颜色输出函数
function log(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color] || ''}${message}${colors.reset}`);
}

// 测试卡密激活逻辑
async function testCardActivation() {
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL
  });

  try {
    log('blue', '🧪 开始测试卡密激活逻辑...\n');

    // 1. 获取一张未使用的测试卡密
    const cardResult = await pool.query(
      'SELECT * FROM cards WHERE status = $1 AND package_type = $2 LIMIT 1',
      ['unused', 'PT'] // 使用测试套餐
    );

    if (cardResult.rows.length === 0) {
      log('red', '❌ 没有找到可用的测试卡密，请先运行 node scripts/create-test-cards.js');
      return;
    }

    const testCard = cardResult.rows[0];
    log('cyan', `🎫 找到测试卡密: ${testCard.code}`);
    log('yellow', `📦 套餐类型: ${testCard.package_type}`);
    log('magenta', `📋 套餐信息: ${JSON.stringify(testCard.package_info, null, 2)}`);

    // 2. 获取测试用户
    const userResult = await pool.query(
      'SELECT * FROM users WHERE username = $1',
      ['testuser'] // 假设有一个测试用户
    );

    if (userResult.rows.length === 0) {
      log('red', '❌ 没有找到测试用户，请先创建测试用户');
      return;
    }

    const testUser = userResult.rows[0];
    log('green', `👤 找到测试用户: ${testUser.username}`);
    
    // 显示用户当前VIP状态
    const currentQuota = calculateQuotaDetails(testUser);
    log('blue', '📊 用户当前状态:');
    log('cyan', `   VIP类型: ${testUser.vip_info?.type || '无'}`);
    log('cyan', `   过期时间: ${testUser.vip_info?.expireAt ? new Date(testUser.vip_info.expireAt).toLocaleString() : '无'}`);
    log('cyan', `   是否老用户: ${currentQuota.isLegacyUser ? '是' : '否'}`);
    log('cyan', `   总配额: ${currentQuota.quotaChars || '无限制'}`);
    log('cyan', `   已用配额: ${currentQuota.usedChars || 0}`);
    log('cyan', `   剩余配额: ${currentQuota.remainingChars || '无限制'}\n`);

    // 3. 验证卡密
    log('blue', '🔍 验证卡密...');
    const cardInfo = await verifyCard(testCard.code);
    if (!cardInfo) {
      log('red', '❌ 卡密验证失败');
      return;
    }
    log('green', '✅ 卡密验证成功');

    // 4. 使用卡密
    log('blue', '🎯 激活卡密...');
    try {
      const updatedVip = await useCard(testCard.code, testUser.username);
      log('green', '✅ 卡密激活成功！');
      
      // 显示更新后的VIP信息
      log('magenta', '🎉 更新后的VIP信息:');
      log('cyan', `   VIP类型: ${updatedVip.type}`);
      log('cyan', `   过期时间: ${new Date(updatedVip.expireAt).toLocaleString()}`);
      log('cyan', `   总配额: ${updatedVip.quotaChars?.toLocaleString() || '无限制'} 字符`);
      log('cyan', `   已用配额: ${updatedVip.usedChars || 0} 字符`);
      log('cyan', `   剩余配额: ${updatedVip.quotaChars ? (updatedVip.quotaChars - (updatedVip.usedChars || 0)).toLocaleString() : '无限制'} 字符`);

      // 验证数据库中的状态
      const updatedCardResult = await pool.query(
        'SELECT * FROM cards WHERE code = $1',
        [testCard.code]
      );
      const updatedCard = updatedCardResult.rows[0];
      log('blue', '\n📋 卡密状态验证:');
      log('cyan', `   状态: ${updatedCard.status}`);
      log('cyan', `   使用者: ${updatedCard.used_by}`);
      log('cyan', `   使用时间: ${updatedCard.used_at ? new Date(updatedCard.used_at).toLocaleString() : '无'}`);

    } catch (error) {
      log('red', `❌ 卡密激活失败: ${error.message}`);
      console.error(error);
    }

    // 5. 测试重复使用（应该失败）
    log('blue', '\n🔄 测试重复使用卡密（应该失败）...');
    try {
      await useCard(testCard.code, testUser.username);
      log('red', '❌ 重复使用卡密应该失败，但却成功了！');
    } catch (error) {
      log('green', `✅ 正确阻止了重复使用: ${error.message}`);
    }

    log('green', '\n🎉 卡密激活逻辑测试完成！');

  } catch (error) {
    log('red', `❌ 测试失败: ${error.message}`);
    console.error(error);
  } finally {
    await pool.end();
  }
}

// 主函数
async function main() {
  try {
    await testCardActivation();
  } catch (error) {
    log('red', `❌ 程序执行失败: ${error.message}`);
    process.exit(1);
  }
}

// 执行脚本
if (require.main === module) {
  main();
}

module.exports = { testCardActivation };
