Node.js (大脑) 指挥逻辑更新手册 (Clash API 迁移指南)
1. 概述
1.1. 背景

在之前的调试中，我们发现所使用的 sing-box v1.11.15 预编译版本不包含 Controller API 功能，导致无法通过该接口对 sing-box 进行动态控制。

为了实现我们的核心目标——动态切换代理节点，我们最终采用了 sing-box 内置的、兼容性极佳的 Clash API 作为新的指挥通道。

1.2. 目标

本手册旨在指导您将 Node.js 应用中负责切换节点的控制逻辑，从原先的 Controller API 格式，无缝迁移到 Clash API 格式。

1.3. 核心思想

您的应用（大脑）的整体故障切换逻辑保持不变。即：

维护一个健康节点池。

通过一个节点发起请求。

如果请求在业务层面失败，则将该节点从池中移除。

选择一个新的健康节点，并下达切换指令。

用新节点重试。

本次更新**仅涉及第 4 步中的“下达切换指令”**这一具体操作的实现方式。

2. 核心变更点对比

Controller API 和 Clash API 在切换选择器（Selector）节点时，其 API 端点（Endpoint）和请求体（Payload）的格式有所不同。

API 类型	HTTP 方法	端点 (Endpoint) URL	请求体 (Payload)
Controller API (旧)	PUT	/outbounds/<selector_name>	{ "outbound": "节点Tag" }
Clash API (新)	PUT	/proxies/<selector_name>	{ "name": "节点Tag" }

关键差异总结:

路径中的 /outbounds/ 变为了 /proxies/。

请求体 JSON 中的键 outbound 变为了 name。

3. Node.js 代码实现

您只需要修改后端代码中负责发送 API 指令的那个函数。我们之前将其命名为 commandSwitchNode。


3.1. 更新后的 commandSwitchNode 函数

请使用以下新版本的函数，替换掉您代码中旧的实现。

Generated typescript
// 文件: aiproxy_client.js

// --- 全局配置与状态管理 ---
// Clash API 的地址和端口
const CLASH_API_ENDPOINT = "http://127.0.0.1:9090";

// 在 config.json 中定义的 selector 的 tag
const SELECTOR_NAME = "proxy-selector"; 

// ... 其他全局变量，如 PROXY_AGENT, healthyNodeTags 等保持不变

/**
 * 【新版本】通过 Clash API 命令 sing-box 切换到指定的节点
 * @param {string} nodeTag 要切换到的节点的 tag
 */
async function commandSwitchNode(nodeTag) {
    console.log(`[BRAIN] Issuing command via Clash API: Switch selector '${SELECTOR_NAME}' to node '${nodeTag}'`);
    
    try {
        // 使用 PUT 方法
        await axios.put(
            // 构造新的、符合 Clash API 规范的 URL
            // 例如: http://127.0.0.1:9090/proxies/proxy-selector
            `${CLASH_API_ENDPOINT}/proxies/${SELECTOR_NAME}`, 
            
            // 构造新的、符合 Clash API 规范的请求体
            { "name": nodeTag },
            
            // 请求头保持不变
            { headers: { 'Content-Type': 'application/json' } }
        );
        
        // 更新大脑自身的状态记录
        currentNodeTag = nodeTag;
        console.log(`[BRAIN] Switch successful. Current node is now '${currentNodeTag}'`);
        return true;
    } catch (error) {
        // 错误处理逻辑保持不变
        console.error(`[BRAIN] Command to switch to '${nodeTag}' FAILED: ${error.message}`);
        return false;
    }
}

// 提示：您的其他函数，如 isApiFailure, makeRequest, initialize, main 等，
// 均无需做任何修改，因为它们调用的都是 commandSwitchNode 这个高层抽象函数。
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
TypeScript
IGNORE_WHEN_COPYING_END
4. 完整伪代码示例 (更新后)

为了方便您对照和检查，这里提供包含所有部分的完整伪代码，其中 commandSwitchNode 函数已更新。

Generated typescript
const axios = require('axios');
const { SocksProxyAgent } = require('socks-proxy-agent');

// --- 全局配置与状态管理 (已适配 Clash API) ---
const CLASH_API_ENDPOINT = "http://127.0.0.1:9090";
const SELECTOR_NAME = "proxy-selector";
const PROXY_AGENT = new SocksProxyAgent("socks5h://127.0.0.1:1080");

let healthyNodeTags = ["SG-01-vless", "US-01-hy2", "US-02-hy2", "US-03-hy2"];
let currentNodeTag = "";

// --- 核心功能函数 ---

/**
 * 【新版本】通过 Clash API 命令 sing-box 切换到指定的节点
 * @param {string} nodeTag
 */
async function commandSwitchNode(nodeTag) {
    console.log(`[BRAIN] Issuing command via Clash API: Switch selector '${SELECTOR_NAME}' to node '${nodeTag}'`);
    try {
        await axios.put(
            `${CLASH_API_ENDPOINT}/proxies/${SELECTOR_NAME}`,
            { "name": nodeTag },
            { headers: { 'Content-Type': 'application/json' } }
        );
        currentNodeTag = nodeTag;
        console.log(`[BRAIN] Switch successful. Current node is now '${currentNodeTag}'`);
        return true;
    } catch (error) {
        console.error(`[BRAIN] Command to switch to '${nodeTag}' FAILED: ${error.message}`);
        return false;
    }
}

/**
 * 判断是否为业务层面的失败 (无需修改)
 * @param {Error} error
 */
function isApiFailure(error) {
    if (!error.response) return true;
    const status = error.response.status;
    const responseBody = JSON.stringify(error.response.data || "").toLowerCase();
    if ([401, 403, 429].includes(status) || error.code === 'ECONNABORTED') return true; // ECONNABORTED 是 axios 的超时错误码
    if (responseBody.includes("quota_exceeded") || responseBody.includes("too many requests")) return true;
    return false;
}

/**
 * 使用代理发起 API 请求，并实现智能故障切换 (无需修改)
 * @param {string} apiUrl
 */
async function makeRequest(apiUrl) {
    if (healthyNodeTags.length === 0) throw new Error("All proxy nodes have failed.");
    
    try {
        const response = await axios.get(apiUrl, { httpsAgent: PROXY_AGENT, timeout: 15000 });
        console.log(`[BRAIN] Request successful via ${currentNodeTag}`);
        return response.data;
    } catch (error) {
        console.warn(`[BRAIN] Request via ${currentNodeTag} failed: ${error.message}`);
        if (isApiFailure(error)) {
            console.error(`[BRAIN] API-level failure detected for node ${currentNodeTag}. Removing from pool.`);
            const failedNode = currentNodeTag;
            healthyNodeTags = healthyNodeTags.filter(tag => tag !== failedNode);
            if (healthyNodeTags.length === 0) throw new Error("All proxy nodes have failed after the last attempt.");
            const nextNode = healthyNodeTags[0];
            await commandSwitchNode(nextNode);
            console.log(`[BRAIN] Retrying the request with new node ${nextNode}...`);
            return makeRequest(apiUrl);
        } else {
            throw error;
        }
    }
}

/**
 * 初始化系统 (无需修改)
 */
async function initialize() {
    if (healthyNodeTags.length > 0) {
        await commandSwitchNode(healthyNodeTags[0]);
    } else {
        console.error("Initialization failed: No healthy nodes available.");
    }
}

// --- 程序入口 (无需修改) ---
async function main() {
    await initialize();
    try {
        const data = await makeRequest("https://www.google.com"); // 使用一个简单的测试地址
        console.log("Final response received. Length:", data.length);
    } catch (e) {
        console.error("FATAL: Could not complete the request.", e.message);
        console.log("Remaining healthy nodes:", healthyNodeTags);
    }
}

main();
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
TypeScript
IGNORE_WHEN_COPYING_END
5. 总结与验证

本次更新的核心是将一个 API 调用替换为另一个。您需要做的就是：

找到您代码中负责发送切换指令的函数。

将其中的 axios.put 调用的 URL 和请求体，修改为本手册中描述的 Clash API 格式。

确保您的常量（如 API 地址、选择器名称）是正确的。

完成修改后，您的 Node.js“大脑”就具备了通过 Clash API 指挥 sing-box 的能力，我们整个高可用代理网关架构的最后一块拼图也就完成了。