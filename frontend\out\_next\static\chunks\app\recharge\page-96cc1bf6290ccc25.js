(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[514],{3478:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>S});var r=a(5155),s=a(9137),i=a.n(s),l=a(2115),o=a(7168),n=a(8482),d=a(9852),c=a(64),b=a(3999);let m=c.bL,x=l.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(c.B8,{ref:t,className:(0,b.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...s})});x.displayName=c.B8.displayName;let u=l.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(c.l9,{ref:t,className:(0,b.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...s})});u.displayName=c.l9.displayName;let f=l.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(c.UC,{ref:t,className:(0,b.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...s})});f.displayName=c.UC.displayName;var g=a(1539),p=a(8564),h=a(7951),y=a(3311),j=a(1436),v=a(9037),N=a(4186),w=a(7580),k=a(7550),A=a(1586),T=a(6194),D=a(3580),E=a(7492);let O={M:{days:30,name:"月套餐"},Q:{days:90,name:"季度套餐"},H:{days:180,name:"半年套餐"},PM:{days:30,name:"月度PRO套餐"},PQ:{days:90,name:"季度PRO套餐"},PH:{days:180,name:"半年PRO套餐"},PT:{days:1,name:"测试套餐"}},P=[{id:"monthly",name:"月度会员",price:25,originalPrice:35,discount:"省\xa510",quota:"无限次数",validity:30,features:["无限字符转换","高级参数调节","高清音质输出"],popular:!1,icon:g.A,color:"blue",type:"standard",limitedOffer:!0},{id:"quarterly",name:"季度会员",price:55,originalPrice:105,discount:"省\xa550",quota:"无限次数",validity:90,features:["无限字符转换","高级参数调节","高清音质输出"],popular:!0,icon:p.A,color:"purple",type:"standard",limitedOffer:!0},{id:"halfyear",name:"半年会员",price:99,originalPrice:210,discount:"省\xa5111",quota:"无限次数",validity:180,features:["无限字符转换","高级参数调节","高清音质输出"],popular:!1,icon:h.A,color:"gold",type:"standard",limitedOffer:!0}],R=[{id:"monthly-pro",name:"月Pro",price:45,originalPrice:60,discount:"省\xa515",quota:"无限次数",validity:30,features:["无限字符转换","高级参数调节","高清音质输出","多人对话模式"],popular:!1,icon:y.A,color:"gradient-blue",type:"pro",badge:"PRO",limitedOffer:!0},{id:"quarterly-pro",name:"季度Pro",price:120,originalPrice:180,discount:"省\xa560",quota:"无限次数",validity:90,features:["无限字符转换","高级参数调节","高清音质输出","多人对话模式"],popular:!0,icon:j.A,color:"gradient-purple",type:"pro",badge:"PRO",limitedOffer:!0},{id:"halfyear-pro",name:"半年Pro",price:220,originalPrice:360,discount:"省\xa5140",quota:"无限次数",validity:180,features:["无限字符转换","高级参数调节","高清音质输出","多人对话模式"],popular:!1,icon:v.A,color:"gradient-gold",type:"pro",badge:"PRO",limitedOffer:!0}];function S(){let{toast:e}=(0,D.dj)(),[t,a]=(0,l.useState)(""),[s,c]=(0,l.useState)(!1),[b,j]=(0,l.useState)(!1),[v,S]=(0,l.useState)("standard"),[_,I]=(0,l.useState)(!1),[C,L]=(0,l.useState)({isVip:!1,isActive:!1,expireAt:0,remainingDays:null,quotaDisplay:"获取中...",validityDisplay:"获取中...",statusType:"inactive",isLegacyUser:!1,usagePercentage:0,quotaChars:0,usedChars:0,remainingChars:0}),M=e=>e&&e in O?O[e].days:30,q=e=>!!e&&e.startsWith("P"),U=e=>{var t;if(!e)return{tier:"none",displayName:"未开通",badge:null,colors:{primary:"text-gray-600",secondary:"text-gray-500",bg:"bg-gray-50",border:"border-gray-200"}};let a=q(e),r=(null===(t=O[e])||void 0===t?void 0:t.name)||"未知套餐";return"T"===e?{tier:"test",displayName:"测试套餐",badge:"TEST",colors:{primary:"text-orange-600",secondary:"text-orange-500",bg:"bg-orange-50",border:"border-orange-200"}}:a?{tier:"pro",displayName:r,badge:"PRO",colors:{primary:"text-purple-600",secondary:"text-purple-500",bg:"bg-gradient-to-r from-purple-50 to-pink-50",border:"border-purple-200"}}:{tier:"standard",displayName:r,badge:"VIP",colors:{primary:"text-blue-600",secondary:"text-blue-500",bg:"bg-blue-50",border:"border-blue-200"}}},V=()=>{if(!C.isVip||!C.expireAt||null===C.remainingDays)return 0;let e=M(C.type);return Math.max(0,Math.min(100,C.remainingDays/e*100))},z=(e,t)=>{if(t)return"无限字符";let a={monthly:8e4,quarterly:25e4,halfyear:55e4,"monthly-pro":25e4,"quarterly-pro":8e5,"halfyear-pro":2e6}[e];return a?"".concat((a/1e4).toFixed(0),"万字符"):"无限字符"},K=(e,t,a)=>{let r=[...e];if("无限字符转换"===r[0]){if(a)r[0]="无限字符转换";else{let e=z(t,!1);r[0]="".concat(e,"字符转换")}}return r},Z=e=>{let t=Date.now(),a=e.isVip&&t<e.expireAt,r=e.expireAt>0?Math.max(0,Math.ceil((e.expireAt-t)/864e5)):null,s="inactive",i="未开通",l="未开通";if(a){if(s="active",e.isLegacyUser)i="无限字符";else{let t=e.remainingChars||0,a=e.quotaChars||0;i="".concat(t.toLocaleString()," / ").concat(a.toLocaleString()," 字符")}let t=new Date(e.expireAt);l="".concat(t.getFullYear(),"-").concat(String(t.getMonth()+1).padStart(2,"0"),"-").concat(String(t.getDate()).padStart(2,"0"))}else e.isVip&&e.expireAt>0&&(s="expired",i="已过期",l="已过期");L({isVip:e.isVip,isActive:a,expireAt:e.expireAt,remainingDays:r,quotaDisplay:i,validityDisplay:l,statusType:s,type:e.type,remainingTime:e.remainingTime||void 0,isLegacyUser:e.isLegacyUser,usagePercentage:e.usagePercentage,quotaChars:e.quotaChars,usedChars:e.usedChars,remainingChars:e.remainingChars})};(0,l.useEffect)(()=>{I(!0)},[]),(0,l.useEffect)(()=>{(async()=>{if(!T.j2.isLoggedIn()){window.location.href="/login";return}try{let e=await T.j2.getUserQuota();Z(e)}catch(a){console.error("获取VIP信息失败:",a);let t=(0,E.A)(a);L(e=>({...e,quotaDisplay:t.statusDisplay,validityDisplay:t.statusDisplay,isLegacyUser:!1})),e({title:t.toastTitle,description:t.toastDescription,variant:t.variant})}j(!0)})()},[]),(0,l.useEffect)(()=>{if(!C.isVip||!C.expireAt)return;let t=setInterval(()=>{let t=Date.now(),a=Math.max(0,Math.ceil((C.expireAt-t)/864e5));a!==C.remainingDays&&(L(e=>({...e,remainingDays:a})),0===a&&C.isActive&&(L(e=>({...e,isActive:!1,quotaDisplay:"已过期",validityDisplay:"已过期",statusType:"expired"})),e({title:"会员已过期",description:"您的会员已过期，请续费以继续使用",variant:"destructive"})))},6e4);return()=>clearInterval(t)},[C.isVip,C.expireAt,C.remainingDays,C.isActive,e]);let F=async()=>{try{let e=await T.j2.getUserQuota();Z(e)}catch(l){var t,a,r,s;console.error("刷新用户信息失败:",l);let i="获取最新用户信息失败，请手动刷新页面";((null===(t=l.message)||void 0===t?void 0:t.includes("登录"))||(null===(a=l.message)||void 0===a?void 0:a.includes("refresh"))||(null===(r=l.message)||void 0===r?void 0:r.includes("401"))||(null===(s=l.message)||void 0===s?void 0:s.includes("No refresh token available")))&&(i="会话已过期，正在跳转到登录页面..."),e({title:"刷新失败",description:i,variant:"destructive"})}},B=async()=>{if(!t.trim()){e({title:"请输入卡密",description:"请输入有效的充值卡密",variant:"destructive"});return}c(!0);try{await T.uZ.useCard(t.trim()),e({title:"充值成功！",description:"会员权限已激活，正在更新信息..."}),a(""),await F()}catch(a){console.error("充值失败:",a);let t=(0,E.A)(a);e({title:t.toastTitle,description:t.toastDescription,variant:t.variant})}finally{c(!1)}},H=e=>{let{pkg:t,index:a}=e,s=t.icon,i="pro"===t.type,l=i?(e=>{switch(e){case"gradient-blue":return{bg:"bg-gradient-to-br from-blue-500/10 to-cyan-500/10",border:"border-blue-300/50",icon:"bg-gradient-to-r from-blue-100 to-cyan-100",iconColor:"text-blue-600",badge:"bg-gradient-to-r from-blue-500 to-cyan-500"};case"gradient-purple":return{bg:"bg-gradient-to-br from-purple-500/10 to-pink-500/10",border:"border-purple-300/50",icon:"bg-gradient-to-r from-purple-100 to-pink-100",iconColor:"text-purple-600",badge:"bg-gradient-to-r from-purple-500 to-pink-500"};case"gradient-gold":return{bg:"bg-gradient-to-br from-yellow-500/10 to-orange-500/10",border:"border-yellow-300/50",icon:"bg-gradient-to-r from-yellow-100 to-orange-100",iconColor:"text-orange-600",badge:"bg-gradient-to-r from-yellow-500 to-orange-500"};default:return{bg:"bg-gradient-to-br from-gray-500/10 to-slate-500/10",border:"border-gray-300/50",icon:"bg-gradient-to-r from-gray-100 to-slate-100",iconColor:"text-gray-600",badge:"bg-gradient-to-r from-gray-500 to-slate-500"}}})(t.color):null;return(0,r.jsxs)(n.Zp,{className:"relative border-0 shadow-xl bg-white/90 backdrop-blur-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105 overflow-hidden group h-full flex flex-col ".concat(t.popular?"ring-2 ring-purple-400 ring-opacity-50":""," ").concat(i?"".concat(null==l?void 0:l.bg," ").concat(null==l?void 0:l.border," border-2"):""),style:{animationDelay:"".concat(200*a,"ms")},children:[t.limitedOffer&&(0,r.jsx)("div",{className:"absolute top-0 left-0 text-white px-3 py-1 text-xs font-bold rounded-br-lg shadow-lg animate-pulse ".concat(i?"bg-gradient-to-r from-amber-500 to-orange-500":"bg-gradient-to-r from-orange-500 to-red-500"),children:(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(N.A,{className:"w-3 h-3"}),"限时优惠"]})}),i&&t.badge&&(0,r.jsx)("div",{className:"absolute top-0 right-0 ".concat(null==l?void 0:l.badge," text-white px-4 py-1 text-sm font-bold rounded-bl-lg shadow-lg"),children:t.badge}),t.popular&&!i&&(0,r.jsx)("div",{className:"absolute top-0 right-0 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-1 text-sm font-bold rounded-bl-lg",children:"推荐"}),(0,r.jsx)("div",{className:"absolute inset-0 ".concat("bg-gradient-to-r from-purple-500/5 to-pink-500/5"," opacity-0 group-hover:opacity-100 transition-opacity duration-500")}),(0,r.jsxs)(n.aR,{className:"relative text-center pb-4",children:[(0,r.jsx)("div",{className:"flex justify-center mb-4",children:(0,r.jsx)("div",{className:"p-4 rounded-2xl ".concat(i?null==l?void 0:l.icon:"blue"===t.color?"bg-gradient-to-r from-blue-100 to-blue-200":"purple"===t.color?"bg-gradient-to-r from-purple-100 to-pink-200":"bg-gradient-to-r from-yellow-100 to-orange-200"),children:(0,r.jsx)(s,{className:"w-8 h-8 ".concat(i?null==l?void 0:l.iconColor:"blue"===t.color?"text-blue-600":"purple"===t.color?"text-purple-600":"text-orange-600")})})}),(0,r.jsx)(n.ZB,{className:"text-2xl font-bold mb-4 ".concat(i?"bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent":"text-gray-900"),children:t.name}),(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center mb-4",children:[(0,r.jsxs)("div",{className:"text-5xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent mb-2",children:["\xa5",t.price]}),t.originalPrice&&(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("span",{className:"text-lg text-gray-400 line-through",children:["\xa5",t.originalPrice]}),(0,r.jsx)("span",{className:"text-sm bg-red-100 text-red-600 px-2 py-1 rounded-full font-bold",children:t.discount})]})]})]}),(0,r.jsxs)(n.Wu,{className:"relative flex flex-col h-full",children:[(0,r.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"配额"}),(0,r.jsx)("span",{className:"font-bold text-emerald-600",children:z(t.id,C.isLegacyUser||!1)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"有效期"}),(0,r.jsxs)("span",{className:"font-bold text-gray-900",children:[t.validity," 天"]})]})]}),(0,r.jsx)("div",{className:"space-y-3 mb-4 flex-grow",children:K(t.features,t.id,C.isLegacyUser||!1).map((e,t)=>{let a=i&&"多人对话模式"===e;return(0,r.jsxs)("div",{className:"flex items-center gap-3 ".concat(a?"bg-gradient-to-r from-purple-50 to-pink-50 p-2 rounded-lg border border-purple-200/50":""),children:[a?(0,r.jsx)("div",{className:"flex items-center justify-center w-5 h-5 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full",children:(0,r.jsx)(w.A,{className:"w-3 h-3 text-white"})}):(0,r.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(i?"bg-gradient-to-r from-purple-400 to-pink-500":"bg-gradient-to-r from-emerald-400 to-teal-500")}),(0,r.jsxs)("span",{className:"text-sm ".concat(a?"bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent font-bold":"text-gray-700"),children:[e,a&&(0,r.jsx)("span",{className:"ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-purple-500 to-pink-500 text-white",children:"NEW"})]})]},t)})}),(0,r.jsx)(o.$,{className:"button-hover-optimized w-full h-12 font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 ".concat(t.popular||i?"bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white":"bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 text-gray-800 border border-gray-300"),children:t.popular||i?"立即购买":"选择套餐"})]})]},t.id)},W=[{left:12,top:25,duration:9},{left:78,top:40,duration:11},{left:35,top:65,duration:8},{left:88,top:18,duration:12},{left:22,top:75,duration:10},{left:68,top:50,duration:13},{left:55,top:30,duration:9},{left:40,top:85,duration:11}];return(0,r.jsxs)("div",{className:"jsx-770e7f3364bbc005 min-h-screen bg-gradient-to-br from-slate-50 via-emerald-50/30 to-teal-50/20 p-6 relative overflow-hidden",children:[_&&(0,r.jsx)(()=>(0,r.jsx)("div",{className:"absolute inset-0 overflow-hidden pointer-events-none",children:W.map((e,t)=>(0,r.jsx)("div",{className:"absolute w-2 h-2 bg-gradient-to-r from-emerald-400 to-teal-400 rounded-full opacity-20 animate-float",style:{left:"".concat(e.left,"%"),top:"".concat(e.top,"%"),animationDelay:"".concat(2*t,"s"),animationDuration:"".concat(e.duration,"s")}},t))}),{className:"jsx-770e7f3364bbc005"}),(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 absolute top-20 right-20 w-72 h-72 bg-gradient-to-r from-emerald-200/20 to-teal-200/20 rounded-full blur-3xl animate-pulse"}),(0,r.jsx)("div",{style:{animationDelay:"2s"},className:"jsx-770e7f3364bbc005 absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-r from-teal-200/20 to-cyan-200/20 rounded-full blur-3xl animate-pulse"}),(0,r.jsxs)("div",{className:"jsx-770e7f3364bbc005 "+"max-w-7xl mx-auto transition-all duration-1000 ".concat(b?"opacity-100 translate-y-0":"opacity-0 translate-y-8"),children:[(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 mb-2",children:(0,r.jsxs)("div",{className:"jsx-770e7f3364bbc005 flex items-center gap-4 mb-6",children:[(0,r.jsx)(o.$,{onClick:()=>window.history.back(),variant:"outline",size:"lg",className:"w-12 h-12 rounded-full border-2 hover:bg-emerald-50 hover:border-emerald-300 transition-all duration-300",children:(0,r.jsx)(k.A,{className:"w-5 h-5"})}),(0,r.jsxs)("div",{className:"jsx-770e7f3364bbc005",children:[(0,r.jsx)("h1",{className:"jsx-770e7f3364bbc005 text-4xl font-bold bg-gradient-to-r from-gray-900 via-emerald-800 to-teal-800 bg-clip-text text-transparent",children:"充值中心"}),(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 h-1 w-24 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-full mt-2"})]})]})}),(0,r.jsxs)("div",{className:"jsx-770e7f3364bbc005 grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"jsx-770e7f3364bbc005 lg:col-span-1 space-y-4",children:[(0,r.jsxs)(n.Zp,{className:"border-0 shadow-xl bg-white/90 backdrop-blur-xl hover:shadow-2xl transition-all duration-500 relative overflow-hidden group",children:[(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 absolute inset-0 bg-gradient-to-r from-emerald-500/5 to-teal-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),(0,r.jsx)(n.aR,{className:"relative pb-5",children:(0,r.jsxs)(n.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 p-1.5 bg-gradient-to-r from-emerald-100 to-teal-100 rounded-lg",children:(0,r.jsx)(g.A,{className:"w-4 h-4 text-emerald-600"})}),"当前配额"]})}),(0,r.jsx)(n.Wu,{className:"relative",children:(0,r.jsxs)("div",{className:"jsx-770e7f3364bbc005 text-center",children:[(()=>{if(C.isActive&&C.type){let e=U(C.type);return(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 mb-3 flex justify-center",children:(0,r.jsxs)("div",{className:"jsx-770e7f3364bbc005 "+"inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-bold border ".concat(e.colors.bg," ").concat(e.colors.border),children:["pro"===e.tier&&(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 flex items-center justify-center w-4 h-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full",children:(0,r.jsx)(h.A,{className:"w-2.5 h-2.5 text-white"})}),"standard"===e.tier&&(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 flex items-center justify-center w-4 h-4 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full",children:(0,r.jsx)(p.A,{className:"w-2.5 h-2.5 text-white"})}),"test"===e.tier&&(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 flex items-center justify-center w-4 h-4 bg-gradient-to-r from-orange-500 to-red-500 rounded-full",children:(0,r.jsx)(N.A,{className:"w-2.5 h-2.5 text-white"})}),(0,r.jsx)("span",{className:"jsx-770e7f3364bbc005 "+(e.colors.primary||""),children:e.displayName}),e.badge&&(0,r.jsx)("span",{className:"jsx-770e7f3364bbc005 "+"px-1.5 py-0.5 text-xs font-bold rounded ".concat("pro"===e.tier?"bg-gradient-to-r from-purple-500 to-pink-500 text-white":"test"===e.tier?"bg-gradient-to-r from-orange-500 to-red-500 text-white":"bg-gradient-to-r from-blue-500 to-indigo-500 text-white"),children:e.badge})]})})}return(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 mb-3 flex justify-center",children:(0,r.jsxs)("div",{className:"jsx-770e7f3364bbc005 inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-bold border bg-gray-50 border-gray-200",children:[(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 flex items-center justify-center w-4 h-4 bg-gray-400 rounded-full",children:(0,r.jsx)(g.A,{className:"w-2.5 h-2.5 text-white"})}),(0,r.jsx)("span",{className:"jsx-770e7f3364bbc005 text-gray-600",children:"未开通会员"}),(0,r.jsx)("span",{className:"jsx-770e7f3364bbc005 px-1.5 py-0.5 text-xs font-bold rounded bg-gray-400 text-white",children:"FREE"})]})})})(),(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 text-3xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent mb-1",children:C.quotaDisplay}),(0,r.jsx)("p",{className:"jsx-770e7f3364bbc005 text-gray-600 text-sm",children:"配音权限"}),(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 mt-3 w-full bg-gray-200 rounded-full h-2.5 overflow-hidden",children:(0,r.jsx)("div",{style:{width:C.isActive?C.isLegacyUser?"100%":"".concat(Math.max(5,100-(C.usagePercentage||0)),"%"):"0%"},className:"jsx-770e7f3364bbc005 h-full bg-gradient-to-r from-emerald-400 to-teal-500 rounded-full transition-all duration-1000"})}),(0,r.jsx)("p",{className:"jsx-770e7f3364bbc005 text-xs text-gray-500 mt-2",children:C.isActive?(()=>{if(C.isLegacyUser){let e=U(C.type);return"pro"===e.tier?"PRO会员专享无限字符":"test"===e.tier?"测试套餐无限字符":"标准会员无限字符"}{let e=C.usedChars||0,t=C.usagePercentage||0;return"已使用 ".concat(e.toLocaleString()," 字符 (").concat(t.toFixed(1),"%)")}})():"请购买套餐开通会员"})]})})]}),(0,r.jsxs)(n.Zp,{className:"border-0 shadow-xl bg-white/90 backdrop-blur-xl hover:shadow-2xl transition-all duration-500 relative overflow-hidden group",children:[(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),(0,r.jsx)(n.aR,{className:"relative",children:(0,r.jsxs)(n.ZB,{className:"flex items-center gap-3 text-xl",children:[(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 p-2 bg-gradient-to-r from-blue-100 to-purple-100 rounded-xl",children:(0,r.jsx)(N.A,{className:"w-5 h-5 text-blue-600"})}),"有效期"]})}),(0,r.jsx)(n.Wu,{className:"relative",children:(0,r.jsxs)("div",{className:"jsx-770e7f3364bbc005 space-y-4",children:[(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 text-center",children:(0,r.jsxs)("div",{className:"jsx-770e7f3364bbc005 relative inline-block",children:[(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent mb-1",children:C.validityDisplay}),C.isActive&&(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 absolute -top-6 -right-6 w-3 h-3 bg-green-400 rounded-full animate-pulse shadow-lg"})]})}),null!==C.remainingDays&&C.isActive&&(0,r.jsxs)("div",{className:"jsx-770e7f3364bbc005 relative",children:[(0,r.jsxs)("div",{className:"jsx-770e7f3364bbc005 flex items-center justify-between text-xs text-gray-500 mb-2",children:[(0,r.jsxs)("span",{className:"jsx-770e7f3364bbc005 font-semibold text-blue-600",children:["剩余 ",C.remainingDays," 天"]}),(0,r.jsx)("span",{className:"jsx-770e7f3364bbc005",children:"到期日期"})]}),(0,r.jsxs)("div",{className:"jsx-770e7f3364bbc005 relative w-full bg-gray-200 rounded-full h-2.5 overflow-hidden mb-2",children:[(0,r.jsx)("div",{style:{width:"".concat(V(),"%")},className:"jsx-770e7f3364bbc005 absolute inset-0 bg-gradient-to-r from-blue-400 via-purple-500 to-indigo-500 rounded-full transition-all duration-1000 ease-out"}),(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer"}),(0,r.jsx)("div",{style:{left:"".concat(Math.max(1.75,Math.min(96.5,V()-1.75)),"%")},className:"jsx-770e7f3364bbc005 absolute top-1/2 transform -translate-y-1/2 w-3.5 h-3.5 bg-white rounded-full shadow-lg border-2 border-blue-500 transition-all duration-1000",children:(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 absolute inset-0.5 bg-blue-500 rounded-full animate-pulse"})})]}),(0,r.jsxs)("div",{className:"jsx-770e7f3364bbc005 flex justify-between",children:[(0,r.jsxs)("div",{className:"jsx-770e7f3364bbc005 flex flex-col items-center",children:[(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 w-1.5 h-1.5 bg-blue-500 rounded-full mb-0.5 animate-pulse"}),(0,r.jsx)("span",{className:"jsx-770e7f3364bbc005 text-xs text-blue-600 font-semibold",children:"今天"})]}),(0,r.jsxs)("div",{className:"jsx-770e7f3364bbc005 flex flex-col items-center",children:[(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 w-1.5 h-1.5 bg-gray-400 rounded-full mb-0.5"}),(0,r.jsx)("span",{className:"jsx-770e7f3364bbc005 text-xs text-gray-500",children:C.validityDisplay})]})]})]}),(0,r.jsxs)("div",{className:"jsx-770e7f3364bbc005 grid grid-cols-2 gap-2",children:[(0,r.jsxs)("div",{className:"jsx-770e7f3364bbc005 "+"p-2.5 rounded-lg border ".concat("active"===C.statusType?"bg-gradient-to-br from-green-50 to-emerald-50 border-green-200/50":"expired"===C.statusType?"bg-gradient-to-br from-red-50 to-rose-50 border-red-200/50":"bg-gradient-to-br from-gray-50 to-slate-50 border-gray-200/50"),children:[(0,r.jsxs)("div",{className:"jsx-770e7f3364bbc005 flex items-center justify-center gap-1.5 mb-0.5",children:[(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 "+"w-1.5 h-1.5 rounded-full ".concat("active"===C.statusType?"bg-green-500":"expired"===C.statusType?"bg-red-500":"bg-gray-400")}),(0,r.jsx)("span",{className:"jsx-770e7f3364bbc005 "+"text-xs font-semibold ".concat("active"===C.statusType?"text-green-700":"expired"===C.statusType?"text-red-700":"text-gray-700"),children:"活跃状态"})]}),(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 "+"text-base font-bold text-center ".concat("active"===C.statusType?"text-green-800":"expired"===C.statusType?"text-red-800":"text-gray-800"),children:"active"===C.statusType?"正常":"expired"===C.statusType?"已过期":"未开通"})]}),(0,r.jsxs)("div",{className:"jsx-770e7f3364bbc005 bg-gradient-to-br from-blue-50 to-indigo-50 p-2.5 rounded-lg border border-blue-200/50",children:[(0,r.jsxs)("div",{className:"jsx-770e7f3364bbc005 flex items-center justify-center gap-1.5 mb-0.5",children:[(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 w-1.5 h-1.5 bg-blue-500 rounded-full animate-pulse"}),(0,r.jsx)("span",{className:"jsx-770e7f3364bbc005 text-xs font-semibold text-blue-700",children:"剩余时间"})]}),(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 text-base font-bold text-blue-800 text-center",children:null!==C.remainingDays?C.remainingDays>0?"".concat(C.remainingDays,"天"):"已过期":"未开通"})]})]})]})})]}),(0,r.jsxs)(n.Zp,{className:"border-0 shadow-xl bg-white/90 backdrop-blur-xl hover:shadow-2xl transition-all duration-500 relative overflow-hidden group",children:[(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 absolute inset-0 bg-gradient-to-r from-orange-500/5 to-red-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),(0,r.jsx)(n.aR,{className:"relative pb-3",children:(0,r.jsxs)(n.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 p-1.5 bg-gradient-to-r from-orange-100 to-red-100 rounded-lg",children:(0,r.jsx)(A.A,{className:"w-4 h-4 text-orange-600"})}),"卡密充值"]})}),(0,r.jsxs)(n.Wu,{className:"relative space-y-3",children:[(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005",children:(0,r.jsx)(d.p,{value:t,onChange:e=>a(e.target.value),placeholder:"请输入充值卡密",className:"w-full h-10 border-2 border-gray-200 focus:border-orange-400 focus:ring-2 focus:ring-orange-50 transition-all duration-300"})}),(0,r.jsx)(o.$,{onClick:B,disabled:!t.trim()||s,className:"w-full h-10 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300",children:s?(0,r.jsxs)("div",{className:"jsx-770e7f3364bbc005 flex items-center gap-2",children:[(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"充值中..."]}):"立即充值"}),(0,r.jsx)("p",{className:"jsx-770e7f3364bbc005 text-xs text-gray-500 text-center",children:"请确保卡密正确，充值后立即生效"})]})]})]}),(0,r.jsxs)("div",{className:"jsx-770e7f3364bbc005 lg:col-span-2 flex flex-col",children:[(0,r.jsxs)("div",{className:"jsx-770e7f3364bbc005 mb-6",children:[(0,r.jsx)("h2",{className:"jsx-770e7f3364bbc005 text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-2",children:"套餐说明"}),(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-2 shadow-sm",children:(0,r.jsxs)("p",{className:"jsx-770e7f3364bbc005 text-gray-700 text-base leading-relaxed",children:[(0,r.jsx)("span",{className:"jsx-770e7f3364bbc005 inline-block w-2 h-2 bg-blue-500 rounded-full mr-2"}),"可提供测试，如需购买卡密请联系",(0,r.jsx)("span",{className:"jsx-770e7f3364bbc005 animate-rainbow font-bold text-lg text-transparent bg-clip-text bg-gradient-to-r from-purple-500 via-pink-500 via-orange-500 via-cyan-500 to-lime-500 mx-1",children:"sunshine-12-06"}),"微信，购买前请先仔细查看套餐信息。"]})})]}),(0,r.jsxs)(m,{value:v,onValueChange:S,className:"w-full",children:[(0,r.jsxs)(x,{className:"grid w-full grid-cols-2 mb-6 h-12 bg-white/80 backdrop-blur-sm border border-gray-200 shadow-lg",children:[(0,r.jsx)(u,{value:"standard",className:"text-base font-semibold data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-500 data-[state=active]:text-white transition-all duration-300",children:"标准会员"}),(0,r.jsx)(u,{value:"pro",className:"text-base font-semibold data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-pink-500 data-[state=active]:text-white transition-all duration-300",children:(0,r.jsxs)("span",{className:"jsx-770e7f3364bbc005 flex items-center gap-2",children:["PRO会员",(0,r.jsx)(y.A,{className:"w-4 h-4"})]})})]}),(0,r.jsx)(f,{value:"standard",className:"mt-0",children:(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 grid grid-cols-1 md:grid-cols-3 gap-6",children:P.map((e,t)=>(0,r.jsx)(H,{pkg:e,index:t},e.id))})}),(0,r.jsx)(f,{value:"pro",className:"mt-0",children:(0,r.jsx)("div",{className:"jsx-770e7f3364bbc005 grid grid-cols-1 md:grid-cols-3 gap-6",children:R.map((e,t)=>(0,r.jsx)(H,{pkg:e,index:t},e.id))})})]})]})]})]}),(0,r.jsx)(i(),{id:"770e7f3364bbc005",children:"@-webkit-keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-webkit-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-moz-keyframes float{0%,100%{-moz-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-moz-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-o-keyframes float{0%,100%{-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-o-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);-moz-transform:translatey(0px)rotate(0deg);-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-webkit-transform:translatey(-20px)rotate(180deg);-moz-transform:translatey(-20px)rotate(180deg);-o-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-webkit-keyframes shimmer{0%{-webkit-transform:translatex(-100%);transform:translatex(-100%)}100%{-webkit-transform:translatex(100%);transform:translatex(100%)}}@-moz-keyframes shimmer{0%{-moz-transform:translatex(-100%);transform:translatex(-100%)}100%{-moz-transform:translatex(100%);transform:translatex(100%)}}@-o-keyframes shimmer{0%{-o-transform:translatex(-100%);transform:translatex(-100%)}100%{-o-transform:translatex(100%);transform:translatex(100%)}}@keyframes shimmer{0%{-webkit-transform:translatex(-100%);-moz-transform:translatex(-100%);-o-transform:translatex(-100%);transform:translatex(-100%)}100%{-webkit-transform:translatex(100%);-moz-transform:translatex(100%);-o-transform:translatex(100%);transform:translatex(100%)}}.animate-float.jsx-770e7f3364bbc005{-webkit-animation:float 8s ease-in-out infinite;-moz-animation:float 8s ease-in-out infinite;-o-animation:float 8s ease-in-out infinite;animation:float 8s ease-in-out infinite;will-change:transform;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.animate-shimmer.jsx-770e7f3364bbc005{-webkit-animation:shimmer 2s ease-in-out infinite;-moz-animation:shimmer 2s ease-in-out infinite;-o-animation:shimmer 2s ease-in-out infinite;animation:shimmer 2s ease-in-out infinite}"})]})}},3580:(e,t,a)=>{"use strict";a.d(t,{dj:()=>m});var r=a(2115);let s=0,i=new Map,l=e=>{if(i.has(e))return;let t=setTimeout(()=>{i.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);i.set(e,t)},o=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:a}=t;return a?l(a):e.toasts.forEach(e=>{l(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},n=[],d={toasts:[]};function c(e){d=o(d,e),n.forEach(e=>{e(d)})}function b(e){let{...t}=e,a=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>c({type:"DISMISS_TOAST",toastId:a});return c({type:"ADD_TOAST",toast:{...t,id:a,open:!0,onOpenChange:e=>{e||r()}}}),{id:a,dismiss:r,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:a}})}}function m(){let[e,t]=r.useState(d);return r.useEffect(()=>(n.push(t),()=>{let e=n.indexOf(t);e>-1&&n.splice(e,1)}),[e]),{...e,toast:b,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},4272:(e,t,a)=>{Promise.resolve().then(a.bind(a,3478))},7492:(e,t,a)=>{"use strict";a.d(t,{A:()=>l,tu:()=>i});let r={TOKEN_EXPIRED:"TOKEN_EXPIRED",TOKEN_INVALID:"TOKEN_INVALID",TOKEN_TYPE_INVALID:"TOKEN_TYPE_INVALID",NO_TOKEN:"NO_TOKEN",AUTH_ERROR:"AUTH_ERROR",REFRESH_TOKEN_EXPIRED:"REFRESH_TOKEN_EXPIRED"};function s(e){if(null==e?void 0:e.code)return Object.values(r).includes(e.code);if(null==e?void 0:e.message){let t=e.message.toLowerCase();return t.includes("token")||t.includes("expired")||t.includes("unauthorized")||t.includes("401")||t.includes("登录")||t.includes("refresh")}return!1}function i(e,t){let a=s(e);a&&t&&t();let r=s(e)?{title:"认证失败",description:"会话已过期，正在跳转到登录页面...",shouldRedirect:(null==e?void 0:e.shouldRedirect)===void 0||e.shouldRedirect}:{title:"操作失败",description:(null==e?void 0:e.message)||"请检查网络连接后重试",shouldRedirect:!1};return{isAuthError:a,message:r.description,shouldRedirect:r.shouldRedirect}}function l(e){var t;return s(e)?{statusDisplay:"请重新登录",toastTitle:"认证失败",toastDescription:"会话已过期，正在跳转到登录页面...",variant:"destructive"}:(null==e?void 0:null===(t=e.message)||void 0===t?void 0:t.includes("卡密"))?{statusDisplay:"充值失败",toastTitle:"充值失败",toastDescription:"卡密无效或已使用，请检查后重试",variant:"destructive"}:{statusDisplay:"获取失败",toastTitle:"操作失败",toastDescription:(null==e?void 0:e.message)||"请检查网络连接后重试",variant:"destructive"}}},9852:(e,t,a)=>{"use strict";a.d(t,{p:()=>l});var r=a(5155),s=a(2115),i=a(3999);let l=s.forwardRef((e,t)=>{let{className:a,type:s,...l}=e;return(0,r.jsx)("input",{type:s,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:t,...l})});l.displayName="Input"}},e=>{var t=t=>e(e.s=t);e.O(0,[352,640,576,441,684,358],()=>t(4272)),_N_E=e.O()}]);