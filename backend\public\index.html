<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS API Server - 健康检查</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 600px;
            width: 100%;
        }
        h1 {
            margin: 0 0 20px 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .status {
            font-size: 1.2em;
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
        }
        .healthy {
            background: rgba(76, 175, 80, 0.3);
            border: 1px solid rgba(76, 175, 80, 0.5);
        }
        .info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 30px 0;
        }
        .info-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .info-item h3 {
            margin: 0 0 10px 0;
            font-size: 1.1em;
            opacity: 0.8;
        }
        .info-item p {
            margin: 0;
            font-size: 1.3em;
            font-weight: 500;
        }
        .endpoints {
            text-align: left;
            margin: 30px 0;
        }
        .endpoints h3 {
            text-align: center;
            margin-bottom: 20px;
        }
        .endpoint {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px 15px;
            margin: 10px 0;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        .method {
            color: #4CAF50;
            font-weight: bold;
            margin-right: 10px;
        }
        .ws {
            color: #FF9800;
        }
        @media (max-width: 600px) {
            .info {
                grid-template-columns: 1fr;
            }
            .container {
                padding: 20px;
            }
            h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 TTS API Server</h1>
        
        <div class="status healthy" id="status">
            ✅ 服务运行正常
        </div>
        
        <div class="info">
            <div class="info-item">
                <h3>运行时间</h3>
                <p id="uptime">加载中...</p>
            </div>
            <div class="info-item">
                <h3>版本</h3>
                <p id="version">1.0.0</p>
            </div>
            <div class="info-item">
                <h3>环境</h3>
                <p id="environment">加载中...</p>
            </div>
            <div class="info-item">
                <h3>内存使用</h3>
                <p id="memory">加载中...</p>
            </div>
        </div>
        
        <div class="endpoints">
            <h3>📡 API 端点</h3>
            <div class="endpoint">
                <span class="method">POST</span>/api/auth/login - 用户登录
            </div>
            <div class="endpoint">
                <span class="method">POST</span>/api/auth/register - 用户注册
            </div>
            <div class="endpoint">
                <span class="method ws">WS</span>/api/tts/ws/generate - TTS生成 (WebSocket)
            </div>
            <div class="endpoint">
                <span class="method">GET</span>/api/tts/voices - 获取语音列表
            </div>
            <div class="endpoint">
                <span class="method">GET</span>/api/user/quota - 获取用户配额
            </div>
            <div class="endpoint">
                <span class="method">GET</span>/api/admin/stats - 系统统计 (管理员)
            </div>
        </div>
    </div>

    <script>
        // 获取健康检查数据
        async function loadHealthData() {
            try {
                const response = await fetch('/health');
                const data = await response.json();
                
                // 更新运行时间
                const uptime = Math.floor(data.uptime);
                const hours = Math.floor(uptime / 3600);
                const minutes = Math.floor((uptime % 3600) / 60);
                const seconds = uptime % 60;
                document.getElementById('uptime').textContent = `${hours}h ${minutes}m ${seconds}s`;
                
                // 更新版本
                document.getElementById('version').textContent = data.version || '1.0.0';
                
                // 更新环境
                document.getElementById('environment').textContent = data.environment || 'production';
                
                // 更新内存使用
                const memoryMB = Math.round(data.memory.rss / 1024 / 1024);
                document.getElementById('memory').textContent = `${memoryMB} MB`;
                
            } catch (error) {
                console.error('Failed to load health data:', error);
                document.getElementById('status').innerHTML = '❌ 服务状态检查失败';
                document.getElementById('status').className = 'status error';
            }
        }
        
        // 页面加载时获取数据
        loadHealthData();
        
        // 每30秒更新一次
        setInterval(loadHealthData, 30000);
    </script>
</body>
</html>
