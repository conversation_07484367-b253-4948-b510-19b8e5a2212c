# 混合健康检查策略实施指南

## 📋 概述

本文档描述了已实施的混合健康检查策略，该策略结合了懒加载健康检查、隔离池机制和后台修复任务，为代理网关提供智能、高效的节点管理能力。

## 🏗️ 架构设计

### 三层架构

#### 第一层：主工作流（被动+懒加载）
- **懒加载检查**：在分配节点前进行最后一刻验证
- **被动故障转移**：实际业务失败时的兜底机制
- **隔离池机制**：将失效节点放入单独的隔离池而非直接丢弃

#### 第二层：后台修复任务（低频主动）
- **低频检查**：每10-30分钟检查一次隔离池
- **渐进式恢复**：连续多次成功才重新加入健康池
- **资源优化**：只检查已知有问题的节点

#### 第三层：系统韧性
- **自动发现**：通过懒加载检查提前发现问题
- **自动隔离**：快速移除问题节点
- **自动修复**：定期尝试恢复隔离节点

## ⚙️ 配置参数

### 环境变量配置

```bash
# 基础网关配置
NETWORK_MODE=gateway
ENABLE_SINGBOX_GATEWAY=true
SINGBOX_API_ENDPOINT=http://127.0.0.1:9090

# 工作池配置
SINGBOX_WORKER_POOL_SIZE=10
SINGBOX_WORKER_PORT_START=1081
SINGBOX_WORKER_SELECTOR_PREFIX=worker-selector

# 健康检查配置
SINGBOX_HEALTH_CHECK_INTERVAL=30000  # 常规健康检查间隔（30秒）

# 隔离池和后台修复配置
SINGBOX_QUARANTINE_CHECK_INTERVAL=600000  # 隔离池检查间隔（10分钟）
SINGBOX_QUARANTINE_HEALTH_CHECK_TIMEOUT=5000  # 隔离池健康检查超时
SINGBOX_QUARANTINE_RECOVERY_THRESHOLD=2  # 临时隔离恢复所需连续成功次数
SINGBOX_QUARANTINE_PERMANENT_RECOVERY_THRESHOLD=3  # 永久隔离恢复所需连续成功次数
SINGBOX_QUARANTINE_ENABLE_PERMANENT_RECOVERY=true  # 是否允许永久隔离节点恢复
```

## 🔧 核心功能

### 1. 懒加载健康检查

在`acquireWorker()`方法中，每次分配节点前都会进行健康检查：

```javascript
// 懒加载健康检查流程
const candidateNode = this.getNextHealthyNode();
const isHealthy = await this.healthCheck(candidateNode);

if (isHealthy) {
  // 使用健康节点
  nodeTagToUse = candidateNode;
} else {
  // 移入隔离池，尝试下一个节点
  this.moveNodeToQuarantine(candidateNode, 'Lazy health check failed');
}
```

**健康检查目标**：`http://www.gstatic.com/generate_204`
- 轻量级检查，响应快
- 返回204状态码表示健康
- 5秒超时，避免阻塞

### 2. 隔离池机制

#### 隔离类型分类

**临时隔离（temporary）**：
- 网络超时、连接错误等临时问题
- 可以通过后台检查自动恢复
- 需要连续2次成功检查才能恢复

**永久隔离（permanent）**：
- 配额超限（quota_exceeded）
- HTTP 403/429错误
- API密钥无效等
- 需要连续3次成功检查才能恢复（可配置禁用）

#### 隔离池数据结构

```javascript
{
  reason: "失败原因",
  timestamp: **********,
  quarantineType: "temporary|permanent",
  consecutiveFailures: 1,
  consecutiveSuccesses: 0,
  lastHealthCheck: null,
  retryCount: 1
}
```

### 3. 持久化机制

隔离池数据会自动保存到 `backend/logs/quarantine-nodes.json`：

- **自动保存**：节点移入/移出隔离池时自动保存
- **启动恢复**：系统重启时自动加载隔离池状态
- **容错处理**：文件不存在或损坏时优雅降级

### 4. 后台修复任务

独立的定时器定期检查隔离池中的节点：

```javascript
// 每10分钟执行一次
setInterval(async () => {
  await this.performQuarantineCheck();
}, this.config.SINGBOX_QUARANTINE_CHECK_INTERVAL);
```

**恢复策略**：
- 对隔离池中的每个节点执行健康检查
- 成功则增加`consecutiveSuccesses`计数
- 失败则重置`consecutiveSuccesses`为0
- 达到阈值时自动恢复到健康池

## 📊 监控和统计

### 获取隔离池状态

```javascript
// 获取隔离池中的节点
const quarantinedNodes = controller.getQuarantinedNodes();
const temporaryNodes = controller.getQuarantinedNodes('temporary');
const permanentNodes = controller.getQuarantinedNodes('permanent');

// 获取隔离池统计
const stats = controller.getQuarantineStats();
console.log(stats);
// {
//   total: 5,
//   temporary: 3,
//   permanent: 2,
//   oldestQuarantine: { nodeTag: 'node-1', timestamp: ********** },
//   newestQuarantine: { nodeTag: 'node-5', timestamp: ********** }
// }
```

### 系统状态监控

```javascript
const status = proxyGateway.getStatus();
console.log(status);
// {
//   initialized: true,
//   running: true,
//   mode: 'gateway',
//   singboxEnabled: true,
//   healthCheckRunning: true,
//   quarantineCheckRunning: true,
//   quarantineCheckInterval: 600000
// }
```

## 🧪 测试验证

运行测试脚本验证所有功能：

```bash
cd backend
node test-health-check-strategy.js
```

测试覆盖：
- ✅ 懒加载健康检查
- ✅ 隔离池机制（临时/永久）
- ✅ 持久化机制
- ✅ 后台修复任务

## 🚀 使用示例

### 基本使用

```javascript
const { ProxyGateway } = require('./src/gateway/core/ProxyGateway');
const { ConfigAdapter } = require('./src/gateway/adapters/ConfigAdapter');

// 初始化配置
const configAdapter = new ConfigAdapter();
const config = configAdapter.initialize();

// 创建代理网关
const gateway = new ProxyGateway(config);
await gateway.initialize();

// 发起请求（自动使用健康检查策略）
const response = await gateway.request({
  url: 'https://api.example.com/data',
  method: 'GET'
});
```

### 手动节点管理

```javascript
const controller = gateway.getSingboxController();

// 手动标记节点为失败
await controller.markNodeFailed('problematic-node', 'Manual intervention');

// 强制恢复节点
await controller.markNodeHealthy('recovered-node', true);

// 查看隔离池状态
const quarantined = controller.getQuarantinedNodes();
console.log('Quarantined nodes:', quarantined);
```

## 🔍 故障排除

### 常见问题

1. **隔离池检查不工作**
   - 检查 `SINGBOX_QUARANTINE_CHECK_INTERVAL` 是否大于0
   - 确认 `NETWORK_MODE=gateway` 且 `ENABLE_SINGBOX_GATEWAY=true`

2. **节点无法恢复**
   - 检查 `SINGBOX_QUARANTINE_ENABLE_PERMANENT_RECOVERY` 设置
   - 确认节点确实通过了健康检查
   - 查看连续成功次数是否达到阈值

3. **持久化失败**
   - 确认 `backend/logs` 目录存在且可写
   - 检查磁盘空间是否充足

### 日志分析

关键日志标识：
- `[LAZY CHECK]`：懒加载健康检查
- `[QUARANTINE]`：隔离池操作
- `[PASSIVE FAIL]`：被动故障转移

## 📈 性能优化建议

1. **调整检查频率**：根据实际需求调整隔离池检查间隔
2. **配置恢复阈值**：根据网络稳定性调整连续成功次数要求
3. **监控资源使用**：定期检查隔离池大小，避免无限增长
4. **优化健康检查**：选择更快的健康检查目标URL

## 🎯 最佳实践

1. **渐进式部署**：先在测试环境验证，再逐步推广到生产
2. **监控告警**：设置隔离池大小和恢复率的监控告警
3. **定期清理**：定期清理长期无法恢复的永久隔离节点
4. **配置调优**：根据实际网络环境调整各项阈值参数

---

## 📝 更新日志

- **v1.0.0** - 初始实现混合健康检查策略
- 懒加载健康检查
- 隔离池机制（临时/永久）
- 后台修复任务
- 持久化支持
