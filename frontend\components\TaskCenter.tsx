"use client"

import React, { useState, useEffect, useCallback, useRef, forwardRef, useImperativeHandle } from 'react'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { 
  List, 
  Search, 
  X, 
  Clock, 
  Copy, 
  Check, 
  Download, 
  RefreshCw 
} from "lucide-react"
import { TokenManager } from "@/lib/api"

// 任务对象接口
interface Task {
  taskId: string;
  createdAt: number;
  status?: 'processing' | 'complete' | 'failed' | 'unknown';
  downloadUrl?: string;
  isRefreshing?: boolean;
}

// 组件Props接口
interface TaskCenterProps {
  className?: string;
  // 【新增】映射查询函数，用于刷新时获取正确的物理taskId
  getActualTaskIds?: (displayTaskId: string) => string[];
}

// 组件Ref接口 - 简化后，只保留核心功能
export interface TaskCenterRef {
  addTask: (taskId: string) => void;
  updateTaskStatus: (taskId: string, status: 'processing' | 'complete' | 'failed', downloadUrl?: string) => void;
}

// 本地存储键名
const STORAGE_KEY = 'tts_task_center_tasks';

// 任务中心组件
const TaskCenter = forwardRef<TaskCenterRef, TaskCenterProps>(({ className = "", getActualTaskIds }, ref) => {
  // 状态管理
  const [showTaskCenter, setShowTaskCenter] = useState(false)
  const [taskList, setTaskList] = useState<Task[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [filteredTasks, setFilteredTasks] = useState<Task[]>([])
  const [copiedTaskId, setCopiedTaskId] = useState<string | null>(null)

  // 保存到本地存储
  const saveTasksToStorage = useCallback((tasks: Task[]) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(tasks));
    } catch (error) {
      console.error('Failed to save tasks to localStorage:', error);
    }
  }, []);

  // 从本地存储加载
  useEffect(() => {
    try {
      const savedTasks = localStorage.getItem(STORAGE_KEY);
      if (savedTasks) {
        const tasks = JSON.parse(savedTasks);
        setTaskList(tasks);
      }
    } catch (error) {
      console.error('Failed to load tasks from localStorage:', error);
    }
  }, []);

  // 搜索过滤逻辑
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredTasks(taskList);
    } else {
      const query = searchQuery.toLowerCase().trim();
      const filtered = taskList.filter(task =>
        task.taskId.toLowerCase().includes(query)
      );
      setFilteredTasks(filtered);
    }
  }, [taskList, searchQuery]);

  // 添加新任务
  const addTask = useCallback((taskId: string) => {
    const newTask: Task = {
      taskId,
      createdAt: Date.now(),
      status: 'processing'
    };
    
    setTaskList(prev => {
      const updatedTasks = [newTask, ...prev];
      saveTasksToStorage(updatedTasks);
      return updatedTasks;
    });
  }, [saveTasksToStorage]);

  // 更新任务状态
  const updateTaskStatus = useCallback((taskId: string, status: 'processing' | 'complete' | 'failed', downloadUrl?: string) => {
    setTaskList(prev => {
      const updatedTasks = prev.map(task =>
        task.taskId === taskId
          ? { ...task, status, downloadUrl, isRefreshing: false }
          : task
      );
      saveTasksToStorage(updatedTasks);
      return updatedTasks;
    });
  }, [saveTasksToStorage]);

  // 暴露方法给父组件 - 简化后只保留核心功能
  useImperativeHandle(ref, () => ({
    addTask,
    updateTaskStatus
  }), [addTask, updateTaskStatus]);

  // 【优化】获取任务状态 - 支持映射查询，使用正确的物理taskId
  const fetchTaskStatus = useCallback(async (displayTaskId: string) => {
    try {
      // 设置刷新状态
      setTaskList(prev => prev.map(task =>
        task.taskId === displayTaskId ? { ...task, isRefreshing: true } : task
      ));

      const token = TokenManager.getAccessToken();
      if (!token) {
        throw new Error('未找到访问令牌，请重新登录');
      }

      // 【核心修复】获取实际需要查询的物理taskId列表
      const actualTaskIds = getActualTaskIds ? getActualTaskIds(displayTaskId) : [displayTaskId];
      console.log('[TASK-CENTER] Refresh request:', {
        displayTaskId,
        actualTaskIds
      });

      let lastError: Error | null = null;
      let successData: any = null;

      // 【核心逻辑】尝试查询所有可能的物理taskId，直到找到成功的
      for (const physicalTaskId of actualTaskIds) {
        try {
          console.log(`[TASK-CENTER] Trying to fetch status for physical task: ${physicalTaskId}`);

          const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/tts/status/${physicalTaskId}`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            lastError = new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
            console.log(`[TASK-CENTER] Physical task ${physicalTaskId} failed:`, lastError.message);
            continue; // 尝试下一个物理ID
          }

          const data = await response.json();

          // 如果找到了成功的任务，记录并跳出循环
          if (data.status === 'complete' || data.status === 'completed') {
            successData = data;
            console.log(`[TASK-CENTER] Found successful task: ${physicalTaskId}`, data);
            break;
          } else if (data.status === 'processing') {
            // 如果还在处理中，也算是有效响应
            successData = data;
            console.log(`[TASK-CENTER] Found processing task: ${physicalTaskId}`, data);
            break;
          } else {
            // 其他状态（如failed），继续尝试下一个
            lastError = new Error(`Task ${physicalTaskId} status: ${data.status}`);
            console.log(`[TASK-CENTER] Physical task ${physicalTaskId} not successful:`, data.status);
            continue;
          }
        } catch (error: any) {
          lastError = error;
          console.log(`[TASK-CENTER] Error fetching physical task ${physicalTaskId}:`, error.message);
          continue; // 尝试下一个物理ID
        }
      }

      // 如果所有物理ID都尝试失败了，抛出最后一个错误
      if (!successData) {
        throw lastError || new Error('所有物理任务ID查询都失败了');
      }

      const data = successData;

      // 【修复】更新任务状态 - 使用displayTaskId更新任务中心显示
      if (data.status === 'complete' || data.status === 'completed') {
        // 优先使用API返回的下载链接，备用R2直链
        const downloadUrl = data.audioUrl || data.downloadUrl || `https://r2-assets.aispeak.top/audios/${displayTaskId}.mp3`;
        updateTaskStatus(displayTaskId, 'complete', downloadUrl);
        console.log(`[TASK-CENTER] Updated display task ${displayTaskId} to complete`);
      } else if (data.status === 'processing' || data.status === 'pending' || data.status === 'running') {
        updateTaskStatus(displayTaskId, 'processing');
        console.log(`[TASK-CENTER] Updated display task ${displayTaskId} to processing`);
      } else if (data.status === 'failed' || data.status === 'error') {
        updateTaskStatus(displayTaskId, 'failed');
        console.log(`[TASK-CENTER] Updated display task ${displayTaskId} to failed`);
      } else {
        // 未知状态，标记为失败
        updateTaskStatus(displayTaskId, 'failed');
        console.log(`[TASK-CENTER] Updated display task ${displayTaskId} to failed (unknown status: ${data.status})`);
      }

    } catch (error: any) {
      console.error('[TASK-CENTER] Failed to fetch task status:', error);
      // 重置刷新状态，但保持原有状态
      setTaskList(prev => prev.map(task =>
        task.taskId === displayTaskId ? { ...task, isRefreshing: false } : task
      ));

      // 可以考虑显示错误提示给用户
      // 这里暂时只在控制台记录错误
    }
  }, [updateTaskStatus, getActualTaskIds]);

  // 复制任务ID
  const copyTaskId = useCallback(async (taskId: string) => {
    try {
      await navigator.clipboard.writeText(taskId);
      setCopiedTaskId(taskId);
      setTimeout(() => setCopiedTaskId(null), 2000);
    } catch (error) {
      console.error('Failed to copy task ID:', error);
    }
  }, []);

  // 下载音频 - 使用安全的认证下载
  const downloadAudio = useCallback(async (downloadUrl: string, taskId: string) => {
    try {
      console.log('[TASK-CENTER] Initiating secure download:', { downloadUrl, taskId });

      // 生成带时间戳的文件名
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      const fileName = `tts_${year}${month}${day}_${hours}${minutes}${seconds}.mp3`;

      if (downloadUrl) {
        // 使用安全的认证下载
        const token = TokenManager.getAccessToken();
        if (!token) {
          console.error('[TASK-CENTER] No token available for download');
          throw new Error('认证失败，请重新登录');
        }

        const response = await fetch(downloadUrl, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/octet-stream'
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const blob = await response.blob();
        const blobUrl = URL.createObjectURL(blob);

        // 创建下载链接
        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 清理blob URL
        URL.revokeObjectURL(blobUrl);

        console.log('[TASK-CENTER] Secure download completed successfully');
      } else {
        console.error('[TASK-CENTER] No download URL provided');
        throw new Error('Download URL not available');
      }
    } catch (error) {
      console.error('[TASK-CENTER] Download failed:', error);
      // 可以在这里添加用户友好的错误提示
      // 例如：toast({ title: "下载失败", description: "请稍后重试" })
    }
  }, []);

  // 清空所有任务
  const clearAllTasks = useCallback(() => {
    setTaskList([]);
    saveTasksToStorage([]);
  }, [saveTasksToStorage]);

  return (
    <>
      {/* 任务中心按钮 */}
      <button
        onClick={() => setShowTaskCenter(true)}
        className={`relative overflow-hidden px-4 py-2 text-sm font-semibold rounded-2xl transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform group text-white bg-gradient-to-r from-purple-500 via-purple-600 to-indigo-600 hover:from-purple-600 hover:via-purple-700 hover:to-indigo-700 hover:scale-105 backdrop-blur-sm ${className}`}
      >
        {/* 渐变光效背景 */}
        <div className="absolute inset-0 bg-gradient-to-r from-purple-400/20 via-purple-500/20 to-indigo-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        
        {/* 图标和文字 */}
        <List className="w-4 h-4 relative z-10" />
        <span className="relative z-10">任务中心</span>
        
        {/* 任务数量徽章 */}
        {taskList.length > 0 && (
          <span className="relative z-10 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[18px] h-[18px] flex items-center justify-center">
            {taskList.length > 99 ? '99+' : taskList.length}
          </span>
        )}
      </button>

      {/* 任务中心模态框 */}
      <Dialog open={showTaskCenter} onOpenChange={setShowTaskCenter}>
        <DialogContent className="max-w-4xl max-h-[80vh] flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-xl font-bold">
              <List className="w-5 h-5 text-purple-600" />
              任务中心
              <span className="text-sm font-normal text-gray-500">
                ({taskList.length} 个任务)
              </span>
            </DialogTitle>
          </DialogHeader>

          {/* 搜索和操作栏 */}
          <div className="flex items-center gap-3 py-4 border-b border-gray-100">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="搜索任务ID..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 h-10 border-2 border-gray-200 focus:border-purple-400 focus:ring-2 focus:ring-purple-50"
              />
            </div>
            <Button
              onClick={clearAllTasks}
              variant="outline"
              size="sm"
              className="h-10 px-4 border-2 border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300"
            >
              <X className="w-4 h-4 mr-1" />
              清空
            </Button>
          </div>

          {/* 任务列表 */}
          <div className="flex-1 overflow-y-auto">
            {filteredTasks.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-64 text-gray-500">
                <List className="w-12 h-12 mb-4 text-gray-300" />
                <h3 className="text-lg font-semibold mb-2">暂无任务</h3>
                <p className="text-sm text-center max-w-sm">
                  {searchQuery ? "没有找到匹配的任务" : "您还没有创建任何任务，开始使用AI语音转换功能来创建您的第一个任务吧！"}
                </p>
              </div>
            ) : (
              <div className="space-y-3 p-1">
                {filteredTasks.map((task, index) => (
                  <TaskCard 
                    key={task.taskId} 
                    task={task} 
                    index={index}
                    onCopy={copyTaskId}
                    onDownload={downloadAudio}
                    onRefresh={fetchTaskStatus}
                    copiedTaskId={copiedTaskId}
                  />
                ))}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
});

TaskCenter.displayName = 'TaskCenter';

// 任务卡片组件Props接口
interface TaskCardProps {
  task: Task;
  index: number;
  onCopy: (taskId: string) => void;
  onDownload: (downloadUrl: string, taskId: string) => void;
  onRefresh: (taskId: string) => void;
  copiedTaskId: string | null;
}

// 时间格式化函数
const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return '刚刚';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes}分钟前`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours}小时前`;
  } else {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days}天前`;
  }
};

// 状态样式获取函数
const getStatusStyle = (status: string): string => {
  switch (status) {
    case 'processing':
      return 'bg-orange-100 text-orange-600';
    case 'complete':
      return 'bg-green-100 text-green-600';
    case 'failed':
      return 'bg-red-100 text-red-600';
    default:
      return 'bg-gray-100 text-gray-600';
  }
};

// 状态文本获取函数
const getStatusText = (status: string): string => {
  switch (status) {
    case 'processing':
      return '处理中';
    case 'complete':
      return '已完成';
    case 'failed':
      return '失败';
    default:
      return '未知';
  }
};

// 任务卡片组件
const TaskCard: React.FC<TaskCardProps> = ({
  task,
  index,
  onCopy,
  onDownload,
  onRefresh,
  copiedTaskId
}) => (
  <div className="bg-white border-2 border-gray-100 rounded-xl p-4 hover:border-purple-200 hover:shadow-md transition-all duration-200">
    <div className="flex items-start justify-between">
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-2">
          <span className="text-xs font-medium text-purple-600 bg-purple-100 px-2 py-1 rounded-lg">
            #{index + 1}
          </span>
          <span className="text-xs text-gray-500 flex items-center gap-1">
            <Clock className="w-3 h-3" />
            {formatTime(task.createdAt)}
          </span>
          {/* 状态指示器 */}
          {task.status && (
            <span className={`text-xs px-2 py-1 rounded-lg font-medium ${getStatusStyle(task.status)}`}>
              {getStatusText(task.status)}
            </span>
          )}
        </div>

        {/* 任务ID */}
        <div className="flex items-center gap-2 mb-3">
          <span className="text-sm font-mono text-gray-700 bg-gray-50 px-2 py-1 rounded border truncate max-w-[700px]">
            {task.taskId}
          </span>
          <button
            onClick={() => onCopy(task.taskId)}
            className="p-1 rounded hover:bg-gray-100 transition-colors duration-200"
            title="复制任务ID"
          >
            {copiedTaskId === task.taskId ? (
              <Check className="w-3 h-3 text-green-600" />
            ) : (
              <Copy className="w-3 h-3 text-gray-400" />
            )}
          </button>
        </div>
      </div>

      {/* 操作按钮区域 */}
      <div className="flex items-center gap-2 ml-4">
        {/* 下载按钮 */}
        {task.status === 'complete' && task.downloadUrl && (
          <button
            onClick={() => onDownload(task.downloadUrl!, task.taskId)}
            className="p-2 rounded-lg bg-green-100 text-green-600 hover:bg-green-200 hover:text-green-700 transition-all duration-200"
            title="下载音频"
          >
            <Download className="w-4 h-4" />
          </button>
        )}

        {/* 刷新按钮 */}
        <button
          onClick={() => onRefresh(task.taskId)}
          disabled={task.isRefreshing}
          className={`p-2 rounded-lg transition-all duration-200 flex items-center justify-center ${
            task.isRefreshing
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-blue-100 text-blue-600 hover:bg-blue-200 hover:text-blue-700 cursor-pointer'
          }`}
          title="刷新任务状态"
        >
          <RefreshCw className={`w-4 h-4 ${task.isRefreshing ? 'animate-spin' : ''}`} />
        </button>
      </div>
    </div>
  </div>
);

export default TaskCenter;
