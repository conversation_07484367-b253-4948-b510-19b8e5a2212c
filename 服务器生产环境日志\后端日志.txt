[INFO] [2025-07-25T14:22:01.281Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":2,"assignedNode":"kr-kr-036x-idx-20"}
[ERROR] [2025-07-25T14:22:01.281Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[WARN] [2025-07-25T14:22:01.281Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Health check failed {"error":"The operation was aborted due to timeout"}
[WARN] [2025-07-25T14:22:01.281Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Network adapter health check failed
[INFO] [2025-07-25T14:22:26.273Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-3","targetNode":"kr-kr-046x-idx-21","originalNodeTag":"kr-kr-046x-idx-21","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-3","requestBody":{"name":"kr-kr-046x-idx-21"}}
[INFO] [2025-07-25T14:22:26.275Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-3","newNode":"kr-kr-046x-idx-21","fixedNodeTag":"kr-kr-046x-idx-21"}
[INFO] [2025-07-25T14:22:26.275Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":3,"workerPort":1083,"assignedNode":"kr-kr-046x-idx-21","method":"GET","url":"https://httpbin.org/ip"}
[INFO] [2025-07-25T14:22:26.276Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1083","workerId":3,"assignedNode":"kr-kr-046x-idx-21","method":"GET","hasProxy":true,"proxyType":"dispatcher"}
[ERROR] [2025-07-25T14:22:31.277Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"The operation was aborted due to timeout","method":"GET","url":"https://httpbin.org/ip","workerId":3,"assignedNode":"kr-kr-046x-idx-21"} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"The operation was aborted due to timeout\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":3,\"assignedNode\":\"kr-kr-046x-idx-21\"}","method":"GET","url":"https://httpbin.org/ip","workerId":3,"assignedNode":"kr-kr-046x-idx-21","stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"The operation was aborted due to timeout\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":3,\"assignedNode\":\"kr-kr-046x-idx-21\"}\n    at TTSRouteLogger.logError (/var/www/myaitts/src/utils/ttsLogger.js:270:18)\n    at Object.logError (/var/www/myaitts/src/utils/ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (/var/www/myaitts/src/gateway/adapters/NetworkAdapter.js:194:21)\n    at async "}
[INFO] [2025-07-25T14:22:31.278Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":3,"assignedNode":"kr-kr-046x-idx-21"}
[ERROR] [2025-07-25T14:22:31.279Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[WARN] [2025-07-25T14:22:31.279Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Health check failed {"error":"The operation was aborted due to timeout"}
[WARN] [2025-07-25T14:22:31.279Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Network adapter health check failed
[INFO] [2025-07-25T14:22:50.928Z] [user:system] [task:N/A] - Single TTS WebSocket connection established {"ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
[INFO] [2025-07-25T14:22:51.396Z] [user:system] [task:N/A] - [TTS-ROUTE] Route decision: gateway {"decision":"gateway","networkMode":"gateway","gatewayEnabled":true}
[INFO] [2025-07-25T14:22:51.397Z] [user:system] [task:N/A] - [TTS-GATEWAY] Starting gateway request {"action":"Starting gateway request","textLength":957,"voiceId":"Cx2PEJFdr8frSuUVB6yZ","modelId":"eleven_multilingual_v2"}
[INFO] [2025-07-25T14:22:51.398Z] [user:system] [task:N/A] - [TTS-GATEWAY] Sending request via network manager {"action":"Sending request via network manager","url":"https://api.elevenlabs.io/v1/text-to-speech/Cx2PEJFdr8frSuUVB6yZ?allow_unauthenticated=1","payloadSize":1112}
[INFO] [2025-07-25T14:22:51.398Z] [user:system] [task:N/A] - [TTS-ROUTE] Route decision: gateway {"decision":"gateway","networkMode":"gateway","gatewayEnabled":true}
[INFO] [2025-07-25T14:22:51.398Z] [user:system] [task:N/A] - [TTS-GATEWAY] Starting gateway request {"action":"Starting gateway request","textLength":918,"voiceId":"Cx2PEJFdr8frSuUVB6yZ","modelId":"eleven_multilingual_v2"}
[INFO] [2025-07-25T14:22:51.398Z] [user:system] [task:N/A] - [TTS-GATEWAY] Sending request via network manager {"action":"Sending request via network manager","url":"https://api.elevenlabs.io/v1/text-to-speech/Cx2PEJFdr8frSuUVB6yZ?allow_unauthenticated=1","payloadSize":1072}
[INFO] [2025-07-25T14:22:51.400Z] [user:system] [task:N/A] - [TTS-ROUTE] Route decision: gateway {"decision":"gateway","networkMode":"gateway","gatewayEnabled":true}
[INFO] [2025-07-25T14:22:51.400Z] [user:system] [task:N/A] - [TTS-GATEWAY] Starting gateway request {"action":"Starting gateway request","textLength":631,"voiceId":"Cx2PEJFdr8frSuUVB6yZ","modelId":"eleven_multilingual_v2"}
[INFO] [2025-07-25T14:22:51.400Z] [user:system] [task:N/A] - [TTS-GATEWAY] Sending request via network manager {"action":"Sending request via network manager","url":"https://api.elevenlabs.io/v1/text-to-speech/Cx2PEJFdr8frSuUVB6yZ?allow_unauthenticated=1","payloadSize":766}
[INFO] [2025-07-25T14:22:51.408Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-6","targetNode":"kr-kr-066x-idx-23","originalNodeTag":"kr-kr-066x-idx-23","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-6","requestBody":{"name":"kr-kr-066x-idx-23"}}
[INFO] [2025-07-25T14:22:51.409Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-4","targetNode":"kr-kr-056x-idx-22","originalNodeTag":"kr-kr-056x-idx-22","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-4","requestBody":{"name":"kr-kr-056x-idx-22"}}
[INFO] [2025-07-25T14:22:51.412Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-8","targetNode":"kr-kr-076x-idx-24","originalNodeTag":"kr-kr-076x-idx-24","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-8","requestBody":{"name":"kr-kr-076x-idx-24"}}
[INFO] [2025-07-25T14:22:51.414Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-6","newNode":"kr-kr-066x-idx-23","fixedNodeTag":"kr-kr-066x-idx-23"}
[INFO] [2025-07-25T14:22:51.414Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":6,"workerPort":1086,"assignedNode":"kr-kr-066x-idx-23","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/Cx2PEJFdr8frSuUVB6yZ?allow_unauthenticated=1"}
[INFO] [2025-07-25T14:22:51.415Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1086","workerId":6,"assignedNode":"kr-kr-066x-idx-23","method":"POST","hasProxy":true,"proxyType":"dispatcher"}
[INFO] [2025-07-25T14:22:51.419Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-4","newNode":"kr-kr-056x-idx-22","fixedNodeTag":"kr-kr-056x-idx-22"}
[INFO] [2025-07-25T14:22:51.420Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":4,"workerPort":1084,"assignedNode":"kr-kr-056x-idx-22","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/Cx2PEJFdr8frSuUVB6yZ?allow_unauthenticated=1"}
[INFO] [2025-07-25T14:22:51.420Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1084","workerId":4,"assignedNode":"kr-kr-056x-idx-22","method":"POST","hasProxy":true,"proxyType":"dispatcher"}
[INFO] [2025-07-25T14:22:51.422Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-8","newNode":"kr-kr-076x-idx-24","fixedNodeTag":"kr-kr-076x-idx-24"}
[INFO] [2025-07-25T14:22:51.423Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":8,"workerPort":1088,"assignedNode":"kr-kr-076x-idx-24","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/Cx2PEJFdr8frSuUVB6yZ?allow_unauthenticated=1"}
[INFO] [2025-07-25T14:22:51.423Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1088","workerId":8,"assignedNode":"kr-kr-076x-idx-24","method":"POST","hasProxy":true,"proxyType":"dispatcher"}
[ERROR] [2025-07-25T14:22:53.245Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"fetch failed","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/Cx2PEJFdr8frSuUVB6yZ?allow_unauthenticated=1","workerId":8,"assignedNode":"kr-kr-076x-idx-24"} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"POST\",\"url\":\"https://api.elevenlabs.io/v1/text-to-speech/Cx2PEJFdr8frSuUVB6yZ?allow_unauthenticated=1\",\"workerId\":8,\"assignedNode\":\"kr-kr-076x-idx-24\"}","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/Cx2PEJFdr8frSuUVB6yZ?allow_unauthenticated=1","workerId":8,"assignedNode":"kr-kr-076x-idx-24","stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"POST\",\"url\":\"https://api.elevenlabs.io/v1/text-to-speech/Cx2PEJFdr8frSuUVB6yZ?allow_unauthenticated=1\",\"workerId\":8,\"assignedNode\":\"kr-kr-076x-idx-24\"}\n    at TTSRouteLogger.logError (/var/www/myaitts/src/utils/ttsLogger.js:270:18)\n    at Object.logError (/var/www/myaitts/src/utils/ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (/var/www/myaitts/src/gateway/adapters/"}
[WARN] [2025-07-25T14:22:53.246Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Worker pool network connectivity error {"error":"fetch failed","url":"https://api.elevenlabs.io/v1/text-to-speech/Cx2PEJFdr8frSuUVB6yZ?allow_unauthenticated=1","workerId":8,"nodeTag":"kr-kr-076x-idx-24"}
[WARN] [2025-07-25T14:22:53.246Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: kr-kr-076x-idx-24 {"reason":"Network error: fetch failed"}
[INFO] [2025-07-25T14:22:53.247Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"kr-kr-076x-idx-24","reason":"Network error: fetch failed","quarantineType":"temporary","remainingHealthyNodes":106,"totalQuarantinedNodes":10}
[INFO] [2025-07-25T14:22:53.247Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":8,"assignedNode":"kr-kr-076x-idx-24"}
[ERROR] [2025-07-25T14:22:53.247Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[ERROR] [2025-07-25T14:22:53.248Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[ERROR] [2025-07-25T14:22:53.248Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in gateway mode {"mode":"gateway","error":"fetch failed","totalDuration":"1848ms"} {"mode":"gateway","error":"[TTS-ERROR] Failed in gateway mode {\"mode\":\"gateway\",\"error\":\"fetch failed\",\"totalDuration\":\"1848ms\"}","totalDuration":"1848ms","stack":"Error: [TTS-ERROR] Failed in gateway mode {\"mode\":\"gateway\",\"error\":\"fetch failed\",\"totalDuration\":\"1848ms\"}\n    at TTSRouteLogger.logError (/var/www/myaitts/src/utils/ttsLogger.js:270:18)\n    at Object.logError (/var/www/myaitts/src/utils/ttsLogger.js:303:48)\n    at generateSpeechWithGateway (/var/www/myaitts/src/utils/ttsUtils.js:1322:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async generateSpeechSmart (/var/www/myaitts/src/utils/ttsUtils.js:13"}
[ERROR] [2025-07-25T14:22:53.248Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in smart mode {"mode":"smart","error":"fetch failed","duration":"1848ms"} {"mode":"smart","error":"[TTS-ERROR] Failed in smart mode {\"mode\":\"smart\",\"error\":\"fetch failed\",\"duration\":\"1848ms\"}","duration":"1848ms","stack":"Error: [TTS-ERROR] Failed in smart mode {\"mode\":\"smart\",\"error\":\"fetch failed\",\"duration\":\"1848ms\"}\n    at TTSRouteLogger.logError (/var/www/myaitts/src/utils/ttsLogger.js:270:18)\n    at Object.logError (/var/www/myaitts/src/utils/ttsLogger.js:303:48)\n    at generateSpeechSmart (/var/www/myaitts/src/utils/ttsUtils.js:1417:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /var/www/myaitts/src/utils/ttsUtils.js:310:29"}
[INFO] [2025-07-25T14:22:56.270Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-9","targetNode":"kr-kr-096x-idx-26","originalNodeTag":"kr-kr-096x-idx-26","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-9","requestBody":{"name":"kr-kr-096x-idx-26"}}
[INFO] [2025-07-25T14:22:56.272Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-9","newNode":"kr-kr-096x-idx-26","fixedNodeTag":"kr-kr-096x-idx-26"}
[INFO] [2025-07-25T14:22:56.273Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":9,"workerPort":1089,"assignedNode":"kr-kr-096x-idx-26","method":"GET","url":"https://httpbin.org/ip"}
[INFO] [2025-07-25T14:22:56.273Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1089","workerId":9,"assignedNode":"kr-kr-096x-idx-26","method":"GET","hasProxy":true,"proxyType":"dispatcher"}
[ERROR] [2025-07-25T14:22:56.361Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"fetch failed","method":"GET","url":"https://httpbin.org/ip","workerId":9,"assignedNode":"kr-kr-096x-idx-26"} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":9,\"assignedNode\":\"kr-kr-096x-idx-26\"}","method":"GET","url":"https://httpbin.org/ip","workerId":9,"assignedNode":"kr-kr-096x-idx-26","stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":9,\"assignedNode\":\"kr-kr-096x-idx-26\"}\n    at TTSRouteLogger.logError (/var/www/myaitts/src/utils/ttsLogger.js:270:18)\n    at Object.logError (/var/www/myaitts/src/utils/ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (/var/www/myaitts/src/gateway/adapters/NetworkAdapter.js:194:21)\n    at process.processTicksAndRejections "}
[WARN] [2025-07-25T14:22:56.362Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Worker pool network connectivity error {"error":"fetch failed","url":"https://httpbin.org/ip","workerId":9,"nodeTag":"kr-kr-096x-idx-26"}
[WARN] [2025-07-25T14:22:56.363Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: kr-kr-096x-idx-26 {"reason":"Network error: fetch failed"}
[INFO] [2025-07-25T14:22:56.364Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"kr-kr-096x-idx-26","reason":"Network error: fetch failed","quarantineType":"temporary","remainingHealthyNodes":105,"totalQuarantinedNodes":11}
[INFO] [2025-07-25T14:22:56.364Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":9,"assignedNode":"kr-kr-096x-idx-26"}
[ERROR] [2025-07-25T14:22:56.364Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[WARN] [2025-07-25T14:22:56.364Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Health check failed {"error":"fetch failed"}
[WARN] [2025-07-25T14:22:56.365Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Network adapter health check failed
[WARN] [2025-07-25T14:22:56.366Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: kr-kr-056x-idx-22 {"reason":"Health check failed"}
[INFO] [2025-07-25T14:22:56.367Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"kr-kr-056x-idx-22","reason":"Health check failed","quarantineType":"temporary","remainingHealthyNodes":104,"totalQuarantinedNodes":12}
[ERROR] [2025-07-25T14:22:56.683Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"fetch failed","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/Cx2PEJFdr8frSuUVB6yZ?allow_unauthenticated=1","workerId":4,"assignedNode":"kr-kr-056x-idx-22"} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"POST\",\"url\":\"https://api.elevenlabs.io/v1/text-to-speech/Cx2PEJFdr8frSuUVB6yZ?allow_unauthenticated=1\",\"workerId\":4,\"assignedNode\":\"kr-kr-056x-idx-22\"}","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/Cx2PEJFdr8frSuUVB6yZ?allow_unauthenticated=1","workerId":4,"assignedNode":"kr-kr-056x-idx-22","stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"POST\",\"url\":\"https://api.elevenlabs.io/v1/text-to-speech/Cx2PEJFdr8frSuUVB6yZ?allow_unauthenticated=1\",\"workerId\":4,\"assignedNode\":\"kr-kr-056x-idx-22\"}\n    at TTSRouteLogger.logError (/var/www/myaitts/src/utils/ttsLogger.js:270:18)\n    at Object.logError (/var/www/myaitts/src/utils/ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (/var/www/myaitts/src/gateway/adapters/"}
[WARN] [2025-07-25T14:22:56.684Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Worker pool network connectivity error {"error":"fetch failed","url":"https://api.elevenlabs.io/v1/text-to-speech/Cx2PEJFdr8frSuUVB6yZ?allow_unauthenticated=1","workerId":4,"nodeTag":"kr-kr-056x-idx-22"}
[WARN] [2025-07-25T14:22:56.684Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: kr-kr-056x-idx-22 {"reason":"Network error: fetch failed"}
[INFO] [2025-07-25T14:22:56.685Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"kr-kr-056x-idx-22","reason":"Network error: fetch failed","quarantineType":"temporary","remainingHealthyNodes":104,"totalQuarantinedNodes":12}
[INFO] [2025-07-25T14:22:56.685Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":4,"assignedNode":"kr-kr-056x-idx-22"}
[ERROR] [2025-07-25T14:22:56.685Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[ERROR] [2025-07-25T14:22:56.685Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[ERROR] [2025-07-25T14:22:56.686Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in gateway mode {"mode":"gateway","error":"fetch failed","totalDuration":"5288ms"} {"mode":"gateway","error":"[TTS-ERROR] Failed in gateway mode {\"mode\":\"gateway\",\"error\":\"fetch failed\",\"totalDuration\":\"5288ms\"}","totalDuration":"5288ms","stack":"Error: [TTS-ERROR] Failed in gateway mode {\"mode\":\"gateway\",\"error\":\"fetch failed\",\"totalDuration\":\"5288ms\"}\n    at TTSRouteLogger.logError (/var/www/myaitts/src/utils/ttsLogger.js:270:18)\n    at Object.logError (/var/www/myaitts/src/utils/ttsLogger.js:303:48)\n    at generateSpeechWithGateway (/var/www/myaitts/src/utils/ttsUtils.js:1322:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async generateSpeechSmart (/var/www/myaitts/src/utils/ttsUtils.js:13"}
[ERROR] [2025-07-25T14:22:56.687Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in smart mode {"mode":"smart","error":"fetch failed","duration":"5291ms"} {"mode":"smart","error":"[TTS-ERROR] Failed in smart mode {\"mode\":\"smart\",\"error\":\"fetch failed\",\"duration\":\"5291ms\"}","duration":"5291ms","stack":"Error: [TTS-ERROR] Failed in smart mode {\"mode\":\"smart\",\"error\":\"fetch failed\",\"duration\":\"5291ms\"}\n    at TTSRouteLogger.logError (/var/www/myaitts/src/utils/ttsLogger.js:270:18)\n    at Object.logError (/var/www/myaitts/src/utils/ttsLogger.js:303:48)\n    at generateSpeechSmart (/var/www/myaitts/src/utils/ttsUtils.js:1417:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /var/www/myaitts/src/utils/ttsUtils.js:310:29"}
[ERROR] [2025-07-25T14:22:56.688Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"fetch failed","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/Cx2PEJFdr8frSuUVB6yZ?allow_unauthenticated=1","workerId":6,"assignedNode":"kr-kr-066x-idx-23"} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"POST\",\"url\":\"https://api.elevenlabs.io/v1/text-to-speech/Cx2PEJFdr8frSuUVB6yZ?allow_unauthenticated=1\",\"workerId\":6,\"assignedNode\":\"kr-kr-066x-idx-23\"}","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/Cx2PEJFdr8frSuUVB6yZ?allow_unauthenticated=1","workerId":6,"assignedNode":"kr-kr-066x-idx-23","stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"POST\",\"url\":\"https://api.elevenlabs.io/v1/text-to-speech/Cx2PEJFdr8frSuUVB6yZ?allow_unauthenticated=1\",\"workerId\":6,\"assignedNode\":\"kr-kr-066x-idx-23\"}\n    at TTSRouteLogger.logError (/var/www/myaitts/src/utils/ttsLogger.js:270:18)\n    at Object.logError (/var/www/myaitts/src/utils/ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (/var/www/myaitts/src/gateway/adapters/"}
[WARN] [2025-07-25T14:22:56.689Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Worker pool network connectivity error {"error":"fetch failed","url":"https://api.elevenlabs.io/v1/text-to-speech/Cx2PEJFdr8frSuUVB6yZ?allow_unauthenticated=1","workerId":6,"nodeTag":"kr-kr-066x-idx-23"}
[WARN] [2025-07-25T14:22:56.689Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: kr-kr-066x-idx-23 {"reason":"Network error: fetch failed"}
[INFO] [2025-07-25T14:22:56.689Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"kr-kr-066x-idx-23","reason":"Network error: fetch failed","quarantineType":"temporary","remainingHealthyNodes":103,"totalQuarantinedNodes":13}
[INFO] [2025-07-25T14:22:56.689Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":6,"assignedNode":"kr-kr-066x-idx-23"}
[ERROR] [2025-07-25T14:22:56.690Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[ERROR] [2025-07-25T14:22:56.690Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[ERROR] [2025-07-25T14:22:56.690Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in gateway mode {"mode":"gateway","error":"fetch failed","totalDuration":"5292ms"} {"mode":"gateway","error":"[TTS-ERROR] Failed in gateway mode {\"mode\":\"gateway\",\"error\":\"fetch failed\",\"totalDuration\":\"5292ms\"}","totalDuration":"5292ms","stack":"Error: [TTS-ERROR] Failed in gateway mode {\"mode\":\"gateway\",\"error\":\"fetch failed\",\"totalDuration\":\"5292ms\"}\n    at TTSRouteLogger.logError (/var/www/myaitts/src/utils/ttsLogger.js:270:18)\n    at Object.logError (/var/www/myaitts/src/utils/ttsLogger.js:303:48)\n    at generateSpeechWithGateway (/var/www/myaitts/src/utils/ttsUtils.js:1322:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async generateSpeechSmart (/var/www/myaitts/src/utils/ttsUtils.js:13"}
[ERROR] [2025-07-25T14:22:56.690Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in smart mode {"mode":"smart","error":"fetch failed","duration":"5292ms"} {"mode":"smart","error":"[TTS-ERROR] Failed in smart mode {\"mode\":\"smart\",\"error\":\"fetch failed\",\"duration\":\"5292ms\"}","duration":"5292ms","stack":"Error: [TTS-ERROR] Failed in smart mode {\"mode\":\"smart\",\"error\":\"fetch failed\",\"duration\":\"5292ms\"}\n    at TTSRouteLogger.logError (/var/www/myaitts/src/utils/ttsLogger.js:270:18)\n    at Object.logError (/var/www/myaitts/src/utils/ttsLogger.js:303:48)\n    at generateSpeechSmart (/var/www/myaitts/src/utils/ttsUtils.js:1417:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /var/www/myaitts/src/utils/ttsUtils.js:310:29"}
[INFO] [2025-07-25T14:22:58.693Z] [user:system] [task:N/A] - [TTS-PROXY] Using fallback mode {"action":"Using fallback mode","strategy":"direct-first-then-proxy"}
[INFO] [2025-07-25T14:22:58.693Z] [user:system] [task:N/A] - [TTS-DIRECT] Attempting direct connection first {"action":"Attempting direct connection first"}
[INFO] [2025-07-25T14:22:58.693Z] [user:system] [task:N/A] - [TTS-DIRECT] Starting direct ElevenLabs request {"action":"Starting direct ElevenLabs request","voiceId":"Cx2PEJFdr8frSuUVB6yZ","modelId":"eleven_multilingual_v2","textLength":957}
[INFO] [2025-07-25T14:23:14.152Z] [user:system] [task:N/A] - [TTS-NETWORK] POST https://api.elevenlabs.io/v1/text-to-speech/Cx2PEJFdr8frSuUVB6yZ {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/Cx2PEJFdr8frSuUVB6yZ","status":200,"duration":"15459ms","responseOk":true}
[INFO] [2025-07-25T14:23:14.741Z] [user:system] [task:N/A] - [TTS-DIRECT] Direct request successful {"action":"Direct request successful","audioSize":1220903,"requestDuration":"15459ms","totalDuration":"16048ms"}
[INFO] [2025-07-25T14:23:14.741Z] [user:system] [task:N/A] - [TTS-SUCCESS] Generated via direct-fallback {"mode":"direct-fallback","audioSize":1220903,"duration":"16048ms"}
[INFO] [2025-07-25T14:23:16.743Z] [user:system] [task:N/A] - [TTS-PROXY] Using fallback mode {"action":"Using fallback mode","strategy":"direct-first-then-proxy"}
[INFO] [2025-07-25T14:23:16.744Z] [user:system] [task:N/A] - [TTS-DIRECT] Attempting direct connection first {"action":"Attempting direct connection first"}
[INFO] [2025-07-25T14:23:16.744Z] [user:system] [task:N/A] - [TTS-DIRECT] Starting direct ElevenLabs request {"action":"Starting direct ElevenLabs request","voiceId":"Cx2PEJFdr8frSuUVB6yZ","modelId":"eleven_multilingual_v2","textLength":918}
[INFO] [2025-07-25T14:23:26.274Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-8","targetNode":"us-us-043x-idx-30","originalNodeTag":"us-us-043x-idx-30","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-8","requestBody":{"name":"us-us-043x-idx-30"}}
[INFO] [2025-07-25T14:23:26.278Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-8","newNode":"us-us-043x-idx-30","fixedNodeTag":"us-us-043x-idx-30"}
[INFO] [2025-07-25T14:23:26.278Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":8,"workerPort":1088,"assignedNode":"us-us-043x-idx-30","method":"GET","url":"https://httpbin.org/ip"}
[INFO] [2025-07-25T14:23:26.279Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1088","workerId":8,"assignedNode":"us-us-043x-idx-30","method":"GET","hasProxy":true,"proxyType":"dispatcher"}
[INFO] [2025-07-25T14:23:30.984Z] [user:system] [task:N/A] - [TTS-NETWORK] POST https://api.elevenlabs.io/v1/text-to-speech/Cx2PEJFdr8frSuUVB6yZ {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/Cx2PEJFdr8frSuUVB6yZ","status":200,"duration":"14240ms","responseOk":true}
[ERROR] [2025-07-25T14:23:31.279Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"The operation was aborted due to timeout","method":"GET","url":"https://httpbin.org/ip","workerId":8,"assignedNode":"us-us-043x-idx-30"} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"The operation was aborted due to timeout\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":8,\"assignedNode\":\"us-us-043x-idx-30\"}","method":"GET","url":"https://httpbin.org/ip","workerId":8,"assignedNode":"us-us-043x-idx-30","stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"The operation was aborted due to timeout\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":8,\"assignedNode\":\"us-us-043x-idx-30\"}\n    at TTSRouteLogger.logError (/var/www/myaitts/src/utils/ttsLogger.js:270:18)\n    at Object.logError (/var/www/myaitts/src/utils/ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (/var/www/myaitts/src/gateway/adapters/NetworkAdapter.js:194:21)\n    at async "}
[INFO] [2025-07-25T14:23:31.280Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":8,"assignedNode":"us-us-043x-idx-30"}
[ERROR] [2025-07-25T14:23:31.281Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[WARN] [2025-07-25T14:23:31.281Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Health check failed {"error":"The operation was aborted due to timeout"}
[WARN] [2025-07-25T14:23:31.281Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Network adapter health check failed
[INFO] [2025-07-25T14:23:31.433Z] [user:system] [task:N/A] - [TTS-DIRECT] Direct request successful {"action":"Direct request successful","audioSize":1095934,"requestDuration":"14240ms","totalDuration":"14689ms"}
[INFO] [2025-07-25T14:23:31.433Z] [user:system] [task:N/A] - [TTS-SUCCESS] Generated via direct-fallback {"mode":"direct-fallback","audioSize":1095934,"duration":"14691ms"}
[INFO] [2025-07-25T14:23:33.434Z] [user:system] [task:N/A] - [TTS-PROXY] Using fallback mode {"action":"Using fallback mode","strategy":"direct-first-then-proxy"}
[INFO] [2025-07-25T14:23:33.435Z] [user:system] [task:N/A] - [TTS-DIRECT] Attempting direct connection first {"action":"Attempting direct connection first"}
[INFO] [2025-07-25T14:23:33.435Z] [user:system] [task:N/A] - [TTS-DIRECT] Starting direct ElevenLabs request {"action":"Starting direct ElevenLabs request","voiceId":"Cx2PEJFdr8frSuUVB6yZ","modelId":"eleven_multilingual_v2","textLength":631}
[INFO] [2025-07-25T14:23:43.094Z] [user:system] [task:N/A] - [TTS-NETWORK] POST https://api.elevenlabs.io/v1/text-to-speech/Cx2PEJFdr8frSuUVB6yZ {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/Cx2PEJFdr8frSuUVB6yZ","status":200,"duration":"9659ms","responseOk":true}
[INFO] [2025-07-25T14:23:43.477Z] [user:system] [task:N/A] - [TTS-DIRECT] Direct request successful {"action":"Direct request successful","audioSize":736907,"requestDuration":"9659ms","totalDuration":"10042ms"}
[INFO] [2025-07-25T14:23:43.477Z] [user:system] [task:N/A] - [TTS-SUCCESS] Generated via direct-fallback {"mode":"direct-fallback","audioSize":736907,"duration":"10044ms"}
[INFO] [2025-07-25T14:23:45.502Z] [user:system] [task:N/A] - GET /stream/1d948d82-309a-4205-a30b-69d992acd870 {"status":200,"duration":"18ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1"}
[INFO] [2025-07-25T14:23:56.272Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-9","targetNode":"us-us-053x-idx-31","originalNodeTag":"us-us-053x-idx-31","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-9","requestBody":{"name":"us-us-053x-idx-31"}}
[INFO] [2025-07-25T14:23:56.276Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-9","newNode":"us-us-053x-idx-31","fixedNodeTag":"us-us-053x-idx-31"}
[INFO] [2025-07-25T14:23:56.276Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":9,"workerPort":1089,"assignedNode":"us-us-053x-idx-31","method":"GET","url":"https://httpbin.org/ip"}
[INFO] [2025-07-25T14:23:56.276Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1089","workerId":9,"assignedNode":"us-us-053x-idx-31","method":"GET","hasProxy":true,"proxyType":"dispatcher"}
[ERROR] [2025-07-25T14:24:01.276Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"The operation was aborted due to timeout","method":"GET","url":"https://httpbin.org/ip","workerId":9,"assignedNode":"us-us-053x-idx-31"} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"The operation was aborted due to timeout\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":9,\"assignedNode\":\"us-us-053x-idx-31\"}","method":"GET","url":"https://httpbin.org/ip","workerId":9,"assignedNode":"us-us-053x-idx-31","stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"The operation was aborted due to timeout\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":9,\"assignedNode\":\"us-us-053x-idx-31\"}\n    at TTSRouteLogger.logError (/var/www/myaitts/src/utils/ttsLogger.js:270:18)\n    at Object.logError (/var/www/myaitts/src/utils/ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (/var/www/myaitts/src/gateway/adapters/NetworkAdapter.js:194:21)\n    at async "}
[INFO] [2025-07-25T14:24:01.277Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":9,"assignedNode":"us-us-053x-idx-31"}
[ERROR] [2025-07-25T14:24:01.277Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[WARN] [2025-07-25T14:24:01.277Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Health check failed {"error":"The operation was aborted due to timeout"}
[WARN] [2025-07-25T14:24:01.277Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Network adapter health check failed
[INFO] [2025-07-25T14:24:04.064Z] [user:system] [task:N/A] - GET /download/1d948d82-309a-4205-a30b-69d992acd870 {"status":200,"duration":"51ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1"}
[INFO] [2025-07-25T14:24:26.276Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-10","targetNode":"us-us-063x-idx-32","originalNodeTag":"us-us-063x-idx-32","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-10","requestBody":{"name":"us-us-063x-idx-32"}}
[INFO] [2025-07-25T14:24:26.279Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-10","newNode":"us-us-063x-idx-32","fixedNodeTag":"us-us-063x-idx-32"}
[INFO] [2025-07-25T14:24:26.280Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":10,"workerPort":1090,"assignedNode":"us-us-063x-idx-32","method":"GET","url":"https://httpbin.org/ip"}
[INFO] [2025-07-25T14:24:26.282Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1090","workerId":10,"assignedNode":"us-us-063x-idx-32","method":"GET","hasProxy":true,"proxyType":"dispatcher"}
[ERROR] [2025-07-25T14:24:31.282Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"The operation was aborted due to timeout","method":"GET","url":"https://httpbin.org/ip","workerId":10,"assignedNode":"us-us-063x-idx-32"} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"The operation was aborted due to timeout\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":10,\"assignedNode\":\"us-us-063x-idx-32\"}","method":"GET","url":"https://httpbin.org/ip","workerId":10,"assignedNode":"us-us-063x-idx-32","stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"The operation was aborted due to timeout\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":10,\"assignedNode\":\"us-us-063x-idx-32\"}\n    at TTSRouteLogger.logError (/var/www/myaitts/src/utils/ttsLogger.js:270:18)\n    at Object.logError (/var/www/myaitts/src/utils/ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (/var/www/myaitts/src/gateway/adapters/NetworkAdapter.js:194:21)\n    at async"}
[INFO] [2025-07-25T14:24:31.284Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":10,"assignedNode":"us-us-063x-idx-32"}
[ERROR] [2025-07-25T14:24:31.284Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[WARN] [2025-07-25T14:24:31.284Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Health check failed {"error":"The operation was aborted due to timeout"}
[WARN] [2025-07-25T14:24:31.285Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Network adapter health check failed
[INFO] [2025-07-25T14:24:56.276Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-1","targetNode":"us-us-073x-idx-33","originalNodeTag":"us-us-073x-idx-33","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-1","requestBody":{"name":"us-us-073x-idx-33"}}
[INFO] [2025-07-25T14:24:56.279Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-1","newNode":"us-us-073x-idx-33","fixedNodeTag":"us-us-073x-idx-33"}
[INFO] [2025-07-25T14:24:56.279Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":1,"workerPort":1081,"assignedNode":"us-us-073x-idx-33","method":"GET","url":"https://httpbin.org/ip"}
[INFO] [2025-07-25T14:24:56.279Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1081","workerId":1,"assignedNode":"us-us-073x-idx-33","method":"GET","hasProxy":true,"proxyType":"dispatcher"}
[ERROR] [2025-07-25T14:24:56.428Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"fetch failed","method":"GET","url":"https://httpbin.org/ip","workerId":1,"assignedNode":"us-us-073x-idx-33"} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":1,\"assignedNode\":\"us-us-073x-idx-33\"}","method":"GET","url":"https://httpbin.org/ip","workerId":1,"assignedNode":"us-us-073x-idx-33","stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":1,\"assignedNode\":\"us-us-073x-idx-33\"}\n    at TTSRouteLogger.logError (/var/www/myaitts/src/utils/ttsLogger.js:270:18)\n    at Object.logError (/var/www/myaitts/src/utils/ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (/var/www/myaitts/src/gateway/adapters/NetworkAdapter.js:194:21)\n    at process.processTicksAndRejections "}
[WARN] [2025-07-25T14:24:56.429Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Worker pool network connectivity error {"error":"fetch failed","url":"https://httpbin.org/ip","workerId":1,"nodeTag":"us-us-073x-idx-33"}
[WARN] [2025-07-25T14:24:56.429Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: us-us-073x-idx-33 {"reason":"Network error: fetch failed"}
[INFO] [2025-07-25T14:24:56.429Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"us-us-073x-idx-33","reason":"Network error: fetch failed","quarantineType":"temporary","remainingHealthyNodes":102,"totalQuarantinedNodes":14}
[INFO] [2025-07-25T14:24:56.431Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":1,"assignedNode":"us-us-073x-idx-33"}
[ERROR] [2025-07-25T14:24:56.431Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[WARN] [2025-07-25T14:24:56.431Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Health check failed {"error":"fetch failed"}
[WARN] [2025-07-25T14:24:56.432Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Network adapter health check failed
[INFO] [2025-07-25T14:25:26.274Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-2","targetNode":"us-us-093x-idx-35","originalNodeTag":"us-us-093x-idx-35","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-2","requestBody":{"name":"us-us-093x-idx-35"}}
[INFO] [2025-07-25T14:25:26.277Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-2","newNode":"us-us-093x-idx-35","fixedNodeTag":"us-us-093x-idx-35"}
[INFO] [2025-07-25T14:25:26.277Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":2,"workerPort":1082,"assignedNode":"us-us-093x-idx-35","method":"GET","url":"https://httpbin.org/ip"}
[INFO] [2025-07-25T14:25:26.278Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1082","workerId":2,"assignedNode":"us-us-093x-idx-35","method":"GET","hasProxy":true,"proxyType":"dispatcher"}
[ERROR] [2025-07-25T14:25:26.486Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"fetch failed","method":"GET","url":"https://httpbin.org/ip","workerId":2,"assignedNode":"us-us-093x-idx-35"} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":2,\"assignedNode\":\"us-us-093x-idx-35\"}","method":"GET","url":"https://httpbin.org/ip","workerId":2,"assignedNode":"us-us-093x-idx-35","stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":2,\"assignedNode\":\"us-us-093x-idx-35\"}\n    at TTSRouteLogger.logError (/var/www/myaitts/src/utils/ttsLogger.js:270:18)\n    at Object.logError (/var/www/myaitts/src/utils/ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (/var/www/myaitts/src/gateway/adapters/NetworkAdapter.js:194:21)\n    at process.processTicksAndRejections "}
[WARN] [2025-07-25T14:25:26.486Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Worker pool network connectivity error {"error":"fetch failed","url":"https://httpbin.org/ip","workerId":2,"nodeTag":"us-us-093x-idx-35"}
[WARN] [2025-07-25T14:25:26.487Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: us-us-093x-idx-35 {"reason":"Network error: fetch failed"}
[INFO] [2025-07-25T14:25:26.487Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"us-us-093x-idx-35","reason":"Network error: fetch failed","quarantineType":"temporary","remainingHealthyNodes":101,"totalQuarantinedNodes":15}
[INFO] [2025-07-25T14:25:26.488Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":2,"assignedNode":"us-us-093x-idx-35"}
[ERROR] [2025-07-25T14:25:26.488Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[WARN] [2025-07-25T14:25:26.488Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Health check failed {"error":"fetch failed"}
[WARN] [2025-07-25T14:25:26.488Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Network adapter health check failed
[INFO] [2025-07-25T14:25:56.278Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-3","targetNode":"jp-awsjp-02-idx-37","originalNodeTag":"jp-awsjp-02-idx-37","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-3","requestBody":{"name":"jp-awsjp-02-idx-37"}}
[INFO] [2025-07-25T14:25:56.280Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-3","newNode":"jp-awsjp-02-idx-37","fixedNodeTag":"jp-awsjp-02-idx-37"}
[INFO] [2025-07-25T14:25:56.280Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":3,"workerPort":1083,"assignedNode":"jp-awsjp-02-idx-37","method":"GET","url":"https://httpbin.org/ip"}
[INFO] [2025-07-25T14:25:56.281Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1083","workerId":3,"assignedNode":"jp-awsjp-02-idx-37","method":"GET","hasProxy":true,"proxyType":"dispatcher"}
[INFO] [2025-07-25T14:25:57.006Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker pool response received {"action":"Worker pool response received","status":503,"ok":false,"workerId":3,"assignedNode":"jp-awsjp-02-idx-37"}
[WARN] [2025-07-25T14:25:57.007Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Worker pool error 503, marking node as failed {"url":"https://httpbin.org/ip","status":503,"workerId":3,"nodeTag":"jp-awsjp-02-idx-37"}
[WARN] [2025-07-25T14:25:57.007Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: jp-awsjp-02-idx-37 {"reason":"HTTP 503 error"}
[INFO] [2025-07-25T14:25:57.008Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"jp-awsjp-02-idx-37","reason":"HTTP 503 error","quarantineType":"temporary","remainingHealthyNodes":100,"totalQuarantinedNodes":16}
[INFO] [2025-07-25T14:25:57.008Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":3,"assignedNode":"jp-awsjp-02-idx-37"}
[ERROR] [2025-07-25T14:25:57.009Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[WARN] [2025-07-25T14:25:57.009Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Network adapter health check failed
[INFO] [2025-07-25T14:26:26.278Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-4","targetNode":"jp-awsjp-04-idx-39","originalNodeTag":"jp-awsjp-04-idx-39","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-4","requestBody":{"name":"jp-awsjp-04-idx-39"}}
[INFO] [2025-07-25T14:26:26.280Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-4","newNode":"jp-awsjp-04-idx-39","fixedNodeTag":"jp-awsjp-04-idx-39"}
[INFO] [2025-07-25T14:26:26.281Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":4,"workerPort":1084,"assignedNode":"jp-awsjp-04-idx-39","method":"GET","url":"https://httpbin.org/ip"}
[INFO] [2025-07-25T14:26:26.281Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1084","workerId":4,"assignedNode":"jp-awsjp-04-idx-39","method":"GET","hasProxy":true,"proxyType":"dispatcher"}
[INFO] [2025-07-25T14:26:27.001Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker pool response received {"action":"Worker pool response received","status":503,"ok":false,"workerId":4,"assignedNode":"jp-awsjp-04-idx-39"}
[WARN] [2025-07-25T14:26:27.002Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Worker pool error 503, marking node as failed {"url":"https://httpbin.org/ip","status":503,"workerId":4,"nodeTag":"jp-awsjp-04-idx-39"}
[WARN] [2025-07-25T14:26:27.002Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: jp-awsjp-04-idx-39 {"reason":"HTTP 503 error"}
[INFO] [2025-07-25T14:26:27.003Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"jp-awsjp-04-idx-39","reason":"HTTP 503 error","quarantineType":"temporary","remainingHealthyNodes":99,"totalQuarantinedNodes":17}
[INFO] [2025-07-25T14:26:27.003Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":4,"assignedNode":"jp-awsjp-04-idx-39"}
[ERROR] [2025-07-25T14:26:27.004Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[WARN] [2025-07-25T14:26:27.004Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Network adapter health check failed