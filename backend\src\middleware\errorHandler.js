const { logger } = require('../utils/logger');

// 错误处理中间件
function errorHandler(err, req, res, next) {
  // 记录错误日志
  logger.error(err, {
    method: req.method,
    url: req.url,
    userAgent: req.get('User-Agent'),
    ip: req.ip
  });

  // 默认错误响应
  let status = 500;
  let message = 'Internal Server Error';
  let code = 'INTERNAL_ERROR';

  // 根据错误类型设置响应
  if (err.name === 'ValidationError') {
    status = 400;
    message = err.message;
    code = 'VALIDATION_ERROR';
  } else if (err.name === 'UnauthorizedError' || err.message.includes('Token')) {
    status = 401;
    message = 'Unauthorized';
    code = 'UNAUTHORIZED';
  } else if (err.name === 'ForbiddenError' || err.message.includes('权限')) {
    status = 403;
    message = 'Forbidden';
    code = 'FORBIDDEN';
  } else if (err.name === 'NotFoundError') {
    status = 404;
    message = 'Not Found';
    code = 'NOT_FOUND';
  } else if (err.name === 'ConflictError') {
    status = 409;
    message = err.message;
    code = 'CONFLICT';
  } else if (err.name === 'TooManyRequestsError') {
    status = 429;
    message = 'Too Many Requests';
    code = 'RATE_LIMIT_EXCEEDED';
  }

  // 在开发环境下返回详细错误信息
  const errorResponse = {
    error: message,
    code: code,
    timestamp: new Date().toISOString()
  };

  if (process.env.NODE_ENV === 'development') {
    errorResponse.stack = err.stack;
    errorResponse.details = err.details || null;
  }

  res.status(status).json(errorResponse);
}

// 异步错误包装器
function asyncHandler(fn) {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

// 404处理中间件
function notFoundHandler(req, res, next) {
  const error = new Error(`Route ${req.originalUrl} not found`);
  error.name = 'NotFoundError';
  next(error);
}

module.exports = {
  errorHandler,
  asyncHandler,
  notFoundHandler
};
