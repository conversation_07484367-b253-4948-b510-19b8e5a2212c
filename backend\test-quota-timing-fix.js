#!/usr/bin/env node

require('dotenv').config();
const { Pool } = require('pg');
const { useCard, calculateQuotaDetails } = require('./src/services/authService');
const { getAllPackages } = require('./src/utils/config');

// 彩色日志输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 数据库连接配置 - 使用与其他测试相同的配置
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

// 创建过期用户
async function createExpiredUser(pool, username) {
  const expiredTime = Date.now() - (7 * 24 * 60 * 60 * 1000); // 7天前过期
  
  const vipInfo = {
    type: 'M',
    expireAt: expiredTime,
    quotaChars: 80000,
    usedChars: 30000  // 已用3万，剩余5万
  };

  const usageStats = {
    totalChars: 30000,
    monthlyChars: 5000,
    monthlyResetAt: Date.now() + (30 * 24 * 60 * 60 * 1000)
  };

  await pool.query(`
    INSERT INTO users (username, password_hash, email, vip_info, usage_stats, created_at)
    VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
    ON CONFLICT (username) DO UPDATE SET
      vip_info = EXCLUDED.vip_info,
      usage_stats = EXCLUDED.usage_stats
  `, [
    username,
    'test_hash',
    `${username}@test.com`,
    JSON.stringify(vipInfo),
    JSON.stringify(usageStats)
  ]);

  log('green', `✅ 创建过期用户: ${username} (过期时间: ${new Date(expiredTime).toLocaleString()})`);
  log('cyan', `   原配额: 80000, 已用: 30000, 剩余: 50000`);
}

// 创建未过期用户
async function createActiveUser(pool, username) {
  const futureTime = Date.now() + (15 * 24 * 60 * 60 * 1000); // 15天后过期
  
  const vipInfo = {
    type: 'Q',
    expireAt: futureTime,
    quotaChars: 250000,
    usedChars: 100000  // 已用10万，剩余15万
  };

  const usageStats = {
    totalChars: 100000,
    monthlyChars: 20000,
    monthlyResetAt: Date.now() + (30 * 24 * 60 * 60 * 1000)
  };

  await pool.query(`
    INSERT INTO users (username, password_hash, email, vip_info, usage_stats, created_at)
    VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
    ON CONFLICT (username) DO UPDATE SET
      vip_info = EXCLUDED.vip_info,
      usage_stats = EXCLUDED.usage_stats
  `, [
    username,
    'test_hash',
    `${username}@test.com`,
    JSON.stringify(vipInfo),
    JSON.stringify(usageStats)
  ]);

  log('green', `✅ 创建未过期用户: ${username} (过期时间: ${new Date(futureTime).toLocaleString()})`);
  log('cyan', `   原配额: 250000, 已用: 100000, 剩余: 150000`);
}

// 创建测试卡密
async function createTestCard(pool, packageType) {
  const packages = getAllPackages();
  const packageConfig = packages[packageType];
  
  if (!packageConfig) {
    throw new Error(`未知的套餐类型: ${packageType}`);
  }

  const cardCode = `TEST${packageType}${Date.now().toString().slice(-6)}`;
  
  const packageInfo = {
    type: packageType,
    duration: packageConfig.days * 86400000,
    quotaChars: packageConfig.chars,
    price: packageConfig.price
  };

  await pool.query(`
    INSERT INTO cards (code, package_type, status, package_info, created_at)
    VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)
    ON CONFLICT (code) DO UPDATE SET
      status = EXCLUDED.status,
      package_info = EXCLUDED.package_info
  `, [
    cardCode,
    packageType,
    'unused',
    JSON.stringify(packageInfo)
  ]);

  return cardCode;
}

// 测试配额计算时机修复
async function testQuotaTimingFix() {
  try {
    log('blue', '🧪 开始测试配额计算时机修复...\n');

    // 1. 创建测试用户
    log('yellow', '👥 创建测试用户...');
    await createExpiredUser(pool, 'expired_user');
    await createActiveUser(pool, 'active_user');

    // 2. 测试过期用户续费（应该不保留剩余字符）
    log('\n🔍 测试过期用户续费逻辑...');
    const expiredCardCode = await createTestCard(pool, 'M');
    log('cyan', `创建测试卡密: ${expiredCardCode}`);
    
    const expiredUserVip = await useCard(expiredCardCode, 'expired_user');
    log('green', '✅ 过期用户续费成功');
    log('cyan', `   新配额: ${expiredUserVip.quotaChars} (预期: 80000, 不保留剩余)`);
    log('cyan', `   已用配额: ${expiredUserVip.usedChars} (预期: 0)`);
    
    // 验证结果
    if (expiredUserVip.quotaChars === 80000 && expiredUserVip.usedChars === 0) {
      log('green', '✅ 过期用户配额计算正确：不保留剩余字符');
    } else {
      log('red', '❌ 过期用户配额计算错误');
    }

    // 3. 测试未过期用户续费（应该保留剩余字符）
    log('\n🔍 测试未过期用户续费逻辑...');
    const activeCardCode = await createTestCard(pool, 'M');
    log('cyan', `创建测试卡密: ${activeCardCode}`);
    
    const activeUserVip = await useCard(activeCardCode, 'active_user');
    log('green', '✅ 未过期用户续费成功');
    log('cyan', `   新配额: ${activeUserVip.quotaChars} (预期: 230000, 保留剩余150000 + 新增80000)`);
    log('cyan', `   已用配额: ${activeUserVip.usedChars} (预期: 0)`);
    
    // 验证结果
    if (activeUserVip.quotaChars === 230000 && activeUserVip.usedChars === 0) {
      log('green', '✅ 未过期用户配额计算正确：保留剩余字符');
    } else {
      log('red', '❌ 未过期用户配额计算错误');
    }

    log('\n🎉 配额计算时机修复测试完成！');

  } catch (error) {
    log('red', `❌ 测试失败: ${error.message}`);
    console.error(error);
  } finally {
    await pool.end();
  }
}

// 主函数
async function main() {
  try {
    await testQuotaTimingFix();
  } catch (error) {
    log('red', `❌ 程序执行失败: ${error.message}`);
    process.exit(1);
  }
}

// 执行脚本
if (require.main === module) {
  main();
}

module.exports = { testQuotaTimingFix };
