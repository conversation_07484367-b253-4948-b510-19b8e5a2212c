/**
 * 测试下载URL修复的脚本
 * 验证新的下载URL格式和跨域访问
 */

const WebSocket = require('ws');
const fetch = require('node-fetch');

// 测试配置
const TEST_CONFIG = {
  wsUrl: 'ws://localhost:3001/api/tts/ws/generate',
  token: 'your_test_token_here', // 需要替换为有效的测试token
  testText: '这是一个测试音频下载功能的文本。'
};

async function testDownloadFix() {
  console.log('🧪 开始测试下载URL修复...\n');
  
  return new Promise((resolve, reject) => {
    const ws = new WebSocket(TEST_CONFIG.wsUrl);
    let taskId = null;
    let downloadUrl = null;
    
    // 设置超时
    const timeout = setTimeout(() => {
      ws.close();
      reject(new Error('测试超时（60秒）'));
    }, 60000);
    
    ws.on('open', () => {
      console.log('✅ WebSocket连接已建立');
    });
    
    ws.on('message', async (data) => {
      try {
        const message = JSON.parse(data);
        console.log('📨 收到消息:', message.type, message.message || '');
        
        switch (message.type) {
          case 'initialized':
            taskId = message.taskId;
            console.log(`📋 任务已初始化，ID: ${taskId}`);
            
            // 发送TTS请求
            const request = {
              action: 'start',
              token: TEST_CONFIG.token,
              input: TEST_CONFIG.testText,
              voice: 'Adam',
              model: 'eleven_turbo_v2'
            };
            
            console.log('📤 发送TTS请求...');
            ws.send(JSON.stringify(request));
            break;
            
          case 'complete':
            downloadUrl = message.downloadUrl;
            console.log('🎉 TTS任务完成！');
            console.log(`📁 下载链接: ${downloadUrl}`);
            
            // 测试下载URL
            await testDownloadUrl(downloadUrl);
            
            clearTimeout(timeout);
            ws.close();
            resolve({
              taskId: message.taskId,
              downloadUrl: downloadUrl,
              audioSize: message.audioSize
            });
            break;
            
          case 'error':
            console.error('❌ 任务失败:', message.message);
            clearTimeout(timeout);
            ws.close();
            reject(new Error(message.message));
            break;
        }
      } catch (error) {
        console.error('❌ 解析消息失败:', error);
      }
    });
    
    ws.on('error', (error) => {
      console.error('❌ WebSocket错误:', error);
      clearTimeout(timeout);
      reject(error);
    });
    
    ws.on('close', () => {
      console.log('🔌 WebSocket连接已关闭');
      clearTimeout(timeout);
    });
  });
}

async function testDownloadUrl(downloadUrl) {
  console.log('\n🔍 测试下载URL...');
  
  try {
    // 检查URL格式
    console.log(`📋 URL格式: ${downloadUrl}`);
    
    if (downloadUrl.startsWith('http://localhost:3001')) {
      console.log('✅ URL格式正确 - 包含完整的服务器地址');
    } else {
      console.log('❌ URL格式错误 - 缺少完整的服务器地址');
      return;
    }
    
    // 测试不带token的请求（应该返回401）
    console.log('🔐 测试无认证访问...');
    try {
      const response = await fetch(downloadUrl);
      if (response.status === 401) {
        console.log('✅ 认证检查正常 - 无token时返回401');
      } else {
        console.log(`❌ 认证检查异常 - 状态码: ${response.status}`);
      }
    } catch (error) {
      console.log(`❌ 请求失败: ${error.message}`);
    }
    
    // 测试带token的请求
    console.log('🔑 测试带认证访问...');
    try {
      const urlWithToken = `${downloadUrl}?token=${TEST_CONFIG.token}`;
      const response = await fetch(urlWithToken);
      
      console.log(`📊 响应状态: ${response.status}`);
      console.log(`📋 Content-Type: ${response.headers.get('content-type')}`);
      console.log(`📏 Content-Length: ${response.headers.get('content-length')}`);
      
      if (response.status === 200) {
        console.log('✅ 下载接口正常工作');
        
        // 检查CORS头
        const corsOrigin = response.headers.get('access-control-allow-origin');
        if (corsOrigin === '*') {
          console.log('✅ CORS配置正确');
        } else {
          console.log('❌ CORS配置可能有问题');
        }
        
      } else if (response.status === 404) {
        console.log('❌ 音频文件未找到 - 可能任务还未完成或文件路径错误');
      } else if (response.status === 401) {
        console.log('❌ 认证失败 - token可能无效');
      } else {
        console.log(`❌ 未知错误 - 状态码: ${response.status}`);
      }
      
    } catch (error) {
      console.log(`❌ 请求失败: ${error.message}`);
    }
    
  } catch (error) {
    console.error('❌ 下载测试失败:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  console.log('⚠️  请确保：');
  console.log('1. 后端服务器正在运行 (localhost:3001)');
  console.log('2. 已设置有效的测试token');
  console.log('3. AUDIO_STORAGE_PATH目录存在且可写');
  console.log('4. 用户有相应的VIP权限\n');
  
  testDownloadFix()
    .then((result) => {
      console.log('\n🎉 下载URL修复测试完成！');
      console.log('测试结果:', result);
    })
    .catch((error) => {
      console.error('\n❌ 测试失败:', error.message);
      process.exit(1);
    });
}

module.exports = {
  testDownloadFix,
  testDownloadUrl
};
