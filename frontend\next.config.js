/** @type {import('next').NextConfig} */
const nextConfig = {
  // 启用静态导出以支持Cloudflare Pages
  output: 'export',

  // 禁用图片优化（静态导出不支持）
  images: {
    unoptimized: true
  },

  // 配置静态导出
  trailingSlash: true,

  // 确保兼容Cloudflare Pages
  poweredByHeader: false,

  // 构建时忽略错误（开发阶段）
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },

  // 实验性功能配置
  experimental: {
    // 确保静态导出兼容性
    esmExternals: 'loose',
  },
}

module.exports = nextConfig
