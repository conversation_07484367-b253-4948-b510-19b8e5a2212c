"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[576],{1886:(t,e,r)=>{r.d(e,{Sn:()=>o,tC:()=>s,uE:()=>i});let a={BASE_URL:"https://my.aispeak.top",BACKUP_URL:"https://my.aispeak.top",BACKUP_URLS:"https://my.aispeak.top".split(",").map(t=>t.trim()).filter(t=>t.length>0),ENABLE_BACKUP:!0,TIMEOUT:3e4},o={AUTH:{LOGIN:"/api/auth/login",REGISTER:"/api/auth/register",REFRESH:"/api/auth/refresh",SEND_VERIFICATION:"/api/auth/send-verification",VERIFY_EMAIL:"/api/auth/verify-email",CHAN<PERSON>_PASSWORD:"/api/auth/change-password",FORGOT_PASSWORD:"/api/auth/forgot-password",RESET_PASSWORD:"/api/auth/reset-password"},TTS:{GENERATE:"/api/tts/generate",STATUS:"/api/tts/status",STREAM:"/api/tts/stream",DOWNLOAD:"/api/tts/download"},USER:{QUOTA:"/api/user/quota"},CARD:{USE:"/api/card/use"},AUTO_TAG:{PROCESS:"/api/auto-tag/process",STATUS:"/api/auto-tag/status",ADMIN_STATS:"/api/auto-tag/admin/stats"}};class s{static getAccessToken(){return localStorage.getItem(this.ACCESS_TOKEN_KEY)}static getRefreshToken(){return localStorage.getItem(this.REFRESH_TOKEN_KEY)}static getUserEmail(){return localStorage.getItem(this.USER_EMAIL_KEY)}static setTokens(t,e,r){localStorage.setItem(this.ACCESS_TOKEN_KEY,t),localStorage.setItem(this.REFRESH_TOKEN_KEY,e),r&&localStorage.setItem(this.USER_EMAIL_KEY,r),localStorage.setItem("isLoggedIn","true")}static clearTokens(){localStorage.removeItem(this.ACCESS_TOKEN_KEY),localStorage.removeItem(this.REFRESH_TOKEN_KEY),localStorage.removeItem(this.USER_EMAIL_KEY),localStorage.removeItem("isLoggedIn")}static isLoggedIn(){return!!this.getAccessToken()}}s.ACCESS_TOKEN_KEY="access_token",s.REFRESH_TOKEN_KEY="refresh_token",s.USER_EMAIL_KEY="userEmail";class n{getCurrentApiUrl(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(t&&this.enableBackup){if(this.backupURLs.length>0&&e>=0&&e<this.backupURLs.length)return this.backupURLs[e];if(this.backupURL)return this.backupURL}return this.baseURL}isBackupApiAvailable(){return this.enableBackup&&(this.backupURLs.length>0||!!this.backupURL)}getBackupApiCount(){return this.enableBackup?this.backupURLs.length>0?this.backupURLs.length:+!!this.backupURL:0}getBackupApiUrl(t){return this.enableBackup?this.backupURLs.length>0?t>=0&&t<this.backupURLs.length?this.backupURLs[t]:null:0===t&&this.backupURL?this.backupURL:null:null}createHeaders(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e={"Content-Type":"application/json"};if(t){let t=s.getAccessToken();t&&(e.Authorization="Bearer ".concat(t))}return e}async handleResponse(t){if(!t.ok){let e="HTTP ".concat(t.status,": ").concat(t.statusText),r=null;try{let a=await t.json();e=a.error||a.message||e,r=a.code||null}catch(t){}let a=Error(e);throw r&&(a.code=r),a}return await t.json()}async request(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a="".concat(this.baseURL).concat(t),o=this.createHeaders(r),s={...e,headers:{...o,...e.headers}},n=new AbortController,i=setTimeout(()=>n.abort(),this.timeout);try{let t=await fetch(a,{...s,signal:n.signal});return clearTimeout(i),await this.handleResponse(t)}catch(t){if(clearTimeout(i),t instanceof Error){if("AbortError"===t.name)throw Error("请求超时，请检查网络连接");throw t}throw Error("网络请求失败")}}async get(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return this.request(t,{method:"GET"},e)}async post(t,e){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return this.request(t,{method:"POST",body:e?JSON.stringify(e):void 0},r)}async put(t,e){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return this.request(t,{method:"PUT",body:e?JSON.stringify(e):void 0},r)}async delete(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return this.request(t,{method:"DELETE"},e)}constructor(){this.baseURL=a.BASE_URL,this.backupURL=a.BACKUP_URL,this.backupURLs=a.BACKUP_URLS,this.enableBackup=a.ENABLE_BACKUP,this.timeout=a.TIMEOUT}}let i=new n},3999:(t,e,r)=>{r.d(e,{cn:()=>s});var a=r(2596),o=r(9688);function s(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];return(0,o.QP)((0,a.$)(e))}},6194:(t,e,r)=>{r.d(e,{j2:()=>c,lY:()=>h,uZ:()=>l});var a=r(1886);class o{static async login(t){try{let e=await a.uE.post(a.Sn.AUTH.LOGIN,t);return a.tC.setTokens(e.access_token,e.refresh_token,t.username),e}catch(t){throw console.error("Login error:",t),t}}static async register(t){try{let e=await a.uE.post(a.Sn.AUTH.REGISTER,t);return a.tC.setTokens(e.access_token,e.refresh_token,t.username),e}catch(t){throw console.error("Register error:",t),t}}static async sendVerificationCode(t){try{return await a.uE.post(a.Sn.AUTH.SEND_VERIFICATION,t)}catch(t){throw console.error("Send verification error:",t),t}}static async verifyEmailAndRegister(t){try{let e=await a.uE.post(a.Sn.AUTH.VERIFY_EMAIL,t);return a.tC.setTokens(e.access_token,e.refresh_token,t.email),e}catch(t){throw console.error("Verify email error:",t),t}}static async refreshToken(){try{let t=a.tC.getRefreshToken();if(!t)throw Error("No refresh token available");let e=await a.uE.post(a.Sn.AUTH.REFRESH,{refresh_token:t});return a.tC.setTokens(e.access_token,e.refresh_token),e}catch(t){throw console.error("Refresh token error:",t),a.tC.clearTokens(),t}}static async logout(){try{a.tC.clearTokens()}catch(t){console.error("Logout error:",t),a.tC.clearTokens()}}static async getUserQuota(){try{return await this.withTokenRefresh(async()=>await a.uE.get(a.Sn.USER.QUOTA,!0))}catch(t){throw console.error("Get user quota error:",t),t}}static async changePassword(t){try{return await this.withTokenRefresh(async()=>await a.uE.post(a.Sn.AUTH.CHANGE_PASSWORD,t,!0))}catch(t){throw console.error("Change password error:",t),t}}static async forgotPassword(t){try{return await a.uE.post(a.Sn.AUTH.FORGOT_PASSWORD,t)}catch(t){throw console.error("Forgot password error:",t),t}}static async resetPassword(t){try{return await a.uE.post(a.Sn.AUTH.RESET_PASSWORD,t)}catch(t){throw console.error("Reset password error:",t),t}}static isLoggedIn(){return a.tC.isLoggedIn()}static getCurrentUserEmail(){return a.tC.getUserEmail()}static isAuthError(t){return t.code?"TOKEN_EXPIRED"===t.code||"TOKEN_INVALID"===t.code||"TOKEN_TYPE_INVALID"===t.code||"NO_TOKEN"===t.code:!!t.message&&(t.message.includes("401")||t.message.toLowerCase().includes("token")||t.message.toLowerCase().includes("expired")||t.message.includes("登录")||t.message.includes("unauthorized"))}static async withTokenRefresh(t){try{return await t()}catch(e){if(this.isAuthError(e))try{return await this.refreshToken(),await t()}catch(e){this.logout();let t=Error("Authentication failed - refresh token expired");throw t.code="REFRESH_TOKEN_EXPIRED",t.shouldRedirect=!0,t}throw e}}}class s{static async useCard(t){try{return await o.withTokenRefresh(async()=>await a.uE.post(a.Sn.CARD.USE,{code:t},!0))}catch(t){throw console.error("Use card error:",t),t}}}class n{static async generateSpeech(t){try{return await o.withTokenRefresh(async()=>{let e=await fetch("".concat(a.uE.baseURL).concat(a.Sn.TTS.GENERATE),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a.tC.getAccessToken())},body:JSON.stringify(t)});if(!e.ok){let t=await e.json().catch(()=>({})),r=Error(t.error||"HTTP ".concat(e.status,": ").concat(e.statusText));throw t.type&&(r.type=t.type),r}return await e.arrayBuffer()})}catch(t){throw console.error("Generate speech error:",t),t}}static async startAsyncGeneration(t){try{return await o.withTokenRefresh(async()=>{let e=await fetch("".concat(a.uE.baseURL).concat(a.Sn.TTS.GENERATE),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a.tC.getAccessToken())},body:JSON.stringify(t)});if(!e.ok){let t=await e.json().catch(()=>({})),r=Error(t.error||"HTTP ".concat(e.status,": ").concat(e.statusText));throw t.type&&(r.type=t.type),r}return await e.json()})}catch(t){throw console.error("Start async generation error:",t),t}}static async checkTaskStatus(t){try{return await o.withTokenRefresh(async()=>{let e=await fetch("".concat(a.uE.baseURL).concat(a.Sn.TTS.STATUS,"/").concat(t),{method:"GET",headers:{Authorization:"Bearer ".concat(a.tC.getAccessToken())}});if(!e.ok){let t=await e.json().catch(()=>({}));throw Error(t.error||"HTTP ".concat(e.status,": ").concat(e.statusText))}return await e.json()})}catch(t){throw console.error("Check task status error:",t),t}}static async downloadAudio(t){try{return await o.withTokenRefresh(async()=>{let e=await fetch("".concat(a.uE.baseURL).concat(a.Sn.TTS.DOWNLOAD,"/").concat(t),{method:"GET",headers:{Authorization:"Bearer ".concat(a.tC.getAccessToken())}});if(!e.ok){let t=await e.json().catch(()=>({}));throw Error(t.error||"HTTP ".concat(e.status,": ").concat(e.statusText))}return await e.arrayBuffer()})}catch(t){throw console.error("Download audio error:",t),t}}static async downloadFromDirectUrl(t){try{let e="localhost"===window.location.hostname||"127.0.0.1"===window.location.hostname,r={method:"GET"};if(e)try{await fetch(t,{method:"HEAD",mode:"cors"})}catch(t){if(r.mode="no-cors",t instanceof TypeError&&t.message.includes("CORS"))throw Error("CORS_ERROR: ".concat(t.message))}let a=await fetch(t,r);if(!a.ok)throw Error("R2 direct download failed: HTTP ".concat(a.status,": ").concat(a.statusText));return await a.arrayBuffer()}catch(t){if(t instanceof Error&&t.message.includes("CORS"))throw Error("R2_CORS_ERROR: CORS configuration needed for r2-assets.aispeak.top. ".concat(t.message));throw t}}static async pollTaskUntilComplete(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:60,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:2e3;if(this.downloadCache.has(t))return await this.downloadCache.get(t);let o=this.performPolling(t,e,r,a);this.downloadCache.set(t,o);try{let e=await o;return this.downloadCache.delete(t),e}catch(e){throw this.downloadCache.delete(t),e}}static async performPolling(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:60,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:2e3,o=0,s=a;for(;o<r;)try{let r=await this.checkTaskStatus(t);if(e&&e(r),"complete"===r.status){if(!(r.audioUrl&&r.audioUrl.includes("r2-assets.aispeak.top")))return await this.downloadAudio(t);try{return await this.downloadFromDirectUrl(r.audioUrl)}catch(e){return await this.downloadAudio(t)}}else if("failed"===r.status)throw Error(r.error||"Task failed");await new Promise(t=>setTimeout(t,s)),s=Math.min(1.2*s,1e4),o++}catch(t){if(console.error("Polling attempt ".concat(o+1," failed:"),t),++o<r)await new Promise(t=>setTimeout(t,s)),s=Math.min(1.5*s,1e4);else throw t}throw Error("Task polling timeout - maximum attempts reached")}}n.downloadCache=new Map;class i{static async processText(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"auto";try{return await o.withTokenRefresh(async()=>await a.uE.post(a.Sn.AUTO_TAG.PROCESS,{text:t,language:e},!0))}catch(t){throw console.error("Auto tag process error:",t),t}}static async getStatus(){try{return await o.withTokenRefresh(async()=>await a.uE.get(a.Sn.AUTO_TAG.STATUS,!0))}catch(t){throw console.error("Auto tag status error:",t),t}}}let c=o,l=s,h=i},7168:(t,e,r)=>{r.d(e,{$:()=>l});var a=r(5155),o=r(2115),s=r(9708),n=r(2085),i=r(3999);let c=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=o.forwardRef((t,e)=>{let{className:r,variant:o,size:n,asChild:l=!1,...h}=t,u=l?s.DX:"button";return(0,a.jsx)(u,{className:(0,i.cn)(c({variant:o,size:n,className:r})),ref:e,...h})});l.displayName="Button"},8482:(t,e,r)=>{r.d(e,{Wu:()=>l,ZB:()=>c,Zp:()=>n,aR:()=>i});var a=r(5155),o=r(2115),s=r(3999);let n=o.forwardRef((t,e)=>{let{className:r,...o}=t;return(0,a.jsx)("div",{ref:e,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...o})});n.displayName="Card";let i=o.forwardRef((t,e)=>{let{className:r,...o}=t;return(0,a.jsx)("div",{ref:e,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",r),...o})});i.displayName="CardHeader";let c=o.forwardRef((t,e)=>{let{className:r,...o}=t;return(0,a.jsx)("div",{ref:e,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",r),...o})});c.displayName="CardTitle",o.forwardRef((t,e)=>{let{className:r,...o}=t;return(0,a.jsx)("div",{ref:e,className:(0,s.cn)("text-sm text-muted-foreground",r),...o})}).displayName="CardDescription";let l=o.forwardRef((t,e)=>{let{className:r,...o}=t;return(0,a.jsx)("div",{ref:e,className:(0,s.cn)("p-6 pt-0",r),...o})});l.displayName="CardContent",o.forwardRef((t,e)=>{let{className:r,...o}=t;return(0,a.jsx)("div",{ref:e,className:(0,s.cn)("flex items-center p-6 pt-0",r),...o})}).displayName="CardFooter"}}]);