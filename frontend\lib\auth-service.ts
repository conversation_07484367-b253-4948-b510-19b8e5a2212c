// 认证服务
// 专门处理认证相关的API调用

import {
  apiClient,
  API_ENDPOINTS,
  TokenManager,
  type LoginRequest,
  type LoginResponse,
  type RegisterRequest,
  type SendVerificationRequest,
  type SendVerificationResponse,
  type VerifyEmailRequest,
  type VerifyEmailResponse,
  type UserQuotaResponse,
  type ChangePasswordRequest,
  type ChangePasswordResponse,
  type ForgotPasswordRequest,
  type ForgotPasswordResponse,
  type ResetPasswordRequest,
  type ResetPasswordResponse
} from './api'

export class AuthService {
  // 登录
  static async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await apiClient.post<LoginResponse>(
        API_ENDPOINTS.AUTH.LOGIN,
        credentials
      )

      // 存储token
      TokenManager.setTokens(
        response.access_token,
        response.refresh_token,
        credentials.username
      )

      return response
    } catch (error) {
      console.error('Login error:', error)
      throw error
    }
  }

  // 传统注册（保持兼容性）
  static async register(userData: RegisterRequest): Promise<LoginResponse> {
    try {
      const response = await apiClient.post<LoginResponse>(
        API_ENDPOINTS.AUTH.REGISTER,
        userData
      )

      // 存储token
      TokenManager.setTokens(
        response.access_token,
        response.refresh_token,
        userData.username
      )

      return response
    } catch (error) {
      console.error('Register error:', error)
      throw error
    }
  }

  // 发送邮箱验证码
  static async sendVerificationCode(data: SendVerificationRequest): Promise<SendVerificationResponse> {
    try {
      const response = await apiClient.post<SendVerificationResponse>(
        API_ENDPOINTS.AUTH.SEND_VERIFICATION,
        data
      )

      return response
    } catch (error) {
      console.error('Send verification error:', error)
      throw error
    }
  }

  // 验证邮箱并完成注册
  static async verifyEmailAndRegister(data: VerifyEmailRequest): Promise<VerifyEmailResponse> {
    try {
      const response = await apiClient.post<VerifyEmailResponse>(
        API_ENDPOINTS.AUTH.VERIFY_EMAIL,
        data
      )

      // 存储token
      TokenManager.setTokens(
        response.access_token,
        response.refresh_token,
        data.email
      )

      return response
    } catch (error) {
      console.error('Verify email error:', error)
      throw error
    }
  }

  // 刷新token
  static async refreshToken(): Promise<LoginResponse> {
    try {
      const refreshToken = TokenManager.getRefreshToken()
      if (!refreshToken) {
        throw new Error('No refresh token available')
      }

      const response = await apiClient.post<LoginResponse>(
        API_ENDPOINTS.AUTH.REFRESH,
        { refresh_token: refreshToken }
      )

      // 更新token
      TokenManager.setTokens(
        response.access_token,
        response.refresh_token
      )

      return response
    } catch (error) {
      console.error('Refresh token error:', error)
      // 如果刷新失败，清除所有token
      TokenManager.clearTokens()
      throw error
    }
  }

  // 登出
  static async logout(): Promise<void> {
    try {
      // 清除本地token
      TokenManager.clearTokens()

      // 可以在这里添加服务端登出逻辑
      // await apiClient.post(API_ENDPOINTS.AUTH.LOGOUT, {}, true)

    } catch (error) {
      console.error('Logout error:', error)
      // 即使服务端登出失败，也要清除本地token
      TokenManager.clearTokens()
    }
  }

  // 获取用户配额信息
  static async getUserQuota(): Promise<UserQuotaResponse> {
    try {
      return await this.withTokenRefresh(async () => {
        return await apiClient.get<UserQuotaResponse>(
          API_ENDPOINTS.USER.QUOTA,
          true // 需要认证
        )
      })
    } catch (error) {
      console.error('Get user quota error:', error)
      throw error
    }
  }

  // 修改密码
  static async changePassword(data: ChangePasswordRequest): Promise<ChangePasswordResponse> {
    try {
      return await this.withTokenRefresh(async () => {
        return await apiClient.post<ChangePasswordResponse>(
          API_ENDPOINTS.AUTH.CHANGE_PASSWORD,
          data,
          true // 需要认证
        )
      })
    } catch (error) {
      console.error('Change password error:', error)
      throw error
    }
  }

  // 忘记密码 - 发送重置验证码
  static async forgotPassword(data: ForgotPasswordRequest): Promise<ForgotPasswordResponse> {
    try {
      const response = await apiClient.post<ForgotPasswordResponse>(
        API_ENDPOINTS.AUTH.FORGOT_PASSWORD,
        data
      )

      return response
    } catch (error) {
      console.error('Forgot password error:', error)
      throw error
    }
  }

  // 重置密码
  static async resetPassword(data: ResetPasswordRequest): Promise<ResetPasswordResponse> {
    try {
      const response = await apiClient.post<ResetPasswordResponse>(
        API_ENDPOINTS.AUTH.RESET_PASSWORD,
        data
      )

      return response
    } catch (error) {
      console.error('Reset password error:', error)
      throw error
    }
  }

  // 检查登录状态
  static isLoggedIn(): boolean {
    return TokenManager.isLoggedIn()
  }

  // 获取当前用户邮箱
  static getCurrentUserEmail(): string | null {
    return TokenManager.getUserEmail()
  }

  // 【新增】判断是否为认证错误的辅助函数
  private static isAuthError(error: any): boolean {
    // 优先检查错误码（更可靠）
    if (error.code) {
      return error.code === 'TOKEN_EXPIRED' ||
             error.code === 'TOKEN_INVALID' ||
             error.code === 'TOKEN_TYPE_INVALID' ||
             error.code === 'NO_TOKEN'
    }

    // 兼容旧版：检查错误消息
    if (error.message) {
      return error.message.includes('401') ||
             error.message.toLowerCase().includes('token') ||
             error.message.toLowerCase().includes('expired') ||
             error.message.includes('登录') ||
             error.message.includes('unauthorized')
    }

    return false
  }

  // 自动刷新token的包装器
  static async withTokenRefresh<T>(apiCall: () => Promise<T>): Promise<T> {
    try {
      return await apiCall()
    } catch (error) {
      // 【关键修改】使用新的认证错误判断逻辑
      if (this.isAuthError(error)) {
        // Token可能过期，尝试刷新
        try {
          await this.refreshToken()
          // 重试原始请求
          return await apiCall()
        } catch (refreshError) {
          // 刷新失败，清理本地数据
          this.logout()

          // 【修复】创建一个特殊的认证失败错误，让调用方处理UI和跳转
          const authFailedError = new Error('Authentication failed - refresh token expired')
          ;(authFailedError as any).code = 'REFRESH_TOKEN_EXPIRED'
          ;(authFailedError as any).shouldRedirect = true
          throw authFailedError
        }
      }
      throw error
    }
  }
}

// 卡密服务
export class CardService {
  // 使用卡密充值
  static async useCard(code: string): Promise<any> {
    try {
      return await AuthService.withTokenRefresh(async () => {
        return await apiClient.post(
          API_ENDPOINTS.CARD.USE,
          { code },
          true // 需要认证
        )
      })
    } catch (error) {
      console.error('Use card error:', error)
      throw error
    }
  }
}

// TTS服务
export class TTSService {
  // 原有的同步生成语音方法（保持向后兼容）
  static async generateSpeech(data: {
    input: string
    voice: string
    stability?: number
    similarity_boost?: number
    style?: number
    speed?: number
  }): Promise<ArrayBuffer> {
    try {
      return await AuthService.withTokenRefresh(async () => {
        const response = await fetch(`${(apiClient as any).baseURL}${API_ENDPOINTS.TTS.GENERATE}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${TokenManager.getAccessToken()}`,
          },
          body: JSON.stringify(data),
        })

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}))
          const error = new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
          // 传递错误类型信息
          if (errorData.type) {
            (error as any).type = errorData.type
          }
          throw error
        }

        return await response.arrayBuffer()
      })
    } catch (error) {
      console.error('Generate speech error:', error)
      throw error
    }
  }

  // 新的异步任务启动方法
  static async startAsyncGeneration(data: {
    input: string
    voice: string
    stability?: number
    similarity_boost?: number
    style?: number
    speed?: number
  }): Promise<{ taskId: string; status: string; message?: string }> {
    try {
      return await AuthService.withTokenRefresh(async () => {
        const response = await fetch(`${(apiClient as any).baseURL}${API_ENDPOINTS.TTS.GENERATE}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${TokenManager.getAccessToken()}`,
          },
          body: JSON.stringify(data),
        })

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}))
          const error = new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
          // 传递错误类型信息
          if (errorData.type) {
            (error as any).type = errorData.type
          }
          throw error
        }

        return await response.json()
      })
    } catch (error) {
      console.error('Start async generation error:', error)
      throw error
    }
  }

  // 检查任务状态
  static async checkTaskStatus(taskId: string): Promise<{
    taskId: string
    status: 'processing' | 'complete' | 'failed'
    createdAt?: number
    completedAt?: number
    audioUrl?: string
    audioSize?: number
    chunksProcessed?: number
    totalChunks?: number
    error?: string
  }> {
    try {
      return await AuthService.withTokenRefresh(async () => {
        const response = await fetch(`${(apiClient as any).baseURL}${API_ENDPOINTS.TTS.STATUS}/${taskId}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${TokenManager.getAccessToken()}`,
          },
        })

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}))
          throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
        }

        return await response.json()
      })
    } catch (error) {
      console.error('Check task status error:', error)
      throw error
    }
  }

  // 下载完成的音频 - Worker代理方式（备用）
  static async downloadAudio(taskId: string): Promise<ArrayBuffer> {
    try {
      return await AuthService.withTokenRefresh(async () => {
        const response = await fetch(`${(apiClient as any).baseURL}${API_ENDPOINTS.TTS.DOWNLOAD}/${taskId}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${TokenManager.getAccessToken()}`,
          },
        })

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}))
          throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
        }

        return await response.arrayBuffer()
      })
    } catch (error) {
      console.error('Download audio error:', error)
      throw error
    }
  }

  // R2直链下载方式（优化后）
  static async downloadFromDirectUrl(directUrl: string): Promise<ArrayBuffer> {
    try {
      // 检查是否为开发环境且存在CORS问题
      const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'

      let fetchOptions: RequestInit = {
        method: 'GET',
        // R2直链不需要认证头
      }

      // 开发环境CORS处理
      if (isDevelopment) {
        // 尝试直接访问，如果CORS失败则通过代理
        try {
          await fetch(directUrl, {
            method: 'HEAD',
            mode: 'cors'
          })
        } catch (corsError) {
          fetchOptions.mode = 'no-cors'

          // 如果no-cors也不行，则抛出错误让其回退到Worker代理
          if (corsError instanceof TypeError && corsError.message.includes('CORS')) {
            throw new Error(`CORS_ERROR: ${corsError.message}`)
          }
        }
      }

      const response = await fetch(directUrl, fetchOptions)

      if (!response.ok) {
        throw new Error(`R2 direct download failed: HTTP ${response.status}: ${response.statusText}`)
      }

      const arrayBuffer = await response.arrayBuffer()
      return arrayBuffer
    } catch (error) {
      // 如果是CORS错误，提供更详细的错误信息
      if (error instanceof Error && error.message.includes('CORS')) {
        throw new Error(`R2_CORS_ERROR: CORS configuration needed for r2-assets.aispeak.top. ${error.message}`)
      }

      throw error
    }
  }

  // 防重复下载缓存
  private static downloadCache = new Map<string, Promise<ArrayBuffer>>()

  // 智能轮询任务状态直到完成
  static async pollTaskUntilComplete(
    taskId: string,
    onProgress?: (status: any) => void,
    maxAttempts: number = 60,
    initialDelay: number = 2000
  ): Promise<ArrayBuffer> {
    // 检查是否已经在下载中
    if (this.downloadCache.has(taskId)) {
      return await this.downloadCache.get(taskId)!
    }

    // 创建下载Promise并缓存
    const downloadPromise = this.performPolling(taskId, onProgress, maxAttempts, initialDelay)
    this.downloadCache.set(taskId, downloadPromise)

    try {
      const result = await downloadPromise
      // 下载完成后清理缓存
      this.downloadCache.delete(taskId)
      return result
    } catch (error) {
      // 下载失败也要清理缓存
      this.downloadCache.delete(taskId)
      throw error
    }
  }

  // 实际的轮询逻辑
  private static async performPolling(
    taskId: string,
    onProgress?: (status: any) => void,
    maxAttempts: number = 60,
    initialDelay: number = 2000
  ): Promise<ArrayBuffer> {
    let attempts = 0
    let delay = initialDelay

    while (attempts < maxAttempts) {
      try {
        const status = await this.checkTaskStatus(taskId)

        // 调用进度回调
        if (onProgress) {
          onProgress(status)
        }

        if (status.status === 'complete') {
          // 任务完成，智能选择下载方式
          // 优先使用R2直链下载
          if (status.audioUrl && status.audioUrl.includes('r2-assets.aispeak.top')) {
            try {
              return await this.downloadFromDirectUrl(status.audioUrl)
            } catch (r2Error) {
              // R2直链失败，回退到Worker代理下载
              return await this.downloadAudio(taskId)
            }
          } else {
            // 没有R2直链或不是R2直链，使用Worker代理下载
            return await this.downloadAudio(taskId)
          }
        } else if (status.status === 'failed') {
          // 任务失败
          throw new Error(status.error || 'Task failed')
        }

        // 任务仍在处理中，等待后重试
        await new Promise(resolve => setTimeout(resolve, delay))

        // 指数退避：延迟时间逐渐增加，但不超过10秒
        delay = Math.min(delay * 1.2, 10000)
        attempts++

      } catch (error) {
        console.error(`Polling attempt ${attempts + 1} failed:`, error)
        attempts++

        // 如果是网络错误，稍等后重试
        if (attempts < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, delay))
          delay = Math.min(delay * 1.5, 10000)
        } else {
          throw error
        }
      }
    }

    throw new Error('Task polling timeout - maximum attempts reached')
  }
}

// 自动标注服务
export class AutoTagService {
  // 处理自动标注请求
  static async processText(text: string, language: string = 'auto'): Promise<{
    success: boolean;
    processedText: string;
    originalLength: number;
    processedLength: number;
    rateLimit?: {
      remaining: number;
      resetTime: number;
    };
  }> {
    try {
      return await AuthService.withTokenRefresh(async () => {
        return await apiClient.post(
          API_ENDPOINTS.AUTO_TAG.PROCESS,
          { text, language },
          true // 需要认证
        )
      })
    } catch (error) {
      console.error('Auto tag process error:', error)
      throw error
    }
  }

  // 获取使用状态
  static async getStatus(): Promise<{
    rateLimit: {
      maxRequests: number;
      remaining: number;
      resetTime: number;
      windowMinutes: number;
    };
    usage: any;
  }> {
    try {
      return await AuthService.withTokenRefresh(async () => {
        return await apiClient.get(
          API_ENDPOINTS.AUTO_TAG.STATUS,
          true // 需要认证
        )
      })
    } catch (error) {
      console.error('Auto tag status error:', error)
      throw error
    }
  }
}

// 导出便捷方法
export const auth = AuthService
export const cardService = CardService
export const ttsService = TTSService
export const autoTagService = AutoTagService
