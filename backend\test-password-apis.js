const request = require('supertest');
const app = require('./src/app');

// 配置
const TEST_EMAIL = '<EMAIL>';
const TEST_USERNAME = 'testuser';
const CURRENT_PASSWORD = 'testpass';
const NEW_PASSWORD = 'newpassword123';

// 测试用户登录获取token
async function login() {
  try {
    console.log('🔐 测试登录...');
    const response = await request(app)
      .post('/api/auth/login')
      .send({
        username: TEST_USERNAME,
        password: CURRENT_PASSWORD
      });

    if (response.status === 200) {
      console.log('✅ 登录成功');
      return response.body.access_token;
    } else {
      console.error('❌ 登录失败:', response.body);
      return null;
    }
  } catch (error) {
    console.error('❌ 登录失败:', error.message);
    return null;
  }
}

// 测试修改密码
async function testChangePassword(token) {
  try {
    console.log('\n🔄 测试修改密码...');
    const response = await request(app)
      .post('/api/auth/change-password')
      .set('Authorization', `Bearer ${token}`)
      .send({
        currentPassword: CURRENT_PASSWORD,
        newPassword: NEW_PASSWORD
      });

    if (response.status === 200) {
      console.log('✅ 修改密码成功:', response.body.message);
      return true;
    } else {
      console.error('❌ 修改密码失败:', response.body);
      return false;
    }
  } catch (error) {
    console.error('❌ 修改密码失败:', error.message);
    return false;
  }
}

// 测试忘记密码 - 发送验证码
async function testForgotPassword() {
  try {
    console.log('\n📧 测试发送重置密码验证码...');
    const response = await request(app)
      .post('/api/auth/forgot-password')
      .send({
        email: TEST_EMAIL
      });

    if (response.status === 200) {
      console.log('✅ 发送验证码成功:', response.body.message);
      return true;
    } else {
      console.error('❌ 发送验证码失败:', response.body);
      return false;
    }
  } catch (error) {
    console.error('❌ 发送验证码失败:', error.message);
    return false;
  }
}

// 测试重置密码（需要手动输入验证码）
async function testResetPassword(verificationCode) {
  try {
    console.log('\n🔑 测试重置密码...');
    const response = await request(app)
      .post('/api/auth/reset-password')
      .send({
        email: TEST_EMAIL,
        code: verificationCode,
        newPassword: 'resetpassword123'
      });

    if (response.status === 200) {
      console.log('✅ 重置密码成功:', response.body.message);
      return true;
    } else {
      console.error('❌ 重置密码失败:', response.body);
      return false;
    }
  } catch (error) {
    console.error('❌ 重置密码失败:', error.message);
    return false;
  }
}

// 测试参数验证
async function testValidation() {
  console.log('\n🧪 测试参数验证...');

  // 测试修改密码 - 缺少token
  const response1 = await request(app)
    .post('/api/auth/change-password')
    .send({
      currentPassword: 'test',
      newPassword: 'test123'
    });

  if (response1.status === 401) {
    console.log('✅ Token验证正常');
  }

  // 测试忘记密码 - 无效邮箱
  const response2 = await request(app)
    .post('/api/auth/forgot-password')
    .send({
      email: 'invalid-email'
    });

  if (response2.status === 400) {
    console.log('✅ 邮箱格式验证正常');
  }

  // 测试重置密码 - 无效验证码格式
  const response3 = await request(app)
    .post('/api/auth/reset-password')
    .send({
      email: TEST_EMAIL,
      code: '123', // 无效格式
      newPassword: 'test123'
    });

  if (response3.status === 400) {
    console.log('✅ 验证码格式验证正常');
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始测试密码相关API...\n');
  
  // 测试参数验证
  await testValidation();
  
  // 测试忘记密码流程
  await testForgotPassword();
  
  // 如果需要测试完整流程，需要先创建测试用户
  console.log('\n📝 注意：');
  console.log('1. 要测试修改密码，需要先创建测试用户并登录');
  console.log('2. 要测试重置密码，需要查看邮件获取验证码');
  console.log('3. 可以手动调用 testResetPassword("验证码") 来测试重置密码');
  
  console.log('\n✨ 测试完成！');
}

// 导出测试函数供手动调用
module.exports = {
  login,
  testChangePassword,
  testForgotPassword,
  testResetPassword,
  testValidation,
  runTests
};

// 如果直接运行此文件
if (require.main === module) {
  runTests().catch(console.error);
}
