require('dotenv').config();
const { isContentViolationError } = require('./src/utils/helpers');

/**
 * 测试违规检测功能
 */
async function testViolationDetection() {
  console.log('🚀 开始测试违规检测功能...\n');

  // 1. 测试基本违规检测函数
  console.log('📋 1. 测试基本违规检测函数');
  
  // 测试用例1：标准违规消息
  const testCase1 = {
    status: 403,
    errorData: {
      detail: {
        message: "We are sorry but text you are trying to use may violate our Terms of Service and has been blocked."
      }
    },
    errorMessage: "We are sorry but text you are trying to use may violate our Terms of Service and has been blocked."
  };
  
  const result1 = isContentViolationError(testCase1.status, testCase1.errorData, testCase1.errorMessage);
  console.log(`✅ 标准违规消息检测: ${result1 ? '通过' : '失败'}`);
  
  // 测试用例2：detail.status字段检测
  const testCase2 = {
    status: 403,
    errorData: {
      detail: {
        status: 'content_against_policy'
      }
    },
    errorMessage: "Content policy violation"
  };
  
  const result2 = isContentViolationError(testCase2.status, testCase2.errorData, testCase2.errorMessage);
  console.log(`✅ detail.status字段检测: ${result2 ? '通过' : '失败'}`);
  
  // 测试用例3：关键词模糊匹配
  const testCase3 = {
    status: 403,
    errorData: {},
    errorMessage: "Your content violates our Terms of Service"
  };
  
  const result3 = isContentViolationError(testCase3.status, testCase3.errorData, testCase3.errorMessage);
  console.log(`✅ 关键词模糊匹配: ${result3 ? '通过' : '失败'}`);
  
  // 测试用例4：非违规错误（应该返回false）
  const testCase4 = {
    status: 429,
    errorData: {},
    errorMessage: "Too many requests"
  };
  
  const result4 = isContentViolationError(testCase4.status, testCase4.errorData, testCase4.errorMessage);
  console.log(`✅ 非违规错误检测: ${!result4 ? '通过' : '失败'}`);
  
  // 测试用例5：非403状态码（应该返回false）
  const testCase5 = {
    status: 500,
    errorData: {},
    errorMessage: "We are sorry but text you are trying to use may violate our Terms of Service and has been blocked."
  };
  
  const result5 = isContentViolationError(testCase5.status, testCase5.errorData, testCase5.errorMessage);
  console.log(`✅ 非403状态码检测: ${!result5 ? '通过' : '失败'}`);

  console.log('\n📋 2. 测试错误对象标志设置');
  
  // 模拟创建违规错误对象
  function createViolationError(originalMessage) {
    const violationError = new Error(originalMessage);
    violationError.status = 403;
    violationError.isContentViolation = true;
    violationError.isDataCenterRetryable = false;
    violationError.originalError = { detail: { message: originalMessage } };
    return violationError;
  }
  
  const violationError = createViolationError("We are sorry but text you are trying to use may violate our Terms of Service and has been blocked.");
  console.log(`✅ 违规错误对象创建: ${violationError.isContentViolation ? '通过' : '失败'}`);
  console.log(`✅ 不可重试标志: ${!violationError.isDataCenterRetryable ? '通过' : '失败'}`);
  console.log(`✅ 状态码设置: ${violationError.status === 403 ? '通过' : '失败'}`);

  console.log('\n📋 3. 测试快速失败逻辑模拟');
  
  // 模拟processChunks中的快速失败逻辑
  async function simulateChunkProcessing() {
    const chunks = ['chunk1', 'chunk2', 'chunk3'];
    const abortController = new AbortController();
    let firstViolationError = null;
    
    console.log('模拟并发处理3个chunks...');
    
    // 模拟第2个chunk发生违规
    for (let i = 0; i < chunks.length; i++) {
      if (abortController.signal.aborted) {
        console.log(`  Chunk ${i + 1}: 已被中止`);
        continue;
      }
      
      if (i === 1) { // 第2个chunk模拟违规
        const violationError = createViolationError("Content violation detected");
        firstViolationError = violationError;
        abortController.abort();
        console.log(`  Chunk ${i + 1}: 检测到违规，中止所有其他chunk`);
        throw violationError;
      } else {
        console.log(`  Chunk ${i + 1}: 处理成功`);
      }
    }
  }
  
  try {
    await simulateChunkProcessing();
  } catch (error) {
    if (error.isContentViolation) {
      console.log(`✅ 快速失败模拟: 成功检测并中止处理`);
    } else {
      console.log(`❌ 快速失败模拟: 失败`);
    }
  }

  console.log('\n🎉 违规检测功能测试完成！');
  console.log('\n📋 测试总结:');
  console.log('   ✅ 基本违规检测函数正常工作');
  console.log('   ✅ 错误对象标志设置正确');
  console.log('   ✅ 快速失败逻辑模拟成功');
  console.log('\n💡 建议:');
  console.log('   1. 在实际环境中测试真实的违规内容');
  console.log('   2. 验证前端能正确接收和显示违规错误消息');
  console.log('   3. 确认WebSocket连接在违规时能正确关闭');
  
  return true;
}

// 运行测试
if (require.main === module) {
  testViolationDetection().catch(console.error);
}

module.exports = { testViolationDetection };
