#!/usr/bin/env node

/**
 * 工作池详细信息测试脚本
 * 直接使用WorkerPoolController测试具体的worker-selector和节点信息
 * 显示每个请求使用的具体Worker、端口、节点和IP地址
 */

require('dotenv').config();
const { SocksProxyAgent } = require('socks-proxy-agent');

// 颜色输出函数
function log(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    magenta: '\x1b[35m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color] || ''}${message}${colors.reset}`);
}

/**
 * IP查询服务列表
 */
const IP_SERVICES = [
  {
    name: 'ipify',
    url: 'https://api.ipify.org?format=json',
    parseResponse: (data) => ({ ip: data.ip })
  },
  {
    name: 'ip-api',
    url: 'http://ip-api.com/json',
    parseResponse: (data) => ({
      ip: data.query,
      country: data.country,
      city: data.city,
      isp: data.isp,
      timezone: data.timezone
    })
  },
  {
    name: 'ip2location',
    url: 'https://api.ip2location.io/',
    parseResponse: (data) => ({
      ip: data.ip,
      country: data.country_name,
      region: data.region_name,
      city: data.city_name,
      isp: data.as
    })
  },
  {
    name: 'myip',
    url: 'https://api.myip.com',
    parseResponse: (data) => ({
      ip: data.ip,
      country: data.country,
      cc: data.cc
    })
  }
];

/**
 * 构建fetch选项（参考后端NetworkAdapter实现）
 */
function buildFetchOptions(options, proxyConfig = null) {
  const fetchOptions = {
    method: options.method || 'GET',
    headers: options.headers || {},
    signal: options.signal
  };

  // 添加请求体
  if (options.body) {
    fetchOptions.body = typeof options.body === 'string' ? options.body : JSON.stringify(options.body);
  }

  // 添加超时控制
  if (options.timeout && !options.signal) {
    fetchOptions.signal = AbortSignal.timeout(options.timeout);
  }

  // 添加代理配置（Node.js环境）
  if (proxyConfig && typeof window === 'undefined') {
    if (proxyConfig.type === 'dispatcher') {
      // 使用undici dispatcher（支持Node.js fetch）
      fetchOptions.dispatcher = proxyConfig.dispatcher;
    } else if (proxyConfig.type === 'agent') {
      // 使用传统agent（降级支持）
      fetchOptions.agent = proxyConfig.agent;
    }
  }

  return fetchOptions;
}

/**
 * 创建SOCKS代理agent/dispatcher（参考后端NetworkAdapter实现）
 */
function createSocksAgent(port, host = '127.0.0.1') {
  try {
    // 首先尝试使用fetch-socks（支持Node.js fetch的undici dispatcher）
    try {
      const { socksDispatcher } = require('fetch-socks');
      return {
        type: 'dispatcher',
        dispatcher: socksDispatcher({
          type: 5,
          host: host,
          port: port
        })
      };
    } catch (fetchSocksError) {
      console.log('   fetch-socks不可用，降级到socks-proxy-agent');
    }

    // 降级到传统的socks-proxy-agent
    const proxyUrl = `socks5h://${host}:${port}`;
    return {
      type: 'agent',
      agent: new SocksProxyAgent(proxyUrl)
    };
  } catch (error) {
    console.warn('SOCKS代理创建失败:', error.message);
    return null;
  }
}

/**
 * 使用具体Worker获取IP地址详情
 */
async function getIpWithWorkerDetails(serviceIndex = 0) {
  const service = IP_SERVICES[serviceIndex];
  let controller = null;
  let acquiredInfo = null;

  try {
    // 1. 使用ProxyGateway的单例WorkerPoolController
    const { ProxyGateway } = require('./src/gateway/core/ProxyGateway');
    const { configAdapter } = require('./src/gateway/adapters/ConfigAdapter');

    const config = configAdapter.getConfig();

    // 创建ProxyGateway实例（会创建或复用全局WorkerPoolController）
    const gateway = new ProxyGateway(config);
    await gateway.initialize();

    // 获取共享的WorkerPoolController实例
    controller = gateway.singboxController;

    log('cyan', `   正在通过具体Worker请求 ${service.name} (${service.url})`);

    // 3. 获取具体的Worker
    acquiredInfo = await controller.acquireWorker();
    const { worker, nodeTag } = acquiredInfo;

    // 4. 创建SOCKS代理（修复：支持dispatcher和agent）
    const proxyConfig = createSocksAgent(worker.port);
    const socksProxy = `socks5h://127.0.0.1:${worker.port}`;

    if (!proxyConfig) {
      throw new Error('无法创建SOCKS代理配置');
    }

    log('yellow', `   使用Worker详情:`);
    console.log(`     Worker ID: ${worker.id}`);
    console.log(`     Worker选择器: ${worker.selector}`);
    console.log(`     Worker端口: ${worker.port}`);
    console.log(`     分配节点: ${nodeTag}`);
    console.log(`     SOCKS代理: ${socksProxy}`);
    console.log(`     代理类型: ${proxyConfig.type}`);

    // 5. 发起网络请求（修复：使用正确的fetch配置）
    const startTime = Date.now();

    const fetchOptions = buildFetchOptions({
      method: 'GET',
      headers: {
        'User-Agent': 'WorkerDetails-Test/1.0',
        'Accept': 'application/json'
      },
      timeout: 15000
    }, proxyConfig);

    const response = await fetch(service.url, fetchOptions);

    const duration = Date.now() - startTime;

    // 6. 检查响应状态（增强错误处理）
    if (!response.ok) {
      // 记录详细的错误信息
      let errorDetails = `HTTP ${response.status}: ${response.statusText}`;
      try {
        const errorText = await response.text();
        if (errorText) {
          errorDetails += ` - ${errorText}`;
        }
      } catch (e) {
        // 忽略读取错误文本的失败
      }
      throw new Error(errorDetails);
    }

    // 7. 解析响应数据
    const data = await response.json();
    const ipInfo = service.parseResponse(data);

    // 验证IP信息
    if (!ipInfo || !ipInfo.ip) {
      throw new Error('无法从响应中解析IP地址');
    }

    // 8. 获取Worker统计信息
    const stats = await controller.getStats();
    const workerDetails = stats.workerDetails ? stats.workerDetails.find(w => w.id === worker.id) : null;

    // 9. 更新Worker成功统计
    if (acquiredInfo && acquiredInfo.worker) {
      acquiredInfo.worker.successCount = (acquiredInfo.worker.successCount || 0) + 1;
    }

    return {
      success: true,
      service: service.name,
      url: service.url,
      ipInfo,
      duration,
      workerInfo: {
        workerId: worker.id,
        workerSelector: worker.selector,
        workerPort: worker.port,
        assignedNode: nodeTag,
        socksProxy: socksProxy,
        requestCount: workerDetails?.requestCount || worker.requestCount || 0,
        successCount: workerDetails?.successCount || worker.successCount || 0,
        failureCount: workerDetails?.failureCount || worker.failureCount || 0
      },
      poolStats: {
        totalWorkers: stats.totalWorkers,
        busyWorkers: stats.busyWorkers,
        idleWorkers: stats.idleWorkers,
        totalNodes: stats.totalNodes,
        healthyNodes: stats.healthyNodes
      }
    };

  } catch (error) {
    // 增强错误处理
    let errorMessage = error.message;
    let errorType = 'unknown';

    // 分类错误类型
    if (error.name === 'AbortError') {
      errorType = 'timeout';
      errorMessage = `请求超时: ${errorMessage}`;
    } else if (error.message.includes('ECONNREFUSED')) {
      errorType = 'connection_refused';
      errorMessage = `连接被拒绝，请检查sing-box是否运行: ${errorMessage}`;
    } else if (error.message.includes('ENOTFOUND')) {
      errorType = 'dns_error';
      errorMessage = `DNS解析失败: ${errorMessage}`;
    } else if (error.message.includes('HTTP')) {
      errorType = 'http_error';
    } else if (error.message.includes('Worker')) {
      errorType = 'worker_error';
    }

    // 更新Worker失败统计
    if (acquiredInfo && acquiredInfo.worker) {
      acquiredInfo.worker.failureCount = (acquiredInfo.worker.failureCount || 0) + 1;
    }

    return {
      success: false,
      service: service.name,
      url: service.url,
      error: errorMessage,
      errorType: errorType,
      duration: Date.now() - (Date.now() - 15000), // 估算持续时间
      workerInfo: acquiredInfo ? {
        workerId: acquiredInfo.worker.id,
        workerSelector: acquiredInfo.worker.selector,
        workerPort: acquiredInfo.worker.port,
        assignedNode: acquiredInfo.nodeTag
      } : null
    };

  } finally {
    // 10. 释放Worker（确保资源清理）
    if (acquiredInfo && controller) {
      try {
        controller.releaseWorker(acquiredInfo.worker);
        log('cyan', `   Worker ${acquiredInfo.worker.id} 已释放`);
      } catch (releaseError) {
        console.warn(`   警告: Worker释放失败: ${releaseError.message}`);
      }
    }

    // 11. 清理控制器
    if (controller) {
      try {
        await controller.cleanup();
      } catch (cleanupError) {
        console.warn(`   警告: 控制器清理失败: ${cleanupError.message}`);
      }
    }
  }
}

/**
 * 测试单个Worker的IP获取
 */
async function testSingleWorkerIp() {
  log('blue', '\n🔍 测试单个Worker IP获取');
  log('blue', '================================');
  
  const result = await getIpWithWorkerDetails(0); // 使用ipify服务
  
  if (result.success) {
    log('green', '✅ Worker IP测试成功');
    
    console.log('\n📋 Worker详细信息:');
    console.log(`   Worker ID: ${result.workerInfo.workerId}`);
    console.log(`   Worker选择器: ${result.workerInfo.workerSelector}`);
    console.log(`   Worker端口: ${result.workerInfo.workerPort}`);
    console.log(`   分配节点: ${result.workerInfo.assignedNode}`);
    console.log(`   SOCKS代理: ${result.workerInfo.socksProxy}`);
    
    console.log('\n🌍 IP地址信息:');
    console.log(`   服务: ${result.service}`);
    console.log(`   IP地址: ${result.ipInfo.ip}`);
    console.log(`   响应时间: ${result.duration}ms`);
    
    console.log('\n📊 工作池状态:');
    console.log(`   总工人数: ${result.poolStats.totalWorkers}`);
    console.log(`   忙碌工人: ${result.poolStats.busyWorkers}`);
    console.log(`   空闲工人: ${result.poolStats.idleWorkers}`);
    console.log(`   健康节点: ${result.poolStats.healthyNodes}/${result.poolStats.totalNodes}`);
    
  } else {
    log('red', '❌ Worker IP测试失败');
    console.log(`   错误: ${result.error}`);
    if (result.workerInfo) {
      console.log(`   使用的Worker: ${result.workerInfo.workerSelector} (端口${result.workerInfo.workerPort})`);
    }
  }
  
  return result;
}

/**
 * 测试多个Worker的并发IP获取
 */
async function testMultipleWorkersIp(workerCount = 3) {
  log('blue', `\n🚀 测试${workerCount}个Worker并发IP获取`);
  log('blue', '================================');
  
  const promises = Array.from({ length: workerCount }, async (_, index) => {
    const serviceIndex = index % IP_SERVICES.length;
    const result = await getIpWithWorkerDetails(serviceIndex);
    
    return {
      testId: index + 1,
      ...result
    };
  });
  
  const results = await Promise.all(promises);
  
  // 分析结果
  const successResults = results.filter(r => r.success);
  const failedResults = results.filter(r => !r.success);
  
  log('green', `✅ 并发测试完成: ${successResults.length}/${results.length} 成功`);
  
  if (successResults.length > 0) {
    console.log('\n📊 成功的Worker请求:');
    successResults.forEach(result => {
      const ipDisplay = typeof result.ipInfo === 'object' && result.ipInfo.country ? 
        `${result.ipInfo.ip} (${result.ipInfo.country})` : 
        result.ipInfo.ip;
        
      console.log(`   测试${result.testId}: ${result.workerInfo.workerSelector} → ${result.workerInfo.assignedNode}`);
      console.log(`     端口: ${result.workerInfo.workerPort} | IP: ${ipDisplay} | 耗时: ${result.duration}ms`);
    });
    
    // 分析Worker和IP分布
    const uniqueWorkers = new Set(successResults.map(r => r.workerInfo.workerSelector));
    const uniqueNodes = new Set(successResults.map(r => r.workerInfo.assignedNode));
    const uniqueIPs = new Set(successResults.map(r => r.ipInfo.ip));

    console.log('\n🔍 分布分析:');
    console.log(`   使用的Worker数: ${uniqueWorkers.size}`);
    console.log(`   使用的节点数: ${uniqueNodes.size}`);
    console.log(`   获取的唯一IP数: ${uniqueIPs.size}`);
    console.log(`   Worker列表: ${Array.from(uniqueWorkers).join(', ')}`);
    console.log(`   IP列表: ${Array.from(uniqueIPs).join(', ')}`);

    if (uniqueIPs.size > 1) {
      log('green', '🎉 检测到多个不同IP，Worker池正在使用不同节点！');
    } else if (uniqueIPs.size === 1) {
      log('yellow', '⚠️  所有Worker返回相同IP，可能使用了相同出口或地理位置相近的节点');
    }

    // 返回分析结果
    const analysisResult = {
      total: results.length,
      success: successResults.length,
      failed: failedResults.length,
      uniqueWorkers: uniqueWorkers.size,
      uniqueIPs: uniqueIPs.size,
      results: successResults
    };

    if (failedResults.length > 0) {
      console.log('\n❌ 失败的Worker请求:');
      failedResults.forEach(result => {
        console.log(`   测试${result.testId}: ${result.error}`);
        if (result.workerInfo) {
          console.log(`     Worker: ${result.workerInfo.workerSelector} (端口${result.workerInfo.workerPort})`);
        }
      });
    }

    return analysisResult;
  }

  // 如果没有成功结果，返回基本信息
  return {
    total: results.length,
    success: successResults.length,
    failed: failedResults.length,
    uniqueWorkers: 0,
    uniqueIPs: 0,
    results: successResults
  };
}

/**
 * 测试顺序Worker切换
 */
async function testSequentialWorkerSwitching() {
  log('blue', '\n🔄 测试顺序Worker切换');
  log('blue', '================================');

  const results = [];

  for (let i = 0; i < IP_SERVICES.length; i++) {
    const service = IP_SERVICES[i];
    log('cyan', `\n   测试服务 ${i + 1}/${IP_SERVICES.length}: ${service.name}`);

    const result = await getIpWithWorkerDetails(i);
    results.push(result);

    if (result.success) {
      const ipDisplay = typeof result.ipInfo === 'object' && result.ipInfo.country ?
        `${result.ipInfo.ip} (${result.ipInfo.country})` :
        result.ipInfo.ip;

      log('green', `   ✅ ${service.name}: ${result.workerInfo.workerSelector} → ${ipDisplay}`);
      console.log(`      节点: ${result.workerInfo.assignedNode} | 端口: ${result.workerInfo.workerPort} | 耗时: ${result.duration}ms`);
    } else {
      log('red', `   ❌ ${service.name}: ${result.error}`);
    }

    // 间隔1秒，让系统有时间处理
    if (i < IP_SERVICES.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  return results;
}

/**
 * 测试Worker池状态监控
 */
async function testWorkerPoolMonitoring() {
  log('blue', '\n📊 测试Worker池状态监控');
  log('blue', '================================');

  let controller = null;

  try {
    // 使用ProxyGateway的单例WorkerPoolController
    const { ProxyGateway } = require('./src/gateway/core/ProxyGateway');
    const { configAdapter } = require('./src/gateway/adapters/ConfigAdapter');

    const config = configAdapter.getConfig();

    // 创建ProxyGateway实例（会复用全局WorkerPoolController）
    const gateway = new ProxyGateway(config);
    await gateway.initialize();

    // 获取共享的WorkerPoolController实例
    controller = gateway.singboxController;

    // 获取初始状态
    const initialStats = await controller.getStats();

    console.log('📋 Worker池初始状态:');
    console.log(`   总工人数: ${initialStats.totalWorkers}`);
    console.log(`   健康节点数: ${initialStats.healthyNodes}/${initialStats.totalNodes}`);
    console.log(`   忙碌工人: ${initialStats.busyWorkers}`);
    console.log(`   空闲工人: ${initialStats.idleWorkers}`);

    console.log('\n👥 Worker详细信息:');
    initialStats.workerDetails.forEach(worker => {
      const status = worker.isBusy ? '忙碌' : '空闲';
      const node = worker.currentNode || '未分配';
      console.log(`   ${worker.selector}: 端口${worker.port} | ${status} | 节点: ${node}`);
      console.log(`     请求数: ${worker.requestCount} | 成功: ${worker.successCount} | 失败: ${worker.failureCount}`);
    });

    // 测试获取多个Worker
    log('cyan', '\n   正在获取3个Worker进行测试...');
    const workers = [];

    for (let i = 0; i < 3; i++) {
      try {
        const acquiredInfo = await controller.acquireWorker();
        workers.push(acquiredInfo);
        console.log(`   获取Worker ${i + 1}: ${acquiredInfo.worker.selector} → ${acquiredInfo.nodeTag}`);
      } catch (error) {
        console.log(`   获取Worker ${i + 1}失败: ${error.message}`);
        break;
      }
    }

    // 获取忙碌状态
    const busyStats = await controller.getStats();
    console.log(`\n📈 获取Worker后状态:`);
    console.log(`   忙碌工人: ${busyStats.busyWorkers}`);
    console.log(`   空闲工人: ${busyStats.idleWorkers}`);

    // 释放所有Worker
    log('cyan', '\n   正在释放所有Worker...');
    workers.forEach(({ worker }) => {
      controller.releaseWorker(worker);
      console.log(`   释放Worker: ${worker.selector}`);
    });

    // 获取最终状态
    const finalStats = await controller.getStats();
    console.log(`\n📉 释放Worker后状态:`);
    console.log(`   忙碌工人: ${finalStats.busyWorkers}`);
    console.log(`   空闲工人: ${finalStats.idleWorkers}`);

    log('green', '✅ Worker池监控测试完成');

    return {
      success: true,
      initialStats,
      busyStats,
      finalStats,
      workersAcquired: workers.length
    };

  } catch (error) {
    log('red', `❌ Worker池监控测试失败: ${error.message}`);
    return {
      success: false,
      error: error.message
    };

  } finally {
    if (controller) {
      await controller.cleanup();
    }
  }
}

/**
 * 检查sing-box API连通性
 */
async function checkSingboxConnectivity(config) {
  try {
    const response = await fetch(`${config.SINGBOX_API_ENDPOINT}/proxies`, {
      method: 'GET',
      signal: AbortSignal.timeout(5000)
    });

    if (response.ok) {
      const data = await response.json();
      return {
        connected: true,
        proxiesCount: data.proxies ? Object.keys(data.proxies).length : 0
      };
    } else {
      return {
        connected: false,
        error: `HTTP ${response.status}: ${response.statusText}`
      };
    }
  } catch (error) {
    return {
      connected: false,
      error: error.message
    };
  }
}

/**
 * 验证测试环境配置
 */
function validateTestEnvironment(config) {
  const issues = [];
  const warnings = [];

  // 检查必需的配置
  if (config.NETWORK_MODE !== 'gateway') {
    issues.push(`网络模式应为 'gateway'，当前为 '${config.NETWORK_MODE}'`);
  }

  if (!config.ENABLE_SINGBOX_GATEWAY) {
    issues.push('ENABLE_SINGBOX_GATEWAY 应为 true');
  }

  if (!config.SINGBOX_WORKER_POOL_SIZE || config.SINGBOX_WORKER_POOL_SIZE < 1) {
    issues.push(`工作池大小无效: ${config.SINGBOX_WORKER_POOL_SIZE}`);
  }

  if (!config.SINGBOX_WORKER_PORT_START || config.SINGBOX_WORKER_PORT_START < 1024) {
    issues.push(`工作池起始端口无效: ${config.SINGBOX_WORKER_PORT_START}`);
  }

  if (!config.SINGBOX_API_ENDPOINT) {
    issues.push('缺少 SINGBOX_API_ENDPOINT 配置');
  }

  // 检查可选配置
  if (config.SINGBOX_WORKER_POOL_SIZE > 10) {
    warnings.push(`工作池大小较大 (${config.SINGBOX_WORKER_POOL_SIZE})，可能影响性能`);
  }

  return { issues, warnings };
}

/**
 * 主测试函数
 */
async function runWorkerDetailsTests() {
  log('cyan', '🧪 Worker详细信息测试开始');
  log('cyan', '================================');

  // 检查配置
  const { configAdapter } = require('./src/gateway/adapters/ConfigAdapter');
  const config = configAdapter.getConfig();

  console.log('📋 测试配置:');
  console.log(`   网络模式: ${config.NETWORK_MODE}`);
  console.log(`   网关启用: ${config.ENABLE_SINGBOX_GATEWAY}`);
  console.log(`   工作池大小: ${config.SINGBOX_WORKER_POOL_SIZE}`);
  console.log(`   端口范围: ${config.SINGBOX_WORKER_PORT_START}-${config.SINGBOX_WORKER_PORT_START + config.SINGBOX_WORKER_POOL_SIZE - 1}`);
  console.log(`   API端点: ${config.SINGBOX_API_ENDPOINT}`);
  console.log(`   代理主机: ${config.SINGBOX_PROXY_HOST || '127.0.0.1'}`);

  // 验证环境配置
  const validation = validateTestEnvironment(config);

  if (validation.issues.length > 0) {
    log('red', '❌ 配置错误:');
    validation.issues.forEach(issue => console.log(`   - ${issue}`));
    log('yellow', '请修复配置后重新运行测试');
    return;
  }

  if (validation.warnings.length > 0) {
    log('yellow', '⚠️  配置警告:');
    validation.warnings.forEach(warning => console.log(`   - ${warning}`));
  }

  if (config.NETWORK_MODE !== 'gateway' || !config.ENABLE_SINGBOX_GATEWAY) {
    log('yellow', '⚠️  警告: 当前配置未启用工作池模式');
  }

  // 检查sing-box连通性
  log('cyan', '\n🔗 检查sing-box连通性...');
  const connectivity = await checkSingboxConnectivity(config);

  if (connectivity.connected) {
    log('green', '✅ sing-box API连接正常');
    console.log(`   可用代理数: ${connectivity.proxiesCount}`);
  } else {
    log('red', '❌ sing-box API连接失败');
    console.log(`   错误: ${connectivity.error}`);
    log('yellow', '请确保sing-box正在运行并检查API端点配置');

    // 如果连接失败，询问是否继续
    console.log('\n是否继续测试？某些测试可能会失败...');
  }

  const testResults = {
    singleWorker: null,
    multipleWorkers: null,
    sequential: null,
    monitoring: null
  };

  try {
    // 1. 单个Worker测试
    testResults.singleWorker = await testSingleWorkerIp();

    // 2. 多Worker并发测试
    testResults.multipleWorkers = await testMultipleWorkersIp(3);

    // 3. 顺序切换测试
    testResults.sequential = await testSequentialWorkerSwitching();

    // 4. Worker池监控测试
    testResults.monitoring = await testWorkerPoolMonitoring();

    // 输出总结
    log('cyan', '\n📊 测试结果总结');
    log('cyan', '================================');

    console.log(`单Worker测试: ${testResults.singleWorker?.success ? '✅ 成功' : '❌ 失败'}`);
    console.log(`多Worker测试: ${testResults.multipleWorkers ? `✅ ${testResults.multipleWorkers.success}/${testResults.multipleWorkers.total} 成功` : '❌ 失败'}`);
    console.log(`顺序切换测试: ${testResults.sequential ? `✅ ${testResults.sequential.filter(r => r.success).length}/${testResults.sequential.length} 成功` : '❌ 失败'}`);
    console.log(`Worker池监控: ${testResults.monitoring?.success ? '✅ 成功' : '❌ 失败'}`);

    if (testResults.multipleWorkers) {
      console.log(`\n🎯 关键指标:`);
      console.log(`   使用的Worker数: ${testResults.multipleWorkers.uniqueWorkers}`);
      console.log(`   获取的唯一IP数: ${testResults.multipleWorkers.uniqueIPs}`);
      console.log(`   成功率: ${((testResults.multipleWorkers.success / testResults.multipleWorkers.total) * 100).toFixed(1)}%`);

      if (testResults.multipleWorkers.uniqueIPs > 1) {
        log('green', '🎉 Worker池正在使用不同节点，IP地址验证成功！');
        console.log('   ✓ 负载均衡工作正常');
        console.log('   ✓ 节点切换机制有效');
      } else if (testResults.multipleWorkers.uniqueIPs === 1 && testResults.multipleWorkers.success > 0) {
        log('yellow', '⚠️  所有Worker返回相同IP地址');
        console.log('   可能原因:');
        console.log('   - 节点位于相同地理位置');
        console.log('   - 使用了相同的出口服务器');
        console.log('   - sing-box配置中只有一个有效节点');
      } else {
        log('red', '❌ 无法获取有效的IP地址');
        console.log('   请检查:');
        console.log('   - sing-box是否正在运行');
        console.log('   - 节点配置是否正确');
        console.log('   - 网络连接是否正常');
      }

      // 分析错误类型
      if (testResults.multipleWorkers.failed > 0) {
        console.log(`\n🔍 错误分析:`);
        const failedResults = testResults.multipleWorkers.results.filter(r => !r.success);
        const errorTypes = {};

        failedResults.forEach(result => {
          const errorType = result.errorType || 'unknown';
          errorTypes[errorType] = (errorTypes[errorType] || 0) + 1;
        });

        Object.entries(errorTypes).forEach(([type, count]) => {
          console.log(`   ${type}: ${count} 次`);
        });
      }
    }

    const totalTests = Object.values(testResults).filter(r => r !== null).length;
    const successfulTests = Object.values(testResults).filter(r => r && (r.success || (Array.isArray(r) && r.some(item => item.success)))).length;

    console.log(`\n📈 测试总结:`);
    console.log(`   总测试数: ${totalTests}`);
    console.log(`   成功测试: ${successfulTests}`);
    console.log(`   失败测试: ${totalTests - successfulTests}`);
    console.log(`   成功率: ${((successfulTests / totalTests) * 100).toFixed(1)}%`);

    if (successfulTests === totalTests) {
      log('green', '🎉 所有Worker详细信息测试完成！');
      console.log('✅ 代理网关系统工作正常');
    } else {
      log('yellow', `⚠️  ${successfulTests}/${totalTests} 个测试成功，请检查失败的测试。`);

      if (successfulTests === 0) {
        log('red', '❌ 所有测试都失败了，请检查:');
        console.log('   1. sing-box是否正在运行');
        console.log('   2. 配置文件是否正确');
        console.log('   3. 网络连接是否正常');
        console.log('   4. 端口是否被占用');
      }
    }

  } catch (error) {
    log('red', `❌ 测试运行失败: ${error.message}`);
    console.error(error);
  }
}

// 运行测试
if (require.main === module) {
  runWorkerDetailsTests().catch(error => {
    log('red', '❌ 测试脚本执行失败:');
    console.error(error);
    process.exit(1);
  });
}

module.exports = {
  getIpWithWorkerDetails,
  testSingleWorkerIp,
  testMultipleWorkersIp,
  testSequentialWorkerSwitching,
  testWorkerPoolMonitoring,
  runWorkerDetailsTests
};
