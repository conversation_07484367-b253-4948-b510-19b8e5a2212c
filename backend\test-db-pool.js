#!/usr/bin/env node

/**
 * 数据库连接池测试脚本
 * 用于验证不同环境下的数据库连接池配置
 */

require('dotenv').config();
const { Pool } = require('pg');

// 颜色输出函数
function colorLog(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 获取连接池配置
function getPoolConfig() {
  const isProduction = process.env.NODE_ENV === 'production';
  
  const config = {
    connectionString: process.env.DATABASE_URL,
    max: parseInt(process.env.DB_POOL_MAX) || (isProduction ? 50 : 20),
    min: parseInt(process.env.DB_POOL_MIN) || (isProduction ? 5 : 2),
    idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT) || 30000,
    connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT) || 5000,
    acquireTimeoutMillis: parseInt(process.env.DB_ACQUIRE_TIMEOUT) || 60000,
  };

  // 生产环境启用SSL
  if (isProduction && process.env.DB_SSL_ENABLED === 'true') {
    config.ssl = { rejectUnauthorized: false };
  }

  return config;
}

// 测试数据库连接
async function testDatabaseConnection() {
  colorLog('blue', '🔍 开始数据库连接池测试...\n');

  const config = getPoolConfig();
  const pool = new Pool(config);

  try {
    // 显示配置信息
    colorLog('blue', '📋 连接池配置:');
    console.log(`   环境: ${process.env.NODE_ENV || 'development'}`);
    console.log(`   最大连接数: ${config.max}`);
    console.log(`   最小连接数: ${config.min}`);
    console.log(`   空闲超时: ${config.idleTimeoutMillis}ms`);
    console.log(`   连接超时: ${config.connectionTimeoutMillis}ms`);
    console.log(`   获取超时: ${config.acquireTimeoutMillis}ms`);
    console.log(`   SSL启用: ${config.ssl ? '是' : '否'}`);
    console.log('');

    // 测试基本连接
    colorLog('yellow', '🔌 测试基本连接...');
    const client = await pool.connect();
    const result = await client.query('SELECT NOW() as current_time, version() as pg_version');
    
    colorLog('green', '✅ 数据库连接成功');
    console.log(`   当前时间: ${result.rows[0].current_time}`);
    console.log(`   PostgreSQL版本: ${result.rows[0].pg_version.split(' ')[0]}`);
    
    client.release();

    // 测试连接池状态
    colorLog('yellow', '\n📊 连接池状态:');
    console.log(`   总连接数: ${pool.totalCount}`);
    console.log(`   空闲连接数: ${pool.idleCount}`);
    console.log(`   等待连接数: ${pool.waitingCount}`);

    // 测试并发连接
    colorLog('yellow', '\n🚀 测试并发连接...');
    const concurrentTests = [];
    const testCount = Math.min(5, config.max);
    
    for (let i = 0; i < testCount; i++) {
      concurrentTests.push(testConcurrentConnection(pool, i + 1));
    }

    await Promise.all(concurrentTests);
    colorLog('green', `✅ 并发连接测试完成 (${testCount}个连接)`);

    // 最终状态
    colorLog('yellow', '\n📊 测试后连接池状态:');
    console.log(`   总连接数: ${pool.totalCount}`);
    console.log(`   空闲连接数: ${pool.idleCount}`);
    console.log(`   等待连接数: ${pool.waitingCount}`);

  } catch (error) {
    colorLog('red', `❌ 数据库连接失败: ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      colorLog('yellow', '   💡 请确保PostgreSQL服务已启动');
    } else if (error.code === 'ENOTFOUND') {
      colorLog('yellow', '   💡 请检查数据库主机地址');
    } else if (error.message.includes('password')) {
      colorLog('yellow', '   💡 请检查数据库用户名和密码');
    }
  } finally {
    await pool.end();
    colorLog('blue', '\n🔚 测试完成');
  }
}

// 测试单个并发连接
async function testConcurrentConnection(pool, connectionId) {
  try {
    const client = await pool.connect();
    const result = await client.query('SELECT $1 as connection_id, pg_backend_pid() as process_id', [connectionId]);
    
    console.log(`   连接 ${connectionId}: PID ${result.rows[0].process_id}`);
    
    // 模拟一些工作
    await new Promise(resolve => setTimeout(resolve, 100));
    
    client.release();
    return true;
  } catch (error) {
    colorLog('red', `   连接 ${connectionId} 失败: ${error.message}`);
    return false;
  }
}

// 显示环境变量信息
function showEnvironmentInfo() {
  colorLog('blue', '🌍 环境变量信息:');
  console.log(`   NODE_ENV: ${process.env.NODE_ENV || '未设置'}`);
  console.log(`   DB_POOL_MAX: ${process.env.DB_POOL_MAX || '未设置'}`);
  console.log(`   DB_POOL_MIN: ${process.env.DB_POOL_MIN || '未设置'}`);
  console.log(`   DB_SSL_ENABLED: ${process.env.DB_SSL_ENABLED || '未设置'}`);
  console.log('');
}

// 主函数
async function main() {
  showEnvironmentInfo();
  await testDatabaseConnection();
}

// 运行测试
if (require.main === module) {
  main().catch(error => {
    colorLog('red', `测试失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { testDatabaseConnection, getPoolConfig };
