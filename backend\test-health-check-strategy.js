/**
 * 混合健康检查策略测试脚本
 * 测试懒加载健康检查、隔离池机制和后台修复任务
 */

const { ConfigAdapter } = require('./src/gateway/adapters/ConfigAdapter');
const { WorkerPoolController } = require('./src/gateway/core/WorkerPoolController');
const { ProxyGateway } = require('./src/gateway/core/ProxyGateway');
const { LoggerAdapter } = require('./src/gateway/adapters/LoggerAdapter');

// 测试配置
const testConfig = {
  NETWORK_MODE: 'gateway',
  ENABLE_SINGBOX_GATEWAY: true,
  SINGBOX_API_ENDPOINT: 'http://127.0.0.1:9090',
  SINGBOX_SELECTOR_NAME: 'proxy-selector',
  SINGBOX_WORKER_POOL_SIZE: 3, // 使用较小的池进行测试
  SINGBOX_WORKER_PORT_START: 1081,
  SINGBOX_WORKER_SELECTOR_PREFIX: 'worker-selector',
  SINGBOX_HEALTH_CHECK_INTERVAL: 10000, // 10秒
  SINGBOX_QUARANTINE_CHECK_INTERVAL: 15000, // 15秒
  SINGBOX_QUARANTINE_HEALTH_CHECK_TIMEOUT: 3000,
  SINGBOX_QUARANTINE_RECOVERY_THRESHOLD: 2,
  SINGBOX_QUARANTINE_PERMANENT_RECOVERY_THRESHOLD: 3,
  SINGBOX_QUARANTINE_ENABLE_PERMANENT_RECOVERY: true
};

class HealthCheckStrategyTester {
  constructor() {
    this.logger = new LoggerAdapter('HEALTH-CHECK-TEST');
    this.workerPoolController = null;
    this.proxyGateway = null;
    this.testResults = {
      lazyHealthCheck: false,
      quarantineMechanism: false,
      backgroundRecovery: false,
      persistence: false
    };
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    this.logger.info('🚀 Starting Health Check Strategy Tests...');
    
    try {
      // 初始化组件
      await this.initializeComponents();
      
      // 测试1：懒加载健康检查
      await this.testLazyHealthCheck();
      
      // 测试2：隔离池机制
      await this.testQuarantineMechanism();
      
      // 测试3：持久化机制
      await this.testPersistence();
      
      // 测试4：后台修复任务
      await this.testBackgroundRecovery();
      
      // 输出测试结果
      this.printTestResults();
      
    } catch (error) {
      this.logger.error('Test execution failed', error);
    } finally {
      await this.cleanup();
    }
  }

  /**
   * 初始化测试组件
   */
  async initializeComponents() {
    this.logger.info('📋 Initializing test components...');
    
    try {
      // 创建WorkerPoolController
      this.workerPoolController = new WorkerPoolController(testConfig, this.logger.createSubLogger('WORKER-POOL'));
      
      // 模拟一些测试节点
      this.workerPoolController.allNodes = new Set(['test-node-1', 'test-node-2', 'test-node-3']);
      this.workerPoolController.healthyNodeTags = new Set(['test-node-1', 'test-node-2', 'test-node-3']);
      this.workerPoolController.failedNodes = new Map();
      
      // 初始化工人池
      this.workerPoolController.initializeWorkers();
      
      this.logger.info('✅ Components initialized successfully');
      
    } catch (error) {
      this.logger.error('❌ Component initialization failed', error);
      throw error;
    }
  }

  /**
   * 测试1：懒加载健康检查
   */
  async testLazyHealthCheck() {
    this.logger.info('🔍 Testing lazy health check...');

    try {
      // 模拟健康检查方法，让第一个节点失败
      const originalHealthCheck = this.workerPoolController.healthCheck.bind(this.workerPoolController);
      this.workerPoolController.healthCheck = async (nodeTag) => {
        if (nodeTag === 'test-node-1') {
          return false; // 第一个节点不健康
        }
        return true; // 其他节点健康
      };

      // 模拟commandSwitchNode方法，避免实际API调用
      const originalCommandSwitchNode = this.workerPoolController.commandSwitchNode.bind(this.workerPoolController);
      this.workerPoolController.commandSwitchNode = async (selector, nodeTag) => {
        this.logger.debug(`Mock switching ${selector} to ${nodeTag}`);
        return true;
      };

      // 尝试获取工人
      const result = await this.workerPoolController.acquireWorker();

      // 验证结果
      if (result && result.nodeTag !== 'test-node-1') {
        this.logger.info('✅ Lazy health check passed: unhealthy node was skipped');
        this.testResults.lazyHealthCheck = true;

        // 验证不健康节点是否被移入隔离池
        if (this.workerPoolController.failedNodes.has('test-node-1')) {
          this.logger.info('✅ Unhealthy node moved to quarantine pool');
        } else {
          this.logger.warn('⚠️ Unhealthy node not moved to quarantine pool');
        }

        // 释放工人
        this.workerPoolController.releaseWorker(result.worker);
      } else {
        this.logger.error('❌ Lazy health check failed: unhealthy node was used or no result');
      }

      // 恢复原始方法
      this.workerPoolController.healthCheck = originalHealthCheck;
      this.workerPoolController.commandSwitchNode = originalCommandSwitchNode;

    } catch (error) {
      this.logger.error('❌ Lazy health check test failed', error);
    }
  }

  /**
   * 测试2：隔离池机制
   */
  async testQuarantineMechanism() {
    this.logger.info('🏥 Testing quarantine mechanism...');
    
    try {
      // 测试临时隔离
      this.workerPoolController.moveNodeToQuarantine('test-node-2', 'Network timeout');
      
      // 验证节点被移入隔离池
      const quarantineInfo = this.workerPoolController.failedNodes.get('test-node-2');
      if (quarantineInfo && quarantineInfo.quarantineType === 'temporary') {
        this.logger.info('✅ Temporary quarantine mechanism working');
      } else {
        this.logger.error('❌ Temporary quarantine mechanism failed');
        return;
      }

      // 测试永久隔离
      this.workerPoolController.moveNodeToQuarantine('test-node-3', 'HTTP 403 quota_exceeded');
      
      const permanentQuarantineInfo = this.workerPoolController.failedNodes.get('test-node-3');
      if (permanentQuarantineInfo && permanentQuarantineInfo.quarantineType === 'permanent') {
        this.logger.info('✅ Permanent quarantine mechanism working');
        this.testResults.quarantineMechanism = true;
      } else {
        this.logger.error('❌ Permanent quarantine mechanism failed');
      }

      // 测试连续成功恢复逻辑
      const recovered1 = await this.workerPoolController.markNodeHealthy('test-node-2'); // 第一次成功
      if (!recovered1) {
        this.logger.info('✅ Node requires multiple successes for recovery');
      }

      const recovered2 = await this.workerPoolController.markNodeHealthy('test-node-2'); // 第二次成功
      if (recovered2) {
        this.logger.info('✅ Temporary node recovered after 2 successes');
      } else {
        this.logger.error('❌ Temporary node recovery failed');
      }
      
    } catch (error) {
      this.logger.error('❌ Quarantine mechanism test failed', error);
    }
  }

  /**
   * 测试3：持久化机制
   */
  async testPersistence() {
    this.logger.info('💾 Testing persistence mechanism...');

    try {
      // 确保测试节点在allNodes中
      this.workerPoolController.allNodes.add('test-persist-node');

      // 添加一个节点到隔离池
      this.workerPoolController.moveNodeToQuarantine('test-persist-node', 'Test persistence');

      // 保存隔离池数据
      await this.workerPoolController.saveQuarantineData();

      // 清空内存中的隔离池
      this.workerPoolController.failedNodes.clear();

      // 重新加载隔离池数据
      await this.workerPoolController.loadQuarantineData();

      // 验证数据是否恢复
      if (this.workerPoolController.failedNodes.has('test-persist-node')) {
        this.logger.info('✅ Persistence mechanism working');
        this.testResults.persistence = true;
      } else {
        this.logger.error('❌ Persistence mechanism failed');
      }

    } catch (error) {
      this.logger.error('❌ Persistence test failed', error);
    }
  }

  /**
   * 测试4：后台修复任务
   */
  async testBackgroundRecovery() {
    this.logger.info('🔄 Testing background recovery...');

    try {
      // 创建ProxyGateway来测试后台修复
      this.proxyGateway = new ProxyGateway(testConfig, this.logger.createSubLogger('PROXY-GATEWAY'));
      this.proxyGateway.singboxController = this.workerPoolController;

      // 确保测试节点在allNodes中
      this.workerPoolController.allNodes.add('test-recovery-node');

      // 模拟健康检查总是返回true
      this.workerPoolController.healthCheck = async () => true;

      // 添加一个临时隔离节点
      this.workerPoolController.moveNodeToQuarantine('test-recovery-node', 'Network error');

      // 执行隔离池检查
      await this.proxyGateway.performQuarantineCheck();

      // 验证节点是否开始恢复过程
      const recoveryInfo = this.workerPoolController.failedNodes.get('test-recovery-node');
      if (recoveryInfo && recoveryInfo.consecutiveSuccesses > 0) {
        this.logger.info('✅ Background recovery mechanism working');
        this.testResults.backgroundRecovery = true;
      } else {
        this.logger.error('❌ Background recovery mechanism failed');
        // 输出调试信息
        this.logger.debug('Recovery info:', recoveryInfo);
      }

    } catch (error) {
      this.logger.error('❌ Background recovery test failed', error);
    }
  }

  /**
   * 输出测试结果
   */
  printTestResults() {
    this.logger.info('📊 Test Results Summary:');
    this.logger.info('========================');
    
    const results = [
      { name: 'Lazy Health Check', passed: this.testResults.lazyHealthCheck },
      { name: 'Quarantine Mechanism', passed: this.testResults.quarantineMechanism },
      { name: 'Persistence', passed: this.testResults.persistence },
      { name: 'Background Recovery', passed: this.testResults.backgroundRecovery }
    ];

    let passedCount = 0;
    results.forEach(result => {
      const status = result.passed ? '✅ PASSED' : '❌ FAILED';
      this.logger.info(`${result.name}: ${status}`);
      if (result.passed) passedCount++;
    });

    this.logger.info('========================');
    this.logger.info(`Overall: ${passedCount}/${results.length} tests passed`);
    
    if (passedCount === results.length) {
      this.logger.info('🎉 All tests passed! Health check strategy is working correctly.');
    } else {
      this.logger.warn('⚠️ Some tests failed. Please check the implementation.');
    }
  }

  /**
   * 清理资源
   */
  async cleanup() {
    this.logger.info('🧹 Cleaning up test resources...');
    
    try {
      if (this.proxyGateway) {
        await this.proxyGateway.stop();
      }
      
      if (this.workerPoolController) {
        await this.workerPoolController.cleanup();
      }
      
      this.logger.info('✅ Cleanup completed');
    } catch (error) {
      this.logger.error('❌ Cleanup failed', error);
    }
  }
}

// 运行测试
if (require.main === module) {
  const tester = new HealthCheckStrategyTester();
  tester.runAllTests().catch(console.error);
}

module.exports = { HealthCheckStrategyTester };
