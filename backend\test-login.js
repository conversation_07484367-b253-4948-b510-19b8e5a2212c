#!/usr/bin/env node

/**
 * 登录测试脚本
 * 测试创建的测试用户是否可以正常登录
 */

const http = require('http');

const BASE_URL = 'http://localhost:3000';

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// HTTP请求函数
function makeRequest(method, path, data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, BASE_URL);
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'TTS-Login-Test/1.0',
        ...headers
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: responseData
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// 测试用户登录
async function testLogin(username, password, description) {
  log('blue', `\n🔐 测试登录: ${description}`);
  log('blue', `   用户名: ${username}`);
  log('blue', `   密码: ${password}`);
  
  try {
    const response = await makeRequest('POST', '/api/auth/login', {
      username: username,
      password: password
    });
    
    if (response.statusCode === 200) {
      const data = JSON.parse(response.body);
      log('green', '✅ 登录成功！');
      log('green', `   用户ID: ${data.user?.id}`);
      log('green', `   用户名: ${data.user?.username}`);
      log('green', `   邮箱: ${data.user?.email}`);
      log('green', `   VIP类型: ${data.user?.vipInfo?.type || '普通用户'}`);
      log('green', `   配额: ${data.user?.vipInfo?.quotaChars?.toLocaleString()} 字符`);
      log('green', `   已使用: ${data.user?.usageStats?.totalChars || 0} 字符`);
      log('green', `   访问令牌: ${data.accessToken?.substring(0, 30)}...`);
      
      return {
        success: true,
        token: data.accessToken,
        user: data.user
      };
    } else {
      const errorData = JSON.parse(response.body);
      log('red', `❌ 登录失败 (${response.statusCode})`);
      log('red', `   错误: ${errorData.error || '未知错误'}`);
      return { success: false, error: errorData.error };
    }
  } catch (error) {
    log('red', `❌ 登录请求失败: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// 测试需要认证的API
async function testAuthenticatedAPI(token, description) {
  log('blue', `\n🔒 测试认证API: ${description}`);
  
  const tests = [
    { path: '/api/user/quota', name: '用户配额' },
    { path: '/api/tts/voices', name: '语音列表' },
    { path: '/api/admin/stats', name: '管理员统计' }
  ];
  
  for (const test of tests) {
    try {
      const response = await makeRequest('GET', test.path, null, {
        'Authorization': `Bearer ${token}`
      });
      
      if (response.statusCode === 200) {
        log('green', `✅ ${test.name}: 访问成功`);
        
        // 显示部分响应数据
        try {
          const data = JSON.parse(response.body);
          if (test.path === '/api/user/quota') {
            log('green', `   配额信息: ${data.quotaChars?.toLocaleString()} 字符`);
          } else if (test.path === '/api/tts/voices') {
            log('green', `   语音数量: ${data.voices?.length || 0} 个`);
          } else if (test.path === '/api/admin/stats') {
            log('green', `   系统统计: 用户数 ${data.totalUsers || 0}`);
          }
        } catch (e) {
          // 忽略JSON解析错误
        }
      } else if (response.statusCode === 403) {
        log('yellow', `⚠️  ${test.name}: 权限不足 (403)`);
      } else {
        log('red', `❌ ${test.name}: 访问失败 (${response.statusCode})`);
      }
    } catch (error) {
      log('red', `❌ ${test.name}: 请求错误 - ${error.message}`);
    }
  }
}

// 主测试函数
async function runLoginTests() {
  log('blue', '🧪 开始登录功能测试...');
  log('blue', '================================');
  
  const testUsers = [
    { username: 'admin', password: 'admin', description: '管理员用户' },
    { username: 'testuser', password: 'testpass', description: '普通用户' },
    { username: '<EMAIL>', password: 'admin', description: '管理员邮箱登录' }
  ];
  
  const results = [];
  
  for (const user of testUsers) {
    const result = await testLogin(user.username, user.password, user.description);
    results.push({ user, result });
    
    // 如果登录成功，测试认证API
    if (result.success && result.token) {
      await testAuthenticatedAPI(result.token, user.description);
    }
  }
  
  // 测试错误登录
  log('blue', '\n🚫 测试错误登录...');
  await testLogin('wronguser', 'wrongpass', '错误用户名密码');
  await testLogin('admin', 'wrongpass', '正确用户名错误密码');
  
  // 输出测试总结
  log('blue', '\n📊 登录测试总结:');
  log('blue', '================================');
  
  const successful = results.filter(r => r.result.success).length;
  const total = results.length;
  
  results.forEach(({ user, result }) => {
    log(result.success ? 'green' : 'red', 
        `${result.success ? '✅' : '❌'} ${user.description}: ${user.username}`);
  });
  
  log('blue', `\n总计: ${total} 个登录测试`);
  log('green', `成功: ${successful} 个`);
  log('red', `失败: ${total - successful} 个`);
  
  if (successful > 0) {
    log('green', '\n🎉 测试用户可以正常登录！');
    log('green', '\n📋 可用的测试账号:');
    log('green', '   👑 管理员: admin / admin');
    log('green', '   👤 普通用户: testuser / testpass');
    log('green', '   📧 邮箱登录: <EMAIL> / admin');
    
    log('blue', '\n🔗 前端登录测试建议:');
    log('blue', '   1. 使用 admin/admin 测试管理员功能');
    log('blue', '   2. 使用 testuser/testpass 测试普通用户功能');
    log('blue', '   3. 测试邮箱登录功能');
    log('blue', '   4. 测试TTS语音生成功能');
  } else {
    log('red', '\n❌ 所有登录测试失败，请检查用户创建和密码哈希');
  }
  
  return results;
}

// 启动测试
if (require.main === module) {
  runLoginTests().catch(console.error);
}

module.exports = { testLogin, testAuthenticatedAPI, runLoginTests };
