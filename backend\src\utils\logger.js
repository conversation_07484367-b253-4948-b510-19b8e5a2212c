const fs = require('fs');
const path = require('path');

class Logger {
  constructor() {
    this.logDir = process.env.LOG_DIR || './logs';
    this.ensureLogDir();
  }

  ensureLogDir() {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  formatMessage(level, message, data = {}, context = {}) {
    const timestamp = new Date().toISOString();
    const username = context.username || 'system';
    const taskId = context.taskId || 'N/A';

    let contextParts = `[user:${username}] [task:${taskId}]`;
    if (context.chunkIndex) {
      contextParts += ` [chunk:${context.chunkIndex}]`;
    }

    const logString = `[${level}] [${timestamp}] ${contextParts} - ${message}`;

    if (Object.keys(data).length > 0) {
      return `${logString} ${JSON.stringify(data)}`;
    }
    return logString;
  }

  log(level, message, data = {}, context = {}) {
    // 只有在DEBUG模式下才输出DEBUG级别的日志
    if (level === 'DEBUG' && !(process.env.DEBUG === 'true' || process.env.DEBUG === true)) {
      return;
    }

    const formattedMessage = this.formatMessage(level, message, data, context);
    
    // 输出到控制台
    console.log(formattedMessage);

    // 写入日志文件
    this.writeToFile(level, formattedMessage);
  }

  writeToFile(level, message) {
    try {
      const date = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
      const logFile = path.join(this.logDir, `${date}.log`);
      
      fs.appendFileSync(logFile, message + '\n');
    } catch (error) {
      console.error('Failed to write log to file:', error);
    }
  }

  debug(message, data = {}, context = {}) {
    this.log('DEBUG', message, data, context);
  }

  info(message, data = {}, context = {}) {
    this.log('INFO', message, data, context);
  }

  warn(message, data = {}, context = {}) {
    this.log('WARN', message, data, context);
  }

  error(error, context = {}, additionalData = {}) {
    const message = error.message || 'Unknown error';
    const data = {
      ...additionalData,
      error: message,
      stack: error.stack?.substring(0, 500) // 限制堆栈长度
    };
    this.log('ERROR', message, data, context);
  }
}

// 创建全局logger实例
const logger = new Logger();

// 增强环境对象的日志功能
function enhanceEnvWithLogging(env, logContext = {}) {
  // 在env中注入日志相关属性
  env._logContext = logContext;
  env._logger = logger;

  // 提供便捷的日志方法，自动使用上下文
  env._log = {
    debug: (message, data = {}) => logger.debug(message, data, env._logContext),
    info: (message, data = {}) => logger.info(message, data, env._logContext),
    warn: (message, data = {}) => logger.warn(message, data, env._logContext),
    error: (error, additionalData = {}) => logger.error(error, env._logContext, additionalData)
  };

  return env;
}

module.exports = {
  logger,
  enhanceEnvWithLogging
};
