// 测试认证错误处理的脚本
const fetch = require('node-fetch');

async function testAuthError() {
  console.log('测试认证错误处理...');
  
  try {
    // 使用一个无效的token测试
    const response = await fetch('http://localhost:3001/api/user/quota', {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer invalid_token_here',
        'Content-Type': 'application/json'
      }
    });
    
    console.log('响应状态:', response.status);
    console.log('响应状态文本:', response.statusText);
    
    const data = await response.json();
    console.log('响应数据:', JSON.stringify(data, null, 2));
    
    // 检查是否包含错误码
    if (data.code) {
      console.log('✅ 错误码存在:', data.code);
    } else {
      console.log('❌ 错误码缺失');
    }
    
  } catch (error) {
    console.error('请求失败:', error.message);
  }
}

async function testNoToken() {
  console.log('\n测试无Token情况...');
  
  try {
    const response = await fetch('http://localhost:3001/api/user/quota', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('响应状态:', response.status);
    console.log('响应状态文本:', response.statusText);
    
    const data = await response.json();
    console.log('响应数据:', JSON.stringify(data, null, 2));
    
    // 检查是否包含错误码
    if (data.code) {
      console.log('✅ 错误码存在:', data.code);
    } else {
      console.log('❌ 错误码缺失');
    }
    
  } catch (error) {
    console.error('请求失败:', error.message);
  }
}

// 运行测试
testAuthError().then(() => testNoToken());
