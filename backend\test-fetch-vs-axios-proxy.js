#!/usr/bin/env node

/**
 * 测试fetch和axios在SOCKS代理上的差异
 * 验证为什么axios可以正确使用代理而fetch不行
 */

require('dotenv').config();
const { SocksProxyAgent } = require('socks-proxy-agent');

// 颜色输出函数
function log(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color] || ''}${message}${colors.reset}`);
}

const TEST_URL = 'https://api.ipify.org?format=json';
const PROXY_PORT = 1081;

/**
 * 测试axios代理
 */
async function testAxiosProxy() {
  log('blue', '\n🧪 测试axios + SOCKS代理');
  log('blue', '========================');
  
  try {
    const axios = require('axios');
    const proxyAgent = new SocksProxyAgent(`socks5h://127.0.0.1:${PROXY_PORT}`);
    
    console.log('   代理配置:', `socks5h://127.0.0.1:${PROXY_PORT}`);
    console.log('   请求URL:', TEST_URL);
    
    const response = await axios.get(TEST_URL, {
      httpsAgent: proxyAgent,
      httpAgent: proxyAgent,
      timeout: 15000
    });
    
    log('green', '✅ axios代理请求成功');
    console.log('   返回IP:', response.data.ip);
    
    return { success: true, ip: response.data.ip, method: 'axios' };
    
  } catch (error) {
    log('red', '❌ axios代理请求失败');
    console.log('   错误:', error.message);
    
    return { success: false, error: error.message, method: 'axios' };
  }
}

/**
 * 测试fetch代理（我们当前的方式）
 */
async function testFetchProxy() {
  log('blue', '\n🧪 测试fetch + SOCKS代理');
  log('blue', '========================');
  
  try {
    const proxyAgent = new SocksProxyAgent(`socks5h://127.0.0.1:${PROXY_PORT}`);
    
    console.log('   代理配置:', `socks5h://127.0.0.1:${PROXY_PORT}`);
    console.log('   请求URL:', TEST_URL);
    
    const fetchOptions = {
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      },
      agent: proxyAgent,
      signal: AbortSignal.timeout(15000)
    };
    
    console.log('   fetch选项:', {
      method: fetchOptions.method,
      hasAgent: !!fetchOptions.agent,
      agentType: fetchOptions.agent?.constructor?.name,
      hasSignal: !!fetchOptions.signal
    });
    
    const response = await fetch(TEST_URL, fetchOptions);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    
    log('green', '✅ fetch代理请求成功');
    console.log('   返回IP:', data.ip);
    
    return { success: true, ip: data.ip, method: 'fetch' };
    
  } catch (error) {
    log('red', '❌ fetch代理请求失败');
    console.log('   错误:', error.message);
    
    return { success: false, error: error.message, method: 'fetch' };
  }
}

/**
 * 测试直连（对比）
 */
async function testDirectConnection() {
  log('blue', '\n🧪 测试直连（对比）');
  log('blue', '==================');
  
  try {
    const response = await fetch(TEST_URL, {
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      },
      signal: AbortSignal.timeout(15000)
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    
    log('green', '✅ 直连请求成功');
    console.log('   返回IP:', data.ip);
    
    return { success: true, ip: data.ip, method: 'direct' };
    
  } catch (error) {
    log('red', '❌ 直连请求失败');
    console.log('   错误:', error.message);
    
    return { success: false, error: error.message, method: 'direct' };
  }
}

/**
 * 测试Node.js版本和fetch支持
 */
function testEnvironment() {
  log('blue', '\n🔍 环境检查');
  log('blue', '============');
  
  console.log('   Node.js版本:', process.version);
  console.log('   fetch可用:', typeof fetch !== 'undefined');
  console.log('   AbortSignal.timeout可用:', typeof AbortSignal !== 'undefined' && typeof AbortSignal.timeout === 'function');
  
  // 检查fetch是否是原生的还是polyfill
  if (typeof fetch !== 'undefined') {
    console.log('   fetch类型:', fetch.toString().includes('[native code]') ? 'native' : 'polyfill');
  }
  
  // 检查SocksProxyAgent版本
  try {
    const pkg = require('socks-proxy-agent/package.json');
    console.log('   socks-proxy-agent版本:', pkg.version);
  } catch (e) {
    console.log('   socks-proxy-agent版本: 无法获取');
  }
}

/**
 * 主测试函数
 */
async function runProxyComparison() {
  log('cyan', '🔬 fetch vs axios 代理对比测试');
  log('cyan', '================================');
  
  // 环境检查
  testEnvironment();
  
  const results = [];
  
  // 测试直连
  const directResult = await testDirectConnection();
  results.push(directResult);
  
  // 测试axios代理
  const axiosResult = await testAxiosProxy();
  results.push(axiosResult);
  
  // 测试fetch代理
  const fetchResult = await testFetchProxy();
  results.push(fetchResult);
  
  // 分析结果
  log('cyan', '\n📊 测试结果分析');
  log('cyan', '================');
  
  const successResults = results.filter(r => r.success);
  const directIP = directResult.success ? directResult.ip : null;
  
  console.log('\n📋 结果汇总:');
  results.forEach(result => {
    const status = result.success ? '✅ 成功' : '❌ 失败';
    const ip = result.success ? result.ip : result.error;
    console.log(`   ${result.method}: ${status} - ${ip}`);
  });
  
  if (successResults.length >= 2) {
    const uniqueIPs = new Set(successResults.map(r => r.ip));
    
    console.log('\n🔍 IP地址分析:');
    console.log(`   唯一IP数量: ${uniqueIPs.size}`);
    console.log(`   IP列表: ${Array.from(uniqueIPs).join(', ')}`);
    
    if (uniqueIPs.size > 1) {
      log('green', '🎉 检测到不同IP，说明代理工作正常！');
      
      // 分析哪个方法使用了代理
      if (axiosResult.success && fetchResult.success) {
        if (axiosResult.ip !== directIP && fetchResult.ip === directIP) {
          log('yellow', '⚠️  关键发现: axios使用了代理，但fetch没有使用代理！');
        } else if (axiosResult.ip === directIP && fetchResult.ip !== directIP) {
          log('yellow', '⚠️  关键发现: fetch使用了代理，但axios没有使用代理！');
        } else if (axiosResult.ip !== directIP && fetchResult.ip !== directIP) {
          if (axiosResult.ip === fetchResult.ip) {
            log('green', '✅ 两种方法都正确使用了代理');
          } else {
            log('yellow', '⚠️  两种方法使用了不同的代理出口');
          }
        }
      }
    } else {
      log('yellow', '⚠️  所有方法返回相同IP，可能都没有使用代理');
    }
  }
  
  // 给出建议
  console.log('\n💡 建议:');
  if (axiosResult.success && !fetchResult.success) {
    log('yellow', '   建议在后端代码中使用axios替代fetch进行代理请求');
  } else if (!axiosResult.success && fetchResult.success) {
    log('green', '   fetch代理配置正确，可以继续使用');
  } else if (axiosResult.success && fetchResult.success) {
    if (directIP && axiosResult.ip !== directIP && fetchResult.ip === directIP) {
      log('red', '   ❌ fetch代理配置有问题，建议使用axios');
    } else {
      log('green', '   ✅ 两种方法都可以使用');
    }
  } else {
    log('red', '   ❌ 两种方法都有问题，请检查sing-box配置');
  }
  
  return results;
}

// 运行测试
if (require.main === module) {
  runProxyComparison().catch(error => {
    log('red', '❌ 测试执行失败:');
    console.error(error);
    process.exit(1);
  });
}

module.exports = { runProxyComparison };
