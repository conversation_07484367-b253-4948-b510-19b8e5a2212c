(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[640],{64:(e,t,r)=>{"use strict";r.d(t,{UC:()=>X,B8:()=>W,bL:()=>H,l9:()=>V});var n=r(2115),i=r(5185),o=r(6081),s=r(2284),u=r(6101),a=r(1285),l=r(3655),c=r(9033),d=r(5845),f=r(4315),h=r(5155),p="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[y,_,g]=(0,s.N)(v),[S,w]=(0,o.A)(v,[g]),[b,A]=S(v),R=n.forwardRef((e,t)=>(0,h.jsx)(y.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,h.jsx)(y.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,h.jsx)(x,{...e,ref:t})})}));R.displayName=v;var x=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:o,loop:s=!1,dir:a,currentTabStopId:v,defaultCurrentTabStopId:y,onCurrentTabStopIdChange:g,onEntryFocus:S,preventScrollOnEntryFocus:w=!1,...A}=e,R=n.useRef(null),x=(0,u.s)(t,R),C=(0,f.jH)(a),[N=null,k]=(0,d.i)({prop:v,defaultProp:y,onChange:g}),[j,M]=n.useState(!1),T=(0,c.c)(S),E=_(r),I=n.useRef(!1),[z,O]=n.useState(0);return n.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(p,T),()=>e.removeEventListener(p,T)},[T]),(0,h.jsx)(b,{scope:r,orientation:o,dir:C,loop:s,currentTabStopId:N,onItemFocus:n.useCallback(e=>k(e),[k]),onItemShiftTab:n.useCallback(()=>M(!0),[]),onFocusableItemAdd:n.useCallback(()=>O(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>O(e=>e-1),[]),children:(0,h.jsx)(l.sG.div,{tabIndex:j||0===z?-1:0,"data-orientation":o,...A,ref:x,style:{outline:"none",...e.style},onMouseDown:(0,i.m)(e.onMouseDown,()=>{I.current=!0}),onFocus:(0,i.m)(e.onFocus,e=>{let t=!I.current;if(e.target===e.currentTarget&&t&&!j){let t=new CustomEvent(p,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=E().filter(e=>e.focusable);F([e.find(e=>e.active),e.find(e=>e.id===N),...e].filter(Boolean).map(e=>e.ref.current),w)}}I.current=!1}),onBlur:(0,i.m)(e.onBlur,()=>M(!1))})})}),C="RovingFocusGroupItem",N=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:o=!0,active:s=!1,tabStopId:u,...c}=e,d=(0,a.B)(),f=u||d,p=A(C,r),m=p.currentTabStopId===f,v=_(r),{onFocusableItemAdd:g,onFocusableItemRemove:S}=p;return n.useEffect(()=>{if(o)return g(),()=>S()},[o,g,S]),(0,h.jsx)(y.ItemSlot,{scope:r,id:f,focusable:o,active:s,children:(0,h.jsx)(l.sG.span,{tabIndex:m?0:-1,"data-orientation":p.orientation,...c,ref:t,onMouseDown:(0,i.m)(e.onMouseDown,e=>{o?p.onItemFocus(f):e.preventDefault()}),onFocus:(0,i.m)(e.onFocus,()=>p.onItemFocus(f)),onKeyDown:(0,i.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){p.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let i=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(i))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(i)))return k[i]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=v().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=p.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>F(r))}})})})});N.displayName=C;var k={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function F(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var j=r(8905),M="Tabs",[T,E]=(0,o.A)(M,[w]),I=w(),[z,O]=T(M),L=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:i,defaultValue:o,orientation:s="horizontal",dir:u,activationMode:c="automatic",...p}=e,m=(0,f.jH)(u),[v,y]=(0,d.i)({prop:n,onChange:i,defaultProp:o});return(0,h.jsx)(z,{scope:r,baseId:(0,a.B)(),value:v,onValueChange:y,orientation:s,dir:m,activationMode:c,children:(0,h.jsx)(l.sG.div,{dir:m,"data-orientation":s,...p,ref:t})})});L.displayName=M;var D="TabsList",P=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...i}=e,o=O(D,r),s=I(r);return(0,h.jsx)(R,{asChild:!0,...s,orientation:o.orientation,dir:o.dir,loop:n,children:(0,h.jsx)(l.sG.div,{role:"tablist","aria-orientation":o.orientation,...i,ref:t})})});P.displayName=D;var U="TabsTrigger",G=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:o=!1,...s}=e,u=O(U,r),a=I(r),c=B(u.baseId,n),d=K(u.baseId,n),f=n===u.value;return(0,h.jsx)(N,{asChild:!0,...a,focusable:!o,active:f,children:(0,h.jsx)(l.sG.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":d,"data-state":f?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:c,...s,ref:t,onMouseDown:(0,i.m)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():u.onValueChange(n)}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&u.onValueChange(n)}),onFocus:(0,i.m)(e.onFocus,()=>{let e="manual"!==u.activationMode;f||o||!e||u.onValueChange(n)})})})});G.displayName=U;var $="TabsContent",q=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:i,forceMount:o,children:s,...u}=e,a=O($,r),c=B(a.baseId,i),d=K(a.baseId,i),f=i===a.value,p=n.useRef(f);return n.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,h.jsx)(j.C,{present:o||f,children:r=>{let{present:n}=r;return(0,h.jsx)(l.sG.div,{"data-state":f?"active":"inactive","data-orientation":a.orientation,role:"tabpanel","aria-labelledby":c,hidden:!n,id:d,tabIndex:0,...u,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:n&&s})}})});function B(e,t){return"".concat(e,"-trigger-").concat(t)}function K(e,t){return"".concat(e,"-content-").concat(t)}q.displayName=$;var H=L,W=P,V=G,X=q},1285:(e,t,r)=>{"use strict";r.d(t,{B:()=>a});var n,i=r(2115),o=r(2712),s=(n||(n=r.t(i,2)))["useId".toString()]||(()=>void 0),u=0;function a(e){let[t,r]=i.useState(s());return(0,o.N)(()=>{e||r(e=>e??String(u++))},[e]),e||(t?`radix-${t}`:"")}},1436:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("Diamond",[["path",{d:"M2.7 10.3a2.41 2.41 0 0 0 0 3.41l7.59 7.59a2.41 2.41 0 0 0 3.41 0l7.59-7.59a2.41 2.41 0 0 0 0-3.41l-7.59-7.59a2.41 2.41 0 0 0-3.41 0Z",key:"1f1r0c"}]])},1539:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},1586:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},2269:(e,t,r)=>{"use strict";var n=r(9509);r(8375);var i=r(2115),o=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(i),s=void 0!==n&&n.env&&!0,u=function(e){return"[object String]"===Object.prototype.toString.call(e)},a=function(){function e(e){var t=void 0===e?{}:e,r=t.name,n=void 0===r?"stylesheet":r,i=t.optimizeForSpeed,o=void 0===i?s:i;l(u(n),"`name` must be a string"),this._name=n,this._deletedRulePlaceholder="#"+n+"-deleted-rule____{}",l("boolean"==typeof o,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=o,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var a="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=a?a.getAttribute("content"):null}var t=e.prototype;return t.setOptimizeForSpeed=function(e){l("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),l(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},t.isOptimizeForSpeed=function(){return this._optimizeForSpeed},t.inject=function(){var e=this;if(l(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(s||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,r){return"number"==typeof r?e._serverSheet.cssRules[r]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),r},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},t.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},t.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},t.insertRule=function(e,t){if(l(u(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var r=this.getSheet();"number"!=typeof t&&(t=r.cssRules.length);try{r.insertRule(e,t)}catch(t){return s||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var n=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,n))}return this._rulesCount++},t.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var r="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!r.cssRules[e])return e;r.deleteRule(e);try{r.insertRule(t,e)}catch(n){s||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),r.insertRule(this._deletedRulePlaceholder,e)}}else{var n=this._tags[e];l(n,"old rule at index `"+e+"` not found"),n.textContent=t}return e},t.deleteRule=function(e){if("undefined"==typeof window){this._serverSheet.deleteRule(e);return}if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];l(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},t.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},t.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,r){return r?t=t.concat(Array.prototype.map.call(e.getSheetForTag(r).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},t.makeStyleTag=function(e,t,r){t&&l(u(t),"makeStyleTag accepts only strings as second parameter");var n=document.createElement("style");this._nonce&&n.setAttribute("nonce",this._nonce),n.type="text/css",n.setAttribute("data-"+e,""),t&&n.appendChild(document.createTextNode(t));var i=document.head||document.getElementsByTagName("head")[0];return r?i.insertBefore(n,r):i.appendChild(n),n},function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(e.prototype,[{key:"length",get:function(){return this._rulesCount}}]),e}();function l(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var c=function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0},d={};function f(e,t){if(!t)return"jsx-"+e;var r=String(t),n=e+r;return d[n]||(d[n]="jsx-"+c(e+"-"+r)),d[n]}function h(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var r=e+t;return d[r]||(d[r]=t.replace(/__jsx-style-dynamic-selector/g,e)),d[r]}var p=function(){function e(e){var t=void 0===e?{}:e,r=t.styleSheet,n=void 0===r?null:r,i=t.optimizeForSpeed,o=void 0!==i&&i;this._sheet=n||new a({name:"styled-jsx",optimizeForSpeed:o}),this._sheet.inject(),n&&"boolean"==typeof o&&(this._sheet.setOptimizeForSpeed(o),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var r=this.getIdAndRules(e),n=r.styleId,i=r.rules;if(n in this._instancesCounts){this._instancesCounts[n]+=1;return}var o=i.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[n]=o,this._instancesCounts[n]=1},t.remove=function(e){var t=this,r=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(r in this._instancesCounts,"styleId: `"+r+"` not found"),this._instancesCounts[r]-=1,this._instancesCounts[r]<1){var n=this._fromServer&&this._fromServer[r];n?(n.parentNode.removeChild(n),delete this._fromServer[r]):(this._indices[r].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[r]),delete this._instancesCounts[r]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],r=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return r[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,r;return t=this.cssRules(),void 0===(r=e)&&(r={}),t.map(function(e){var t=e[0],n=e[1];return o.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:r.nonce?r.nonce:void 0,dangerouslySetInnerHTML:{__html:n}})})},t.getIdAndRules=function(e){var t=e.children,r=e.dynamic,n=e.id;if(r){var i=f(n,r);return{styleId:i,rules:Array.isArray(t)?t.map(function(e){return h(i,e)}):[h(i,t)]}}return{styleId:f(n),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),m=i.createContext(null);m.displayName="StyleSheetContext";var v=o.default.useInsertionEffect||o.default.useLayoutEffect,y="undefined"!=typeof window?new p:void 0;function _(e){var t=y||i.useContext(m);return t&&("undefined"==typeof window?t.add(e):v(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}_.dynamic=function(e){return e.map(function(e){return f(e[0],e[1])}).join(" ")},t.style=_},2284:(e,t,r)=>{"use strict";r.d(t,{N:()=>a});var n=r(2115),i=r(6081),o=r(6101),s=r(9708),u=r(5155);function a(e){let t=e+"CollectionProvider",[r,a]=(0,i.A)(t),[l,c]=r(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:r}=e,i=n.useRef(null),o=n.useRef(new Map).current;return(0,u.jsx)(l,{scope:t,itemMap:o,collectionRef:i,children:r})};d.displayName=t;let f=e+"CollectionSlot",h=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,i=c(f,r),a=(0,o.s)(t,i.collectionRef);return(0,u.jsx)(s.DX,{ref:a,children:n})});h.displayName=f;let p=e+"CollectionItemSlot",m="data-radix-collection-item",v=n.forwardRef((e,t)=>{let{scope:r,children:i,...a}=e,l=n.useRef(null),d=(0,o.s)(t,l),f=c(p,r);return n.useEffect(()=>(f.itemMap.set(l,{ref:l,...a}),()=>void f.itemMap.delete(l))),(0,u.jsx)(s.DX,{[m]:"",ref:d,children:i})});return v.displayName=p,[{Provider:d,Slot:h,ItemSlot:v},function(t){let r=c(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},a]}},2712:(e,t,r)=>{"use strict";r.d(t,{N:()=>i});var n=r(2115),i=globalThis?.document?n.useLayoutEffect:()=>{}},3311:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},3655:(e,t,r)=>{"use strict";r.d(t,{hO:()=>a,sG:()=>u});var n=r(2115),i=r(7650),o=r(9708),s=r(5155),u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=n.forwardRef((e,r)=>{let{asChild:n,...i}=e,u=n?o.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(u,{...i,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function a(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},4186:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4315:(e,t,r)=>{"use strict";r.d(t,{jH:()=>o});var n=r(2115);r(5155);var i=n.createContext(void 0);function o(e){let t=n.useContext(i);return e||t||"ltr"}},5185:(e,t,r)=>{"use strict";function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}r.d(t,{m:()=>n})},5845:(e,t,r)=>{"use strict";r.d(t,{i:()=>o});var n=r(2115),i=r(9033);function o({prop:e,defaultProp:t,onChange:r=()=>{}}){let[o,s]=function({defaultProp:e,onChange:t}){let r=n.useState(e),[o]=r,s=n.useRef(o),u=(0,i.c)(t);return n.useEffect(()=>{s.current!==o&&(u(o),s.current=o)},[o,s,u]),r}({defaultProp:t,onChange:r}),u=void 0!==e,a=u?e:o,l=(0,i.c)(r);return[a,n.useCallback(t=>{if(u){let r="function"==typeof t?t(e):t;r!==e&&l(r)}else s(t)},[u,e,s,l])]}},6081:(e,t,r)=>{"use strict";r.d(t,{A:()=>s,q:()=>o});var n=r(2115),i=r(5155);function o(e,t){let r=n.createContext(t),o=e=>{let{children:t,...o}=e,s=n.useMemo(()=>o,Object.values(o));return(0,i.jsx)(r.Provider,{value:s,children:t})};return o.displayName=e+"Provider",[o,function(i){let o=n.useContext(r);if(o)return o;if(void 0!==t)return t;throw Error(`\`${i}\` must be used within \`${e}\``)}]}function s(e,t=[]){let r=[],o=()=>{let t=r.map(e=>n.createContext(e));return function(r){let i=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:i}}),[r,i])}};return o.scopeName=e,[function(t,o){let s=n.createContext(o),u=r.length;r=[...r,o];let a=t=>{let{scope:r,children:o,...a}=t,l=r?.[e]?.[u]||s,c=n.useMemo(()=>a,Object.values(a));return(0,i.jsx)(l.Provider,{value:c,children:o})};return a.displayName=t+"Provider",[a,function(r,i){let a=i?.[e]?.[u]||s,l=n.useContext(a);if(l)return l;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=r.reduce((t,{useScope:r,scopeName:n})=>{let i=r(e)[`__scope${n}`];return{...t,...i}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return r.scopeName=t.scopeName,r}(o,...t)]}},7550:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},7580:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},7951:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("Crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]])},8375:()=>{},8564:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},8905:(e,t,r)=>{"use strict";r.d(t,{C:()=>s});var n=r(2115),i=r(6101),o=r(2712),s=e=>{let{present:t,children:r}=e,s=function(e){var t,r;let[i,s]=n.useState(),a=n.useRef({}),l=n.useRef(e),c=n.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=u(a.current);c.current="mounted"===d?e:"none"},[d]),(0,o.N)(()=>{let t=a.current,r=l.current;if(r!==e){let n=c.current,i=u(t);e?f("MOUNT"):"none"===i||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):r&&n!==i?f("ANIMATION_OUT"):f("UNMOUNT"),l.current=e}},[e,f]),(0,o.N)(()=>{if(i){var e;let t;let r=null!==(e=i.ownerDocument.defaultView)&&void 0!==e?e:window,n=e=>{let n=u(a.current).includes(e.animationName);if(e.target===i&&n&&(f("ANIMATION_END"),!l.current)){let e=i.style.animationFillMode;i.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=e)})}},o=e=>{e.target===i&&(c.current=u(a.current))};return i.addEventListener("animationstart",o),i.addEventListener("animationcancel",n),i.addEventListener("animationend",n),()=>{r.clearTimeout(t),i.removeEventListener("animationstart",o),i.removeEventListener("animationcancel",n),i.removeEventListener("animationend",n)}}f("ANIMATION_END")},[i,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:n.useCallback(e=>{e&&(a.current=getComputedStyle(e)),s(e)},[])}}(t),a="function"==typeof r?r({present:s.isPresent}):n.Children.only(r),l=(0,i.s)(s.ref,function(e){var t,r;let n=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,i=n&&"isReactWarning"in n&&n.isReactWarning;return i?e.ref:(i=(n=null===(r=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===r?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof r||s.isPresent?n.cloneElement(a,{ref:l}):null};function u(e){return(null==e?void 0:e.animationName)||"none"}s.displayName="Presence"},9033:(e,t,r)=>{"use strict";r.d(t,{c:()=>i});var n=r(2115);function i(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},9037:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},9137:(e,t,r)=>{"use strict";e.exports=r(2269).style},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(2115);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:i=24,strokeWidth:u=2,absoluteStrokeWidth:a,className:l="",children:c,iconNode:d,...f}=e;return(0,n.createElement)("svg",{ref:t,...s,width:i,height:i,stroke:r,strokeWidth:a?24*Number(u)/Number(i):u,className:o("lucide",l),...f},[...d.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),a=(e,t)=>{let r=(0,n.forwardRef)((r,s)=>{let{className:a,...l}=r;return(0,n.createElement)(u,{ref:s,iconNode:t,className:o("lucide-".concat(i(e)),a),...l})});return r.displayName="".concat(e),r}}}]);