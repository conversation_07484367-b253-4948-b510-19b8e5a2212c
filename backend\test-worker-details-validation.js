#!/usr/bin/env node

/**
 * 验证修复后的测试脚本的关键功能
 * 确保修复不会影响现有逻辑
 */

require('dotenv').config();

// 颜色输出函数
function log(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color] || ''}${message}${colors.reset}`);
}

/**
 * 验证修复的关键功能
 */
async function validateFixes() {
  log('cyan', '🔍 验证测试脚本修复');
  log('cyan', '========================');

  const issues = [];
  const successes = [];

  try {
    // 1. 验证依赖导入
    log('blue', '\n1. 验证依赖导入...');
    
    try {
      const { SocksProxyAgent } = require('socks-proxy-agent');
      successes.push('✅ SocksProxyAgent 导入成功');
    } catch (error) {
      issues.push(`❌ SocksProxyAgent 导入失败: ${error.message}`);
    }

    // 2. 验证配置适配器
    log('blue', '\n2. 验证配置适配器...');
    
    try {
      const { configAdapter } = require('./src/gateway/adapters/ConfigAdapter');
      const config = configAdapter.getConfig();
      
      if (config) {
        successes.push('✅ 配置适配器工作正常');
        console.log(`   网络模式: ${config.NETWORK_MODE}`);
        console.log(`   网关启用: ${config.ENABLE_SINGBOX_GATEWAY}`);
      } else {
        issues.push('❌ 配置适配器返回空配置');
      }
    } catch (error) {
      issues.push(`❌ 配置适配器失败: ${error.message}`);
    }

    // 3. 验证WorkerPoolController
    log('blue', '\n3. 验证WorkerPoolController...');
    
    try {
      const { WorkerPoolController } = require('./src/gateway/core/WorkerPoolController');
      
      if (typeof WorkerPoolController === 'function') {
        successes.push('✅ WorkerPoolController 导入成功');
      } else {
        issues.push('❌ WorkerPoolController 不是有效的构造函数');
      }
    } catch (error) {
      issues.push(`❌ WorkerPoolController 导入失败: ${error.message}`);
    }

    // 4. 验证修复后的函数
    log('blue', '\n4. 验证修复后的函数...');
    
    try {
      // 导入测试脚本中的函数
      const testModule = require('./test-worker-details.js');
      
      if (typeof testModule.getIpWithWorkerDetails === 'function') {
        successes.push('✅ getIpWithWorkerDetails 函数可用');
      } else {
        issues.push('❌ getIpWithWorkerDetails 函数不可用');
      }
      
      if (typeof testModule.runWorkerDetailsTests === 'function') {
        successes.push('✅ runWorkerDetailsTests 函数可用');
      } else {
        issues.push('❌ runWorkerDetailsTests 函数不可用');
      }
    } catch (error) {
      issues.push(`❌ 测试模块导入失败: ${error.message}`);
    }

    // 5. 验证fetch和AbortSignal支持
    log('blue', '\n5. 验证fetch和AbortSignal支持...');
    
    try {
      if (typeof fetch === 'function') {
        successes.push('✅ fetch API 可用');
      } else {
        issues.push('❌ fetch API 不可用');
      }
      
      if (typeof AbortSignal !== 'undefined' && typeof AbortSignal.timeout === 'function') {
        successes.push('✅ AbortSignal.timeout 可用');
      } else {
        issues.push('❌ AbortSignal.timeout 不可用（需要Node.js 16+）');
      }
    } catch (error) {
      issues.push(`❌ fetch/AbortSignal 验证失败: ${error.message}`);
    }

    // 6. 验证SOCKS代理创建
    log('blue', '\n6. 验证SOCKS代理创建...');
    
    try {
      const { SocksProxyAgent } = require('socks-proxy-agent');
      const testAgent = new SocksProxyAgent('socks5h://127.0.0.1:1081');
      
      if (testAgent) {
        successes.push('✅ SOCKS代理agent创建成功');
      } else {
        issues.push('❌ SOCKS代理agent创建失败');
      }
    } catch (error) {
      issues.push(`❌ SOCKS代理创建失败: ${error.message}`);
    }

  } catch (error) {
    issues.push(`❌ 验证过程出错: ${error.message}`);
  }

  // 输出结果
  log('cyan', '\n📊 验证结果');
  log('cyan', '============');

  if (successes.length > 0) {
    log('green', '\n✅ 成功项目:');
    successes.forEach(success => console.log(`   ${success}`));
  }

  if (issues.length > 0) {
    log('red', '\n❌ 问题项目:');
    issues.forEach(issue => console.log(`   ${issue}`));
  }

  const totalChecks = successes.length + issues.length;
  const successRate = ((successes.length / totalChecks) * 100).toFixed(1);

  console.log(`\n📈 总体结果:`);
  console.log(`   总检查项: ${totalChecks}`);
  console.log(`   成功项: ${successes.length}`);
  console.log(`   问题项: ${issues.length}`);
  console.log(`   成功率: ${successRate}%`);

  if (issues.length === 0) {
    log('green', '\n🎉 所有验证通过！测试脚本修复成功。');
    console.log('✅ 可以安全运行 node test-worker-details.js');
  } else if (issues.length < successes.length) {
    log('yellow', '\n⚠️  大部分验证通过，但有一些问题需要注意。');
  } else {
    log('red', '\n❌ 验证失败，请检查环境配置。');
  }

  return {
    totalChecks,
    successes: successes.length,
    issues: issues.length,
    successRate: parseFloat(successRate)
  };
}

// 运行验证
if (require.main === module) {
  validateFixes().catch(error => {
    log('red', '❌ 验证脚本执行失败:');
    console.error(error);
    process.exit(1);
  });
}

module.exports = { validateFixes };
