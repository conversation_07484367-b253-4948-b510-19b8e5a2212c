/**
 * 测试对话式TTS功能的脚本
 * 用于验证新实现的对话式TTS处理逻辑
 */

const WebSocket = require('ws');

// 测试配置
const TEST_CONFIG = {
  wsUrl: 'ws://localhost:3000/api/tts/ws/generate',
  token: 'your_test_token_here', // 需要替换为有效的测试token
  testDialogue: [
    {
      voice: 'Adam',
      text: '你好！今天天气真不错。'
    },
    {
      voice: 'Alice', 
      text: '是的，阳光明媚，很适合出去走走。'
    },
    {
      voice: 'Adam',
      text: '那我们一起去公园散步吧！'
    }
  ]
};

function testDialogueTTS() {
  return new Promise((resolve, reject) => {
    console.log('🚀 开始测试对话式TTS功能...');
    
    const ws = new WebSocket(TEST_CONFIG.wsUrl);
    let taskId = null;
    
    // 设置超时
    const timeout = setTimeout(() => {
      ws.close();
      reject(new Error('测试超时（60秒）'));
    }, 60000);
    
    ws.on('open', () => {
      console.log('✅ WebSocket连接已建立');
    });
    
    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data);
        console.log('📨 收到消息:', message);
        
        switch (message.type) {
          case 'initialized':
            taskId = message.taskId;
            console.log(`📋 任务已初始化，ID: ${taskId}`);
            
            // 发送对话式TTS请求
            const request = {
              action: 'start',
              token: TEST_CONFIG.token,
              taskType: 'dialogue',
              dialogue: TEST_CONFIG.testDialogue,
              model: 'eleven_turbo_v2',
              stability: 0.5,
              similarity_boost: 0.75
            };
            
            console.log('📤 发送对话式TTS请求...');
            ws.send(JSON.stringify(request));
            break;
            
          case 'progress':
            console.log(`⏳ 进度更新: ${message.message || JSON.stringify(message)}`);
            break;
            
          case 'complete':
            console.log('🎉 对话式TTS任务完成！');
            console.log(`📁 下载链接: ${message.downloadUrl}`);
            console.log(`📊 音频大小: ${message.audioSize} bytes`);
            clearTimeout(timeout);
            ws.close();
            resolve({
              taskId: message.taskId,
              downloadUrl: message.downloadUrl,
              audioSize: message.audioSize
            });
            break;
            
          case 'error':
            console.error('❌ 任务失败:', message.message);
            clearTimeout(timeout);
            ws.close();
            reject(new Error(message.message));
            break;
            
          default:
            console.log('📝 其他消息:', message);
        }
      } catch (error) {
        console.error('❌ 解析消息失败:', error);
      }
    });
    
    ws.on('error', (error) => {
      console.error('❌ WebSocket错误:', error);
      clearTimeout(timeout);
      reject(error);
    });
    
    ws.on('close', () => {
      console.log('🔌 WebSocket连接已关闭');
      clearTimeout(timeout);
    });
  });
}

function testSingleTTS() {
  return new Promise((resolve, reject) => {
    console.log('🚀 开始测试普通TTS功能...');
    
    const ws = new WebSocket(TEST_CONFIG.wsUrl);
    let taskId = null;
    
    // 设置超时
    const timeout = setTimeout(() => {
      ws.close();
      reject(new Error('测试超时（60秒）'));
    }, 60000);
    
    ws.on('open', () => {
      console.log('✅ WebSocket连接已建立');
    });
    
    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data);
        console.log('📨 收到消息:', message);
        
        switch (message.type) {
          case 'initialized':
            taskId = message.taskId;
            console.log(`📋 任务已初始化，ID: ${taskId}`);
            
            // 发送普通TTS请求
            const request = {
              action: 'start',
              token: TEST_CONFIG.token,
              input: '这是一个普通TTS测试，用于验证单人语音合成功能。',
              voice: 'Adam',
              model: 'eleven_turbo_v2',
              stability: 0.5,
              similarity_boost: 0.75
            };
            
            console.log('📤 发送普通TTS请求...');
            ws.send(JSON.stringify(request));
            break;
            
          case 'progress':
            console.log(`⏳ 进度更新: ${message.message || JSON.stringify(message)}`);
            break;
            
          case 'complete':
            console.log('🎉 普通TTS任务完成！');
            console.log(`📁 下载链接: ${message.downloadUrl}`);
            console.log(`📊 音频大小: ${message.audioSize} bytes`);
            clearTimeout(timeout);
            ws.close();
            resolve({
              taskId: message.taskId,
              downloadUrl: message.downloadUrl,
              audioSize: message.audioSize
            });
            break;
            
          case 'error':
            console.error('❌ 任务失败:', message.message);
            clearTimeout(timeout);
            ws.close();
            reject(new Error(message.message));
            break;
            
          default:
            console.log('📝 其他消息:', message);
        }
      } catch (error) {
        console.error('❌ 解析消息失败:', error);
      }
    });
    
    ws.on('error', (error) => {
      console.error('❌ WebSocket错误:', error);
      clearTimeout(timeout);
      reject(error);
    });
    
    ws.on('close', () => {
      console.log('🔌 WebSocket连接已关闭');
      clearTimeout(timeout);
    });
  });
}

// 主测试函数
async function runTests() {
  console.log('🧪 开始TTS功能测试...\n');
  
  try {
    // 测试普通TTS
    console.log('=== 测试1: 普通TTS ===');
    const singleResult = await testSingleTTS();
    console.log('✅ 普通TTS测试通过\n');
    
    // 等待一下再测试对话式TTS
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 测试对话式TTS
    console.log('=== 测试2: 对话式TTS ===');
    const dialogueResult = await testDialogueTTS();
    console.log('✅ 对话式TTS测试通过\n');
    
    console.log('🎉 所有测试通过！');
    console.log('测试结果:');
    console.log('- 普通TTS:', singleResult);
    console.log('- 对话式TTS:', dialogueResult);
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  console.log('⚠️  请确保：');
  console.log('1. 服务器正在运行 (localhost:3000)');
  console.log('2. 已设置有效的测试token');
  console.log('3. 数据库中有语音映射数据');
  console.log('4. 用户有相应的VIP权限\n');
  
  runTests();
}

module.exports = {
  testSingleTTS,
  testDialogueTTS,
  runTests
};
