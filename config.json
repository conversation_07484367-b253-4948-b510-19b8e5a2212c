{"log": {"level": "debug", "timestamp": true}, "experimental": {"clash_api": {"external_controller": "127.0.0.1:9090", "secret": ""}}, "dns": {"servers": [{"tag": "ali-dns", "address": "*********", "detour": "direct-dns"}, {"tag": "google-dns", "address": "*******", "detour": "direct-dns"}], "strategy": "ipv4_only"}, "inbounds": [{"type": "socks", "tag": "worker-in-1", "listen": "127.0.0.1", "listen_port": 1081}, {"type": "socks", "tag": "worker-in-2", "listen": "127.0.0.1", "listen_port": 1082}, {"type": "socks", "tag": "worker-in-3", "listen": "127.0.0.1", "listen_port": 1083}, {"type": "socks", "tag": "worker-in-4", "listen": "127.0.0.1", "listen_port": 1084}, {"type": "socks", "tag": "worker-in-5", "listen": "127.0.0.1", "listen_port": 1085}, {"type": "socks", "tag": "worker-in-6", "listen": "127.0.0.1", "listen_port": 1086}, {"type": "socks", "tag": "worker-in-7", "listen": "127.0.0.1", "listen_port": 1087}, {"type": "socks", "tag": "worker-in-8", "listen": "127.0.0.1", "listen_port": 1088}, {"type": "socks", "tag": "worker-in-9", "listen": "127.0.0.1", "listen_port": 1089}, {"type": "socks", "tag": "worker-in-10", "listen": "127.0.0.1", "listen_port": 1090}], "outbounds": [{"type": "direct", "tag": "direct-dns"}, {"type": "selector", "tag": "worker-selector-1", "outbounds": ["jp-awsjp-01-idx-0", "jp-awsjp-02-idx-1", "jp-awsjp-03-idx-2", "jp-awsjp-04-idx-3", "jp-awsjp-05-idx-4", "sg-awssg-01-idx-5", "sg-awssg-02-idx-6", "sg-awssg-03-idx-7", "sg-awssg-04-idx-8", "sg-awssg-05-idx-9", "us-us-01-0-1-hy2-idx-10", "us-us-02-0-1-hy2-idx-11", "us-us-03-0-1-hy2-idx-12", "us-us-04-0-1-hy2-idx-13", "us-us-05-0-1-hy2-idx-14", "us-us-06-0-1-hy2-idx-15", "us-us-07-0-1-hy2-idx-16", "us-us-08-0-1-hy2-idx-17", "us-us-09-0-1-hy2-idx-18", "us-us-10-0-1-hy2-idx-19", "us-us-11-0-1-hy2-idx-20", "us-us-12-0-1-hy2-idx-21", "us-us-13-0-1-hy2-idx-22", "us-us-14-0-1-hy2-idx-23", "us-us-15-0-1-hy2-idx-24", "us-us-02-hy2-idx-25", "us-us-03-hy2-idx-26", "us-us-04-hy2-idx-27", "us-us-05-hy2-idx-28", "us-us-06-hy2-idx-29", "kr-kr-hy2-idx-30", "kr-kr-2-hy2-idx-31", "kr-kr-3-hy2-idx-32", "kr-kr-4-hy2-idx-33", "kr-kr-5-hy2-idx-34", "ae-ae-hy2-idx-35", "de-de-hy2-idx-36", "de-de-2-hy2-idx-37", "ch-ch-hy2-idx-38", "au-au-hy2-idx-39", "au-au-2-hy2-idx-40", "au-au-3-hy2-idx-41", "sg-sg-hy2-idx-42", "sg-sg-2-hy2-idx-43", "sg-sg-3-hy2-idx-44", "sg-sg-4-hy2-idx-45", "sg-sg-5-hy2-idx-46", "sg-sg-6-hy2-idx-47", "us-us-hy2-idx-48", "us-us-01-0-1-idx-49", "us-us-02-0-1-idx-50", "us-us-03-0-1-idx-51", "us-us-04-0-1-idx-52", "us-us-05-0-1-idx-53", "us-us-06-0-1-idx-54", "kr-kr-01-idx-55", "hk-hkthk-01-idx-56", "hk-hkthk-02-idx-57", "hk-hkthk-03-idx-58", "hk-hkthk-04-idx-59", "hk-hkthk-05-idx-60", "tw-tw-idx-61", "tw-tw-2-idx-62", "tw-tw-3-idx-63", "jp-jp-01-0-1-idx-64", "in-in-01-0-1-idx-65", "us-us-01-0-1-idx-66", "us-us-02-0-1-idx-67", "us-us-03-0-1-idx-68", "us-us-04-0-1-idx-69", "us-us-05-0-1-idx-70", "us-us-01-0-01-idx-71", "us-us-02-0-01-idx-72", "us-us-03-0-01-idx-73", "us-us-04-0-01-idx-74", "us-us-05-0-01-idx-75", "us-us-01-idx-76", "us-us-05-idx-77", "sg-sg-idx-78", "sg-sg-2-idx-79", "sg-sg-3-idx-80", "sg-sg-4-idx-81", "jp-jp-idx-82"], "default": "jp-awsjp-01-idx-0"}, {"type": "selector", "tag": "worker-selector-2", "outbounds": ["jp-awsjp-01-idx-0", "jp-awsjp-02-idx-1", "jp-awsjp-03-idx-2", "jp-awsjp-04-idx-3", "jp-awsjp-05-idx-4", "sg-awssg-01-idx-5", "sg-awssg-02-idx-6", "sg-awssg-03-idx-7", "sg-awssg-04-idx-8", "sg-awssg-05-idx-9", "us-us-01-0-1-hy2-idx-10", "us-us-02-0-1-hy2-idx-11", "us-us-03-0-1-hy2-idx-12", "us-us-04-0-1-hy2-idx-13", "us-us-05-0-1-hy2-idx-14", "us-us-06-0-1-hy2-idx-15", "us-us-07-0-1-hy2-idx-16", "us-us-08-0-1-hy2-idx-17", "us-us-09-0-1-hy2-idx-18", "us-us-10-0-1-hy2-idx-19", "us-us-11-0-1-hy2-idx-20", "us-us-12-0-1-hy2-idx-21", "us-us-13-0-1-hy2-idx-22", "us-us-14-0-1-hy2-idx-23", "us-us-15-0-1-hy2-idx-24", "us-us-02-hy2-idx-25", "us-us-03-hy2-idx-26", "us-us-04-hy2-idx-27", "us-us-05-hy2-idx-28", "us-us-06-hy2-idx-29", "kr-kr-hy2-idx-30", "kr-kr-2-hy2-idx-31", "kr-kr-3-hy2-idx-32", "kr-kr-4-hy2-idx-33", "kr-kr-5-hy2-idx-34", "ae-ae-hy2-idx-35", "de-de-hy2-idx-36", "de-de-2-hy2-idx-37", "ch-ch-hy2-idx-38", "au-au-hy2-idx-39", "au-au-2-hy2-idx-40", "au-au-3-hy2-idx-41", "sg-sg-hy2-idx-42", "sg-sg-2-hy2-idx-43", "sg-sg-3-hy2-idx-44", "sg-sg-4-hy2-idx-45", "sg-sg-5-hy2-idx-46", "sg-sg-6-hy2-idx-47", "us-us-hy2-idx-48", "us-us-01-0-1-idx-49", "us-us-02-0-1-idx-50", "us-us-03-0-1-idx-51", "us-us-04-0-1-idx-52", "us-us-05-0-1-idx-53", "us-us-06-0-1-idx-54", "kr-kr-01-idx-55", "hk-hkthk-01-idx-56", "hk-hkthk-02-idx-57", "hk-hkthk-03-idx-58", "hk-hkthk-04-idx-59", "hk-hkthk-05-idx-60", "tw-tw-idx-61", "tw-tw-2-idx-62", "tw-tw-3-idx-63", "jp-jp-01-0-1-idx-64", "in-in-01-0-1-idx-65", "us-us-01-0-1-idx-66", "us-us-02-0-1-idx-67", "us-us-03-0-1-idx-68", "us-us-04-0-1-idx-69", "us-us-05-0-1-idx-70", "us-us-01-0-01-idx-71", "us-us-02-0-01-idx-72", "us-us-03-0-01-idx-73", "us-us-04-0-01-idx-74", "us-us-05-0-01-idx-75", "us-us-01-idx-76", "us-us-05-idx-77", "sg-sg-idx-78", "sg-sg-2-idx-79", "sg-sg-3-idx-80", "sg-sg-4-idx-81", "jp-jp-idx-82"], "default": "jp-awsjp-01-idx-0"}, {"type": "selector", "tag": "worker-selector-3", "outbounds": ["jp-awsjp-01-idx-0", "jp-awsjp-02-idx-1", "jp-awsjp-03-idx-2", "jp-awsjp-04-idx-3", "jp-awsjp-05-idx-4", "sg-awssg-01-idx-5", "sg-awssg-02-idx-6", "sg-awssg-03-idx-7", "sg-awssg-04-idx-8", "sg-awssg-05-idx-9", "us-us-01-0-1-hy2-idx-10", "us-us-02-0-1-hy2-idx-11", "us-us-03-0-1-hy2-idx-12", "us-us-04-0-1-hy2-idx-13", "us-us-05-0-1-hy2-idx-14", "us-us-06-0-1-hy2-idx-15", "us-us-07-0-1-hy2-idx-16", "us-us-08-0-1-hy2-idx-17", "us-us-09-0-1-hy2-idx-18", "us-us-10-0-1-hy2-idx-19", "us-us-11-0-1-hy2-idx-20", "us-us-12-0-1-hy2-idx-21", "us-us-13-0-1-hy2-idx-22", "us-us-14-0-1-hy2-idx-23", "us-us-15-0-1-hy2-idx-24", "us-us-02-hy2-idx-25", "us-us-03-hy2-idx-26", "us-us-04-hy2-idx-27", "us-us-05-hy2-idx-28", "us-us-06-hy2-idx-29", "kr-kr-hy2-idx-30", "kr-kr-2-hy2-idx-31", "kr-kr-3-hy2-idx-32", "kr-kr-4-hy2-idx-33", "kr-kr-5-hy2-idx-34", "ae-ae-hy2-idx-35", "de-de-hy2-idx-36", "de-de-2-hy2-idx-37", "ch-ch-hy2-idx-38", "au-au-hy2-idx-39", "au-au-2-hy2-idx-40", "au-au-3-hy2-idx-41", "sg-sg-hy2-idx-42", "sg-sg-2-hy2-idx-43", "sg-sg-3-hy2-idx-44", "sg-sg-4-hy2-idx-45", "sg-sg-5-hy2-idx-46", "sg-sg-6-hy2-idx-47", "us-us-hy2-idx-48", "us-us-01-0-1-idx-49", "us-us-02-0-1-idx-50", "us-us-03-0-1-idx-51", "us-us-04-0-1-idx-52", "us-us-05-0-1-idx-53", "us-us-06-0-1-idx-54", "kr-kr-01-idx-55", "hk-hkthk-01-idx-56", "hk-hkthk-02-idx-57", "hk-hkthk-03-idx-58", "hk-hkthk-04-idx-59", "hk-hkthk-05-idx-60", "tw-tw-idx-61", "tw-tw-2-idx-62", "tw-tw-3-idx-63", "jp-jp-01-0-1-idx-64", "in-in-01-0-1-idx-65", "us-us-01-0-1-idx-66", "us-us-02-0-1-idx-67", "us-us-03-0-1-idx-68", "us-us-04-0-1-idx-69", "us-us-05-0-1-idx-70", "us-us-01-0-01-idx-71", "us-us-02-0-01-idx-72", "us-us-03-0-01-idx-73", "us-us-04-0-01-idx-74", "us-us-05-0-01-idx-75", "us-us-01-idx-76", "us-us-05-idx-77", "sg-sg-idx-78", "sg-sg-2-idx-79", "sg-sg-3-idx-80", "sg-sg-4-idx-81", "jp-jp-idx-82"], "default": "jp-awsjp-01-idx-0"}, {"type": "selector", "tag": "worker-selector-4", "outbounds": ["jp-awsjp-01-idx-0", "jp-awsjp-02-idx-1", "jp-awsjp-03-idx-2", "jp-awsjp-04-idx-3", "jp-awsjp-05-idx-4", "sg-awssg-01-idx-5", "sg-awssg-02-idx-6", "sg-awssg-03-idx-7", "sg-awssg-04-idx-8", "sg-awssg-05-idx-9", "us-us-01-0-1-hy2-idx-10", "us-us-02-0-1-hy2-idx-11", "us-us-03-0-1-hy2-idx-12", "us-us-04-0-1-hy2-idx-13", "us-us-05-0-1-hy2-idx-14", "us-us-06-0-1-hy2-idx-15", "us-us-07-0-1-hy2-idx-16", "us-us-08-0-1-hy2-idx-17", "us-us-09-0-1-hy2-idx-18", "us-us-10-0-1-hy2-idx-19", "us-us-11-0-1-hy2-idx-20", "us-us-12-0-1-hy2-idx-21", "us-us-13-0-1-hy2-idx-22", "us-us-14-0-1-hy2-idx-23", "us-us-15-0-1-hy2-idx-24", "us-us-02-hy2-idx-25", "us-us-03-hy2-idx-26", "us-us-04-hy2-idx-27", "us-us-05-hy2-idx-28", "us-us-06-hy2-idx-29", "kr-kr-hy2-idx-30", "kr-kr-2-hy2-idx-31", "kr-kr-3-hy2-idx-32", "kr-kr-4-hy2-idx-33", "kr-kr-5-hy2-idx-34", "ae-ae-hy2-idx-35", "de-de-hy2-idx-36", "de-de-2-hy2-idx-37", "ch-ch-hy2-idx-38", "au-au-hy2-idx-39", "au-au-2-hy2-idx-40", "au-au-3-hy2-idx-41", "sg-sg-hy2-idx-42", "sg-sg-2-hy2-idx-43", "sg-sg-3-hy2-idx-44", "sg-sg-4-hy2-idx-45", "sg-sg-5-hy2-idx-46", "sg-sg-6-hy2-idx-47", "us-us-hy2-idx-48", "us-us-01-0-1-idx-49", "us-us-02-0-1-idx-50", "us-us-03-0-1-idx-51", "us-us-04-0-1-idx-52", "us-us-05-0-1-idx-53", "us-us-06-0-1-idx-54", "kr-kr-01-idx-55", "hk-hkthk-01-idx-56", "hk-hkthk-02-idx-57", "hk-hkthk-03-idx-58", "hk-hkthk-04-idx-59", "hk-hkthk-05-idx-60", "tw-tw-idx-61", "tw-tw-2-idx-62", "tw-tw-3-idx-63", "jp-jp-01-0-1-idx-64", "in-in-01-0-1-idx-65", "us-us-01-0-1-idx-66", "us-us-02-0-1-idx-67", "us-us-03-0-1-idx-68", "us-us-04-0-1-idx-69", "us-us-05-0-1-idx-70", "us-us-01-0-01-idx-71", "us-us-02-0-01-idx-72", "us-us-03-0-01-idx-73", "us-us-04-0-01-idx-74", "us-us-05-0-01-idx-75", "us-us-01-idx-76", "us-us-05-idx-77", "sg-sg-idx-78", "sg-sg-2-idx-79", "sg-sg-3-idx-80", "sg-sg-4-idx-81", "jp-jp-idx-82"], "default": "jp-awsjp-01-idx-0"}, {"type": "selector", "tag": "worker-selector-5", "outbounds": ["jp-awsjp-01-idx-0", "jp-awsjp-02-idx-1", "jp-awsjp-03-idx-2", "jp-awsjp-04-idx-3", "jp-awsjp-05-idx-4", "sg-awssg-01-idx-5", "sg-awssg-02-idx-6", "sg-awssg-03-idx-7", "sg-awssg-04-idx-8", "sg-awssg-05-idx-9", "us-us-01-0-1-hy2-idx-10", "us-us-02-0-1-hy2-idx-11", "us-us-03-0-1-hy2-idx-12", "us-us-04-0-1-hy2-idx-13", "us-us-05-0-1-hy2-idx-14", "us-us-06-0-1-hy2-idx-15", "us-us-07-0-1-hy2-idx-16", "us-us-08-0-1-hy2-idx-17", "us-us-09-0-1-hy2-idx-18", "us-us-10-0-1-hy2-idx-19", "us-us-11-0-1-hy2-idx-20", "us-us-12-0-1-hy2-idx-21", "us-us-13-0-1-hy2-idx-22", "us-us-14-0-1-hy2-idx-23", "us-us-15-0-1-hy2-idx-24", "us-us-02-hy2-idx-25", "us-us-03-hy2-idx-26", "us-us-04-hy2-idx-27", "us-us-05-hy2-idx-28", "us-us-06-hy2-idx-29", "kr-kr-hy2-idx-30", "kr-kr-2-hy2-idx-31", "kr-kr-3-hy2-idx-32", "kr-kr-4-hy2-idx-33", "kr-kr-5-hy2-idx-34", "ae-ae-hy2-idx-35", "de-de-hy2-idx-36", "de-de-2-hy2-idx-37", "ch-ch-hy2-idx-38", "au-au-hy2-idx-39", "au-au-2-hy2-idx-40", "au-au-3-hy2-idx-41", "sg-sg-hy2-idx-42", "sg-sg-2-hy2-idx-43", "sg-sg-3-hy2-idx-44", "sg-sg-4-hy2-idx-45", "sg-sg-5-hy2-idx-46", "sg-sg-6-hy2-idx-47", "us-us-hy2-idx-48", "us-us-01-0-1-idx-49", "us-us-02-0-1-idx-50", "us-us-03-0-1-idx-51", "us-us-04-0-1-idx-52", "us-us-05-0-1-idx-53", "us-us-06-0-1-idx-54", "kr-kr-01-idx-55", "hk-hkthk-01-idx-56", "hk-hkthk-02-idx-57", "hk-hkthk-03-idx-58", "hk-hkthk-04-idx-59", "hk-hkthk-05-idx-60", "tw-tw-idx-61", "tw-tw-2-idx-62", "tw-tw-3-idx-63", "jp-jp-01-0-1-idx-64", "in-in-01-0-1-idx-65", "us-us-01-0-1-idx-66", "us-us-02-0-1-idx-67", "us-us-03-0-1-idx-68", "us-us-04-0-1-idx-69", "us-us-05-0-1-idx-70", "us-us-01-0-01-idx-71", "us-us-02-0-01-idx-72", "us-us-03-0-01-idx-73", "us-us-04-0-01-idx-74", "us-us-05-0-01-idx-75", "us-us-01-idx-76", "us-us-05-idx-77", "sg-sg-idx-78", "sg-sg-2-idx-79", "sg-sg-3-idx-80", "sg-sg-4-idx-81", "jp-jp-idx-82"], "default": "jp-awsjp-01-idx-0"}, {"type": "selector", "tag": "worker-selector-6", "outbounds": ["jp-awsjp-01-idx-0", "jp-awsjp-02-idx-1", "jp-awsjp-03-idx-2", "jp-awsjp-04-idx-3", "jp-awsjp-05-idx-4", "sg-awssg-01-idx-5", "sg-awssg-02-idx-6", "sg-awssg-03-idx-7", "sg-awssg-04-idx-8", "sg-awssg-05-idx-9", "us-us-01-0-1-hy2-idx-10", "us-us-02-0-1-hy2-idx-11", "us-us-03-0-1-hy2-idx-12", "us-us-04-0-1-hy2-idx-13", "us-us-05-0-1-hy2-idx-14", "us-us-06-0-1-hy2-idx-15", "us-us-07-0-1-hy2-idx-16", "us-us-08-0-1-hy2-idx-17", "us-us-09-0-1-hy2-idx-18", "us-us-10-0-1-hy2-idx-19", "us-us-11-0-1-hy2-idx-20", "us-us-12-0-1-hy2-idx-21", "us-us-13-0-1-hy2-idx-22", "us-us-14-0-1-hy2-idx-23", "us-us-15-0-1-hy2-idx-24", "us-us-02-hy2-idx-25", "us-us-03-hy2-idx-26", "us-us-04-hy2-idx-27", "us-us-05-hy2-idx-28", "us-us-06-hy2-idx-29", "kr-kr-hy2-idx-30", "kr-kr-2-hy2-idx-31", "kr-kr-3-hy2-idx-32", "kr-kr-4-hy2-idx-33", "kr-kr-5-hy2-idx-34", "ae-ae-hy2-idx-35", "de-de-hy2-idx-36", "de-de-2-hy2-idx-37", "ch-ch-hy2-idx-38", "au-au-hy2-idx-39", "au-au-2-hy2-idx-40", "au-au-3-hy2-idx-41", "sg-sg-hy2-idx-42", "sg-sg-2-hy2-idx-43", "sg-sg-3-hy2-idx-44", "sg-sg-4-hy2-idx-45", "sg-sg-5-hy2-idx-46", "sg-sg-6-hy2-idx-47", "us-us-hy2-idx-48", "us-us-01-0-1-idx-49", "us-us-02-0-1-idx-50", "us-us-03-0-1-idx-51", "us-us-04-0-1-idx-52", "us-us-05-0-1-idx-53", "us-us-06-0-1-idx-54", "kr-kr-01-idx-55", "hk-hkthk-01-idx-56", "hk-hkthk-02-idx-57", "hk-hkthk-03-idx-58", "hk-hkthk-04-idx-59", "hk-hkthk-05-idx-60", "tw-tw-idx-61", "tw-tw-2-idx-62", "tw-tw-3-idx-63", "jp-jp-01-0-1-idx-64", "in-in-01-0-1-idx-65", "us-us-01-0-1-idx-66", "us-us-02-0-1-idx-67", "us-us-03-0-1-idx-68", "us-us-04-0-1-idx-69", "us-us-05-0-1-idx-70", "us-us-01-0-01-idx-71", "us-us-02-0-01-idx-72", "us-us-03-0-01-idx-73", "us-us-04-0-01-idx-74", "us-us-05-0-01-idx-75", "us-us-01-idx-76", "us-us-05-idx-77", "sg-sg-idx-78", "sg-sg-2-idx-79", "sg-sg-3-idx-80", "sg-sg-4-idx-81", "jp-jp-idx-82"], "default": "jp-awsjp-01-idx-0"}, {"type": "selector", "tag": "worker-selector-7", "outbounds": ["jp-awsjp-01-idx-0", "jp-awsjp-02-idx-1", "jp-awsjp-03-idx-2", "jp-awsjp-04-idx-3", "jp-awsjp-05-idx-4", "sg-awssg-01-idx-5", "sg-awssg-02-idx-6", "sg-awssg-03-idx-7", "sg-awssg-04-idx-8", "sg-awssg-05-idx-9", "us-us-01-0-1-hy2-idx-10", "us-us-02-0-1-hy2-idx-11", "us-us-03-0-1-hy2-idx-12", "us-us-04-0-1-hy2-idx-13", "us-us-05-0-1-hy2-idx-14", "us-us-06-0-1-hy2-idx-15", "us-us-07-0-1-hy2-idx-16", "us-us-08-0-1-hy2-idx-17", "us-us-09-0-1-hy2-idx-18", "us-us-10-0-1-hy2-idx-19", "us-us-11-0-1-hy2-idx-20", "us-us-12-0-1-hy2-idx-21", "us-us-13-0-1-hy2-idx-22", "us-us-14-0-1-hy2-idx-23", "us-us-15-0-1-hy2-idx-24", "us-us-02-hy2-idx-25", "us-us-03-hy2-idx-26", "us-us-04-hy2-idx-27", "us-us-05-hy2-idx-28", "us-us-06-hy2-idx-29", "kr-kr-hy2-idx-30", "kr-kr-2-hy2-idx-31", "kr-kr-3-hy2-idx-32", "kr-kr-4-hy2-idx-33", "kr-kr-5-hy2-idx-34", "ae-ae-hy2-idx-35", "de-de-hy2-idx-36", "de-de-2-hy2-idx-37", "ch-ch-hy2-idx-38", "au-au-hy2-idx-39", "au-au-2-hy2-idx-40", "au-au-3-hy2-idx-41", "sg-sg-hy2-idx-42", "sg-sg-2-hy2-idx-43", "sg-sg-3-hy2-idx-44", "sg-sg-4-hy2-idx-45", "sg-sg-5-hy2-idx-46", "sg-sg-6-hy2-idx-47", "us-us-hy2-idx-48", "us-us-01-0-1-idx-49", "us-us-02-0-1-idx-50", "us-us-03-0-1-idx-51", "us-us-04-0-1-idx-52", "us-us-05-0-1-idx-53", "us-us-06-0-1-idx-54", "kr-kr-01-idx-55", "hk-hkthk-01-idx-56", "hk-hkthk-02-idx-57", "hk-hkthk-03-idx-58", "hk-hkthk-04-idx-59", "hk-hkthk-05-idx-60", "tw-tw-idx-61", "tw-tw-2-idx-62", "tw-tw-3-idx-63", "jp-jp-01-0-1-idx-64", "in-in-01-0-1-idx-65", "us-us-01-0-1-idx-66", "us-us-02-0-1-idx-67", "us-us-03-0-1-idx-68", "us-us-04-0-1-idx-69", "us-us-05-0-1-idx-70", "us-us-01-0-01-idx-71", "us-us-02-0-01-idx-72", "us-us-03-0-01-idx-73", "us-us-04-0-01-idx-74", "us-us-05-0-01-idx-75", "us-us-01-idx-76", "us-us-05-idx-77", "sg-sg-idx-78", "sg-sg-2-idx-79", "sg-sg-3-idx-80", "sg-sg-4-idx-81", "jp-jp-idx-82"], "default": "jp-awsjp-01-idx-0"}, {"type": "selector", "tag": "worker-selector-8", "outbounds": ["jp-awsjp-01-idx-0", "jp-awsjp-02-idx-1", "jp-awsjp-03-idx-2", "jp-awsjp-04-idx-3", "jp-awsjp-05-idx-4", "sg-awssg-01-idx-5", "sg-awssg-02-idx-6", "sg-awssg-03-idx-7", "sg-awssg-04-idx-8", "sg-awssg-05-idx-9", "us-us-01-0-1-hy2-idx-10", "us-us-02-0-1-hy2-idx-11", "us-us-03-0-1-hy2-idx-12", "us-us-04-0-1-hy2-idx-13", "us-us-05-0-1-hy2-idx-14", "us-us-06-0-1-hy2-idx-15", "us-us-07-0-1-hy2-idx-16", "us-us-08-0-1-hy2-idx-17", "us-us-09-0-1-hy2-idx-18", "us-us-10-0-1-hy2-idx-19", "us-us-11-0-1-hy2-idx-20", "us-us-12-0-1-hy2-idx-21", "us-us-13-0-1-hy2-idx-22", "us-us-14-0-1-hy2-idx-23", "us-us-15-0-1-hy2-idx-24", "us-us-02-hy2-idx-25", "us-us-03-hy2-idx-26", "us-us-04-hy2-idx-27", "us-us-05-hy2-idx-28", "us-us-06-hy2-idx-29", "kr-kr-hy2-idx-30", "kr-kr-2-hy2-idx-31", "kr-kr-3-hy2-idx-32", "kr-kr-4-hy2-idx-33", "kr-kr-5-hy2-idx-34", "ae-ae-hy2-idx-35", "de-de-hy2-idx-36", "de-de-2-hy2-idx-37", "ch-ch-hy2-idx-38", "au-au-hy2-idx-39", "au-au-2-hy2-idx-40", "au-au-3-hy2-idx-41", "sg-sg-hy2-idx-42", "sg-sg-2-hy2-idx-43", "sg-sg-3-hy2-idx-44", "sg-sg-4-hy2-idx-45", "sg-sg-5-hy2-idx-46", "sg-sg-6-hy2-idx-47", "us-us-hy2-idx-48", "us-us-01-0-1-idx-49", "us-us-02-0-1-idx-50", "us-us-03-0-1-idx-51", "us-us-04-0-1-idx-52", "us-us-05-0-1-idx-53", "us-us-06-0-1-idx-54", "kr-kr-01-idx-55", "hk-hkthk-01-idx-56", "hk-hkthk-02-idx-57", "hk-hkthk-03-idx-58", "hk-hkthk-04-idx-59", "hk-hkthk-05-idx-60", "tw-tw-idx-61", "tw-tw-2-idx-62", "tw-tw-3-idx-63", "jp-jp-01-0-1-idx-64", "in-in-01-0-1-idx-65", "us-us-01-0-1-idx-66", "us-us-02-0-1-idx-67", "us-us-03-0-1-idx-68", "us-us-04-0-1-idx-69", "us-us-05-0-1-idx-70", "us-us-01-0-01-idx-71", "us-us-02-0-01-idx-72", "us-us-03-0-01-idx-73", "us-us-04-0-01-idx-74", "us-us-05-0-01-idx-75", "us-us-01-idx-76", "us-us-05-idx-77", "sg-sg-idx-78", "sg-sg-2-idx-79", "sg-sg-3-idx-80", "sg-sg-4-idx-81", "jp-jp-idx-82"], "default": "jp-awsjp-01-idx-0"}, {"type": "selector", "tag": "worker-selector-9", "outbounds": ["jp-awsjp-01-idx-0", "jp-awsjp-02-idx-1", "jp-awsjp-03-idx-2", "jp-awsjp-04-idx-3", "jp-awsjp-05-idx-4", "sg-awssg-01-idx-5", "sg-awssg-02-idx-6", "sg-awssg-03-idx-7", "sg-awssg-04-idx-8", "sg-awssg-05-idx-9", "us-us-01-0-1-hy2-idx-10", "us-us-02-0-1-hy2-idx-11", "us-us-03-0-1-hy2-idx-12", "us-us-04-0-1-hy2-idx-13", "us-us-05-0-1-hy2-idx-14", "us-us-06-0-1-hy2-idx-15", "us-us-07-0-1-hy2-idx-16", "us-us-08-0-1-hy2-idx-17", "us-us-09-0-1-hy2-idx-18", "us-us-10-0-1-hy2-idx-19", "us-us-11-0-1-hy2-idx-20", "us-us-12-0-1-hy2-idx-21", "us-us-13-0-1-hy2-idx-22", "us-us-14-0-1-hy2-idx-23", "us-us-15-0-1-hy2-idx-24", "us-us-02-hy2-idx-25", "us-us-03-hy2-idx-26", "us-us-04-hy2-idx-27", "us-us-05-hy2-idx-28", "us-us-06-hy2-idx-29", "kr-kr-hy2-idx-30", "kr-kr-2-hy2-idx-31", "kr-kr-3-hy2-idx-32", "kr-kr-4-hy2-idx-33", "kr-kr-5-hy2-idx-34", "ae-ae-hy2-idx-35", "de-de-hy2-idx-36", "de-de-2-hy2-idx-37", "ch-ch-hy2-idx-38", "au-au-hy2-idx-39", "au-au-2-hy2-idx-40", "au-au-3-hy2-idx-41", "sg-sg-hy2-idx-42", "sg-sg-2-hy2-idx-43", "sg-sg-3-hy2-idx-44", "sg-sg-4-hy2-idx-45", "sg-sg-5-hy2-idx-46", "sg-sg-6-hy2-idx-47", "us-us-hy2-idx-48", "us-us-01-0-1-idx-49", "us-us-02-0-1-idx-50", "us-us-03-0-1-idx-51", "us-us-04-0-1-idx-52", "us-us-05-0-1-idx-53", "us-us-06-0-1-idx-54", "kr-kr-01-idx-55", "hk-hkthk-01-idx-56", "hk-hkthk-02-idx-57", "hk-hkthk-03-idx-58", "hk-hkthk-04-idx-59", "hk-hkthk-05-idx-60", "tw-tw-idx-61", "tw-tw-2-idx-62", "tw-tw-3-idx-63", "jp-jp-01-0-1-idx-64", "in-in-01-0-1-idx-65", "us-us-01-0-1-idx-66", "us-us-02-0-1-idx-67", "us-us-03-0-1-idx-68", "us-us-04-0-1-idx-69", "us-us-05-0-1-idx-70", "us-us-01-0-01-idx-71", "us-us-02-0-01-idx-72", "us-us-03-0-01-idx-73", "us-us-04-0-01-idx-74", "us-us-05-0-01-idx-75", "us-us-01-idx-76", "us-us-05-idx-77", "sg-sg-idx-78", "sg-sg-2-idx-79", "sg-sg-3-idx-80", "sg-sg-4-idx-81", "jp-jp-idx-82"], "default": "jp-awsjp-01-idx-0"}, {"type": "selector", "tag": "worker-selector-10", "outbounds": ["jp-awsjp-01-idx-0", "jp-awsjp-02-idx-1", "jp-awsjp-03-idx-2", "jp-awsjp-04-idx-3", "jp-awsjp-05-idx-4", "sg-awssg-01-idx-5", "sg-awssg-02-idx-6", "sg-awssg-03-idx-7", "sg-awssg-04-idx-8", "sg-awssg-05-idx-9", "us-us-01-0-1-hy2-idx-10", "us-us-02-0-1-hy2-idx-11", "us-us-03-0-1-hy2-idx-12", "us-us-04-0-1-hy2-idx-13", "us-us-05-0-1-hy2-idx-14", "us-us-06-0-1-hy2-idx-15", "us-us-07-0-1-hy2-idx-16", "us-us-08-0-1-hy2-idx-17", "us-us-09-0-1-hy2-idx-18", "us-us-10-0-1-hy2-idx-19", "us-us-11-0-1-hy2-idx-20", "us-us-12-0-1-hy2-idx-21", "us-us-13-0-1-hy2-idx-22", "us-us-14-0-1-hy2-idx-23", "us-us-15-0-1-hy2-idx-24", "us-us-02-hy2-idx-25", "us-us-03-hy2-idx-26", "us-us-04-hy2-idx-27", "us-us-05-hy2-idx-28", "us-us-06-hy2-idx-29", "kr-kr-hy2-idx-30", "kr-kr-2-hy2-idx-31", "kr-kr-3-hy2-idx-32", "kr-kr-4-hy2-idx-33", "kr-kr-5-hy2-idx-34", "ae-ae-hy2-idx-35", "de-de-hy2-idx-36", "de-de-2-hy2-idx-37", "ch-ch-hy2-idx-38", "au-au-hy2-idx-39", "au-au-2-hy2-idx-40", "au-au-3-hy2-idx-41", "sg-sg-hy2-idx-42", "sg-sg-2-hy2-idx-43", "sg-sg-3-hy2-idx-44", "sg-sg-4-hy2-idx-45", "sg-sg-5-hy2-idx-46", "sg-sg-6-hy2-idx-47", "us-us-hy2-idx-48", "us-us-01-0-1-idx-49", "us-us-02-0-1-idx-50", "us-us-03-0-1-idx-51", "us-us-04-0-1-idx-52", "us-us-05-0-1-idx-53", "us-us-06-0-1-idx-54", "kr-kr-01-idx-55", "hk-hkthk-01-idx-56", "hk-hkthk-02-idx-57", "hk-hkthk-03-idx-58", "hk-hkthk-04-idx-59", "hk-hkthk-05-idx-60", "tw-tw-idx-61", "tw-tw-2-idx-62", "tw-tw-3-idx-63", "jp-jp-01-0-1-idx-64", "in-in-01-0-1-idx-65", "us-us-01-0-1-idx-66", "us-us-02-0-1-idx-67", "us-us-03-0-1-idx-68", "us-us-04-0-1-idx-69", "us-us-05-0-1-idx-70", "us-us-01-0-01-idx-71", "us-us-02-0-01-idx-72", "us-us-03-0-01-idx-73", "us-us-04-0-01-idx-74", "us-us-05-0-01-idx-75", "us-us-01-idx-76", "us-us-05-idx-77", "sg-sg-idx-78", "sg-sg-2-idx-79", "sg-sg-3-idx-80", "sg-sg-4-idx-81", "jp-jp-idx-82"], "default": "jp-awsjp-01-idx-0"}, {"tag": "jp-awsjp-01-idx-0", "type": "vless", "server": "pq.aws48.yydjc.top", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "xtls-rprx-vision", "tls": {"enabled": true, "insecure": false, "server_name": "buylite.tv.apple.com", "utls": {"enabled": true, "fingerprint": "chrome"}, "reality": {"enabled": true, "public_key": "6T-kYBf65ERaEAhxIyHL1FCfu0QR6P2XQMtcvUgzSjM", "short_id": "70ad150d"}}}, {"tag": "jp-awsjp-02-idx-1", "type": "vless", "server": "pq.aws49.yydjc.top", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "xtls-rprx-vision", "tls": {"enabled": true, "insecure": false, "server_name": "buylite.tv.apple.com", "utls": {"enabled": true, "fingerprint": "chrome"}, "reality": {"enabled": true, "public_key": "xOnY2ykLrcFMIhp7GBQu1mSH7yIW-yCc1ThJnVVtKDc", "short_id": "5b6762be"}}}, {"tag": "jp-awsjp-03-idx-2", "type": "vless", "server": "pq.aws50.yydjc.top", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "xtls-rprx-vision", "tls": {"enabled": true, "insecure": false, "server_name": "tv.apple.com", "utls": {"enabled": true, "fingerprint": "chrome"}, "reality": {"enabled": true, "public_key": "O5WC6waWDGR_ElOHmhqEQGUWDufcFxTuu7BPhwW1sE4", "short_id": "e1df2a15"}}}, {"tag": "jp-awsjp-04-idx-3", "type": "vless", "server": "pq.aws51.yydjc.top", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "xtls-rprx-vision", "tls": {"enabled": true, "insecure": false, "server_name": "buylite.tv.apple.com", "utls": {"enabled": true, "fingerprint": "chrome"}, "reality": {"enabled": true, "public_key": "N86yyW-L91vOtC9qgJcYAhnva9M4WT3vclSnsQo4A2k", "short_id": "79fd451e"}}}, {"tag": "jp-awsjp-05-idx-4", "type": "vless", "server": "pq.aws52.yydjc.top", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "xtls-rprx-vision", "tls": {"enabled": true, "insecure": false, "server_name": "tv.apple.com", "utls": {"enabled": true, "fingerprint": "chrome"}, "reality": {"enabled": true, "public_key": "gkeUZppVQzutjGsRcsGMW8OrPHboJ3qRFpIvj8lcUj4", "short_id": "c174618d"}}}, {"tag": "sg-awssg-01-idx-5", "type": "vless", "server": "pq.aws58.yydjc.top", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "xtls-rprx-vision", "tls": {"enabled": true, "insecure": false, "server_name": "iosapps.itunes.apple.com", "utls": {"enabled": true, "fingerprint": "chrome"}, "reality": {"enabled": true, "public_key": "T1qm-lWyGwT_sYy7KsoVsIH20qVR3W79wHmElqjdgz4", "short_id": "c5905f97"}}}, {"tag": "sg-awssg-02-idx-6", "type": "vless", "server": "pq.aws59.yydjc.top", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "xtls-rprx-vision", "tls": {"enabled": true, "insecure": false, "server_name": "iosapps.itunes.apple.com", "utls": {"enabled": true, "fingerprint": "chrome"}, "reality": {"enabled": true, "public_key": "mnshlVo5tkzYbmEB9xrgmUHwYETnXLAjjlGAssqaDGI", "short_id": "436299c6"}}}, {"tag": "sg-awssg-03-idx-7", "type": "vless", "server": "pq.aws60.yydjc.top", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "xtls-rprx-vision", "tls": {"enabled": true, "insecure": false, "server_name": "iosapps.itunes.apple.com", "utls": {"enabled": true, "fingerprint": "chrome"}, "reality": {"enabled": true, "public_key": "xjWkdgeetCnB1-kHqwVnAaSUqg4qK9TFWQlamW8FSRI", "short_id": "e2e15173"}}}, {"tag": "sg-awssg-04-idx-8", "type": "vless", "server": "pq.aws61.yydjc.top", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "xtls-rprx-vision", "tls": {"enabled": true, "insecure": false, "server_name": "iosapps.itunes.apple.com", "utls": {"enabled": true, "fingerprint": "chrome"}, "reality": {"enabled": true, "public_key": "9076EbD5vM8kATc_08QvDiqZF5KHTM_LaSrMOvcwfHw", "short_id": "57b7989c"}}}, {"tag": "sg-awssg-05-idx-9", "type": "vless", "server": "pq.aws62.yydjc.top", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "xtls-rprx-vision", "tls": {"enabled": true, "insecure": false, "server_name": "iosapps.itunes.apple.com", "utls": {"enabled": true, "fingerprint": "chrome"}, "reality": {"enabled": true, "public_key": "kzlqssyYFyVB-nGMDTQXITmiEZNUF4S_ygmuTJ2BEA8", "short_id": "1f922969"}}}, {"tag": "us-us-01-0-1-hy2-idx-10", "type": "hysteria2", "server": "pq.us1.globals-download.com", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "www.apple.com"}}, {"tag": "us-us-02-0-1-hy2-idx-11", "type": "hysteria2", "server": "pq.us2.globals-download.com", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "www.apple.com"}}, {"tag": "us-us-03-0-1-hy2-idx-12", "type": "hysteria2", "server": "pq.us3.globals-download.com", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "tv.apple.com"}}, {"tag": "us-us-04-0-1-hy2-idx-13", "type": "hysteria2", "server": "pq.us4.globals-download.com", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "www.apple.com"}}, {"tag": "us-us-05-0-1-hy2-idx-14", "type": "hysteria2", "server": "208.87.243.39", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "www.apple.com"}}, {"tag": "us-us-06-0-1-hy2-idx-15", "type": "hysteria2", "server": "208.87.243.41", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "www.apple.com"}}, {"tag": "us-us-07-0-1-hy2-idx-16", "type": "hysteria2", "server": "208.87.243.57", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "www.apple.com"}}, {"tag": "us-us-08-0-1-hy2-idx-17", "type": "hysteria2", "server": "208.87.243.133", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "www.apple.com"}}, {"tag": "us-us-09-0-1-hy2-idx-18", "type": "hysteria2", "server": "208.87.243.139", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "www.apple.com"}}, {"tag": "us-us-10-0-1-hy2-idx-19", "type": "hysteria2", "server": "208.87.243.183", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "api.push.apple.com"}}, {"tag": "us-us-11-0-1-hy2-idx-20", "type": "hysteria2", "server": "208.87.243.195", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "api.push.apple.com"}}, {"tag": "us-us-12-0-1-hy2-idx-21", "type": "hysteria2", "server": "208.87.243.197", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "api.push.apple.com"}}, {"tag": "us-us-13-0-1-hy2-idx-22", "type": "hysteria2", "server": "108.181.4.223", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "api.push.apple.com"}}, {"tag": "us-us-14-0-1-hy2-idx-23", "type": "hysteria2", "server": "108.181.4.67", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "www.apple.com"}}, {"tag": "us-us-15-0-1-hy2-idx-24", "type": "hysteria2", "server": "108.181.5.199", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "www.apple.com"}}, {"tag": "us-us-02-hy2-idx-25", "type": "hysteria2", "server": "146.235.201.189", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "tv.apple.com"}}, {"tag": "us-us-03-hy2-idx-26", "type": "hysteria2", "server": "167.234.210.122", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "www.apple.com"}}, {"tag": "us-us-04-hy2-idx-27", "type": "hysteria2", "server": "138.2.229.162", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "tv.apple.com"}}, {"tag": "us-us-05-hy2-idx-28", "type": "hysteria2", "server": "167.234.208.240", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "tv.apple.com"}}, {"tag": "us-us-06-hy2-idx-29", "type": "hysteria2", "server": "192.18.133.190", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "tv.apple.com"}}, {"tag": "kr-kr-hy2-idx-30", "type": "hysteria2", "server": "193.123.240.119", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "www.apple.com"}}, {"tag": "kr-kr-2-hy2-idx-31", "type": "hysteria2", "server": "193.123.250.219", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "www.apple.com"}}, {"tag": "kr-kr-3-hy2-idx-32", "type": "hysteria2", "server": "146.56.164.36", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "api.push.apple.com"}}, {"tag": "kr-kr-4-hy2-idx-33", "type": "hysteria2", "server": "152.69.225.196", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "www.apple.com"}}, {"tag": "kr-kr-5-hy2-idx-34", "type": "hysteria2", "server": "146.56.100.103", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "www.apple.com"}}, {"tag": "ae-ae-hy2-idx-35", "type": "hysteria2", "server": "193.123.76.84", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "www.apple.com"}}, {"tag": "de-de-hy2-idx-36", "type": "hysteria2", "server": "132.145.239.250", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "api.push.apple.com"}}, {"tag": "de-de-2-hy2-idx-37", "type": "hysteria2", "server": "141.147.19.58", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "api.push.apple.com"}}, {"tag": "ch-ch-hy2-idx-38", "type": "hysteria2", "server": "152.67.95.183", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "www.apple.com"}}, {"tag": "au-au-hy2-idx-39", "type": "hysteria2", "server": "152.69.180.226", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "api.push.apple.com"}}, {"tag": "au-au-2-hy2-idx-40", "type": "hysteria2", "server": "158.179.18.251", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "api.push.apple.com"}}, {"tag": "au-au-3-hy2-idx-41", "type": "hysteria2", "server": "159.13.35.245", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "api.push.apple.com"}}, {"tag": "sg-sg-hy2-idx-42", "type": "hysteria2", "server": "152.69.220.212", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "api.push.apple.com"}}, {"tag": "sg-sg-2-hy2-idx-43", "type": "hysteria2", "server": "158.178.236.104", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "api.push.apple.com"}}, {"tag": "sg-sg-3-hy2-idx-44", "type": "hysteria2", "server": "140.245.35.78", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "www.apple.com"}}, {"tag": "sg-sg-4-hy2-idx-45", "type": "hysteria2", "server": "140.245.37.170", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "www.apple.com"}}, {"tag": "sg-sg-5-hy2-idx-46", "type": "hysteria2", "server": "140.245.46.17", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "www.apple.com"}}, {"tag": "sg-sg-6-hy2-idx-47", "type": "hysteria2", "server": "140.245.36.207", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "www.apple.com"}}, {"tag": "us-us-hy2-idx-48", "type": "hysteria2", "server": "**************", "server_port": 35000, "password": "5c38d810-a522-446e-bcdd-1082e8a8df86", "tls": {"enabled": true, "insecure": true, "server_name": "www.apple.com"}}, {"tag": "us-us-01-0-1-idx-49", "type": "vless", "server": "*************", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "", "transport": {"type": "ws", "path": "/pq/us1", "headers": {"Host": "pq-us1.globals-download.com"}}, "tls": {"enabled": true, "insecure": false, "server_name": "pq-us1.globals-download.com", "utls": {"enabled": true, "fingerprint": "chrome"}}}, {"tag": "us-us-02-0-1-idx-50", "type": "vless", "server": "*************", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "", "transport": {"type": "ws", "path": "/pq/us2", "headers": {"Host": "pq-us2.globals-download.com"}}, "tls": {"enabled": true, "insecure": false, "server_name": "pq-us2.globals-download.com", "utls": {"enabled": true, "fingerprint": "chrome"}}}, {"tag": "us-us-03-0-1-idx-51", "type": "vless", "server": "*************", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "", "transport": {"type": "ws", "path": "/pq/us3", "headers": {"Host": "pq-us3.globals-download.com"}}, "tls": {"enabled": true, "insecure": false, "server_name": "pq-us3.globals-download.com", "utls": {"enabled": true, "fingerprint": "chrome"}}}, {"tag": "us-us-04-0-1-idx-52", "type": "vless", "server": "*************", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "", "transport": {"type": "ws", "path": "/pq/us4", "headers": {"Host": "pq-us4.globals-download.com"}}, "tls": {"enabled": true, "insecure": false, "server_name": "pq-us4.globals-download.com", "utls": {"enabled": true, "fingerprint": "chrome"}}}, {"tag": "us-us-05-0-1-idx-53", "type": "vless", "server": "*************", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "", "transport": {"type": "ws", "path": "/pq/us5", "headers": {"Host": "pq-us5.globals-download.com"}}, "tls": {"enabled": true, "insecure": false, "server_name": "pq-us5.globals-download.com", "utls": {"enabled": true, "fingerprint": "chrome"}}}, {"tag": "us-us-06-0-1-idx-54", "type": "vless", "server": "*************", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "", "transport": {"type": "ws", "path": "/pq/us6", "headers": {"Host": "pq-us6.globals-download.com"}}, "tls": {"enabled": true, "insecure": false, "server_name": "pq-us6.globals-download.com", "utls": {"enabled": true, "fingerprint": "chrome"}}}, {"tag": "kr-kr-01-idx-55", "type": "vless", "server": "**************", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "xtls-rprx-vision", "tls": {"enabled": true, "insecure": false, "server_name": "buylite.music.apple.com", "utls": {"enabled": true, "fingerprint": "chrome"}, "reality": {"enabled": true, "public_key": "P--AseuV7sGvgy7YJ8iX58GxeP5-M2oq0Mq2YxPAXRs", "short_id": "f00d40f38482"}}}, {"tag": "hk-hkthk-01-idx-56", "type": "vless", "server": "pq.aws11.yydjc.top", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "xtls-rprx-vision", "tls": {"enabled": true, "insecure": false, "server_name": "download-porter.hoyoverse.com", "utls": {"enabled": true, "fingerprint": "chrome"}, "reality": {"enabled": true, "public_key": "E7siAVurojsISFT8AAx-QmWFlvgaifbJANnT4Xms4n0", "short_id": "884e9b89"}}}, {"tag": "hk-hkthk-02-idx-57", "type": "vless", "server": "pq.aws12.yydjc.top", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "xtls-rprx-vision", "tls": {"enabled": true, "insecure": false, "server_name": "download-porter.hoyoverse.com", "utls": {"enabled": true, "fingerprint": "ios"}, "reality": {"enabled": true, "public_key": "ybBxu1pW3O9Me954LCDfXgHW6lIYepAbnla82yaZ5HE", "short_id": "6a1faecb"}}}, {"tag": "hk-hkthk-03-idx-58", "type": "vless", "server": "pq.aws13.yydjc.top", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "xtls-rprx-vision", "tls": {"enabled": true, "insecure": false, "server_name": "cdn-dynmedia-1.microsoft.com", "utls": {"enabled": true, "fingerprint": "ios"}, "reality": {"enabled": true, "public_key": "d0lNmb2Ao0UELGAQ037dgn_dtoOxHuPMEwo9Z4zhLzE", "short_id": "4d84e084"}}}, {"tag": "hk-hkthk-04-idx-59", "type": "vless", "server": "pq.aws14.yydjc.top", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "xtls-rprx-vision", "tls": {"enabled": true, "insecure": false, "server_name": "www.mi.com", "utls": {"enabled": true, "fingerprint": "ios"}, "reality": {"enabled": true, "public_key": "Rwqxz0M2DjcW39poMFeZzfjlYZf2PwxxQ0UEI4CSZik", "short_id": "6891d99c"}}}, {"tag": "hk-hkthk-05-idx-60", "type": "vless", "server": "pq.aws15.yydjc.top", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "xtls-rprx-vision", "tls": {"enabled": true, "insecure": false, "server_name": "dyn.felissimo.co.jp", "utls": {"enabled": true, "fingerprint": "ios"}, "reality": {"enabled": true, "public_key": "XDOJMg-h4KXwKN5KelFXi1bFB8XBmM1XtxgdHnw6qik", "short_id": "56e852ac"}}}, {"tag": "tw-tw-idx-61", "type": "vless", "server": "pq.hinet.tw1.yydjc.top", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "xtls-rprx-vision", "tls": {"enabled": true, "insecure": false, "server_name": "download.yydjc.top", "utls": {"enabled": true, "fingerprint": "chrome"}}}, {"tag": "tw-tw-2-idx-62", "type": "vless", "server": "pq.hinet.tw2.yydjc.top", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "xtls-rprx-vision", "tls": {"enabled": true, "insecure": false, "server_name": "download.yydjc.top", "utls": {"enabled": true, "fingerprint": "chrome"}}}, {"tag": "tw-tw-3-idx-63", "type": "vless", "server": "pq.hinet.tw3.yydjc.top", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "xtls-rprx-vision", "tls": {"enabled": true, "insecure": false, "server_name": "download.yydjc.top", "utls": {"enabled": true, "fingerprint": "chrome"}}}, {"tag": "jp-jp-01-0-1-idx-64", "type": "vless", "server": "************", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "", "transport": {"type": "ws", "path": "/pq/jp1", "headers": {"Host": "jpa1.xn--ghqu5fm27b67w.com"}}, "tls": {"enabled": true, "insecure": false, "utls": {"enabled": true, "fingerprint": "ios"}}}, {"tag": "in-in-01-0-1-idx-65", "type": "vless", "server": "************", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "", "transport": {"type": "ws", "path": "/pq/in1", "headers": {"Host": "in1s-s.pqvip.top"}}, "tls": {"enabled": true, "insecure": false, "utls": {"enabled": true, "fingerprint": "chrome"}}}, {"tag": "us-us-01-0-1-idx-66", "type": "vless", "server": "************", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "", "transport": {"type": "ws", "path": "/pq/us1", "headers": {"Host": "usa1.xn--ghqu5fm27b67w.com"}}, "tls": {"enabled": true, "insecure": false, "utls": {"enabled": true, "fingerprint": "chrome"}}}, {"tag": "us-us-02-0-1-idx-67", "type": "vless", "server": "************", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "", "transport": {"type": "ws", "path": "/pq/us2", "headers": {"Host": "usa2.xn--ghqu5fm27b67w.com"}}, "tls": {"enabled": true, "insecure": false, "utls": {"enabled": true, "fingerprint": "chrome"}}}, {"tag": "us-us-03-0-1-idx-68", "type": "vless", "server": "************", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "", "transport": {"type": "ws", "path": "/pq/us3", "headers": {"Host": "usa3.xn--ghqu5fm27b67w.com"}}, "tls": {"enabled": true, "insecure": false, "utls": {"enabled": true, "fingerprint": "chrome"}}}, {"tag": "us-us-04-0-1-idx-69", "type": "vless", "server": "************", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "", "transport": {"type": "ws", "path": "/pq/us4", "headers": {"Host": "usa4.xn--ghqu5fm27b67w.com"}}, "tls": {"enabled": true, "insecure": false, "utls": {"enabled": true, "fingerprint": "chrome"}}}, {"tag": "us-us-05-0-1-idx-70", "type": "vless", "server": "************", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "", "transport": {"type": "ws", "path": "/pq/us5", "headers": {"Host": "usa5.xn--ghqu5fm27b67w.com"}}, "tls": {"enabled": true, "insecure": false, "utls": {"enabled": true, "fingerprint": "chrome"}}}, {"tag": "us-us-01-0-01-idx-71", "type": "vless", "server": "************", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "", "transport": {"type": "ws", "path": "/pq/us1", "headers": {"Host": "us1a.pqvip.top"}}, "tls": {"enabled": true, "insecure": false, "utls": {"enabled": true, "fingerprint": "chrome"}}}, {"tag": "us-us-02-0-01-idx-72", "type": "vless", "server": "************", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "", "transport": {"type": "ws", "path": "/pq/us2", "headers": {"Host": "us2s.pqvip.top"}}, "tls": {"enabled": true, "insecure": false, "utls": {"enabled": true, "fingerprint": "chrome"}}}, {"tag": "us-us-03-0-01-idx-73", "type": "vless", "server": "************", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "", "transport": {"type": "ws", "path": "/pq/us3", "headers": {"Host": "us3s.pqvip.top"}}, "tls": {"enabled": true, "insecure": false, "utls": {"enabled": true, "fingerprint": "chrome"}}}, {"tag": "us-us-04-0-01-idx-74", "type": "vless", "server": "************", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "", "transport": {"type": "ws", "path": "/pq/us4", "headers": {"Host": "us4s.pqvip.top"}}, "tls": {"enabled": true, "insecure": false, "utls": {"enabled": true, "fingerprint": "chrome"}}}, {"tag": "us-us-05-0-01-idx-75", "type": "vless", "server": "************", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "", "transport": {"type": "ws", "path": "/pq/us5", "headers": {"Host": "us5s.pqvip.top"}}, "tls": {"enabled": true, "insecure": false, "utls": {"enabled": true, "fingerprint": "ios"}}}, {"tag": "us-us-01-idx-76", "type": "vless", "server": "***************", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "xtls-rprx-vision", "tls": {"enabled": true, "insecure": false, "server_name": "dcpqjs.yydjc.top", "utls": {"enabled": true, "fingerprint": "ios"}}}, {"tag": "us-us-05-idx-77", "type": "vless", "server": "**************", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "xtls-rprx-vision", "tls": {"enabled": true, "insecure": false, "server_name": "dcpqjs.yydjc.top", "utls": {"enabled": true, "fingerprint": "ios"}}}, {"tag": "sg-sg-idx-78", "type": "vless", "server": "**************", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "xtls-rprx-vision", "tls": {"enabled": true, "insecure": false, "server_name": "tv.apple.com", "utls": {"enabled": true, "fingerprint": "chrome"}, "reality": {"enabled": true, "public_key": "Wmz2q2twfWYu6FrO5xU1M87LO5KjkyGif0AuPLNMPW0", "short_id": "a86103965ac913f2"}}}, {"tag": "sg-sg-2-idx-79", "type": "vless", "server": "140.245.58.56", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "xtls-rprx-vision", "tls": {"enabled": true, "insecure": false, "server_name": "tv.apple.com", "utls": {"enabled": true, "fingerprint": "chrome"}, "reality": {"enabled": true, "public_key": "Wmz2q2twfWYu6FrO5xU1M87LO5KjkyGif0AuPLNMPW0", "short_id": "a86103965ac913f2"}}}, {"tag": "sg-sg-3-idx-80", "type": "vless", "server": "*************", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "xtls-rprx-vision", "tls": {"enabled": true, "insecure": false, "server_name": "buylite.music.apple.com", "utls": {"enabled": true, "fingerprint": "chrome"}, "reality": {"enabled": true, "public_key": "IPno1w9GUxhRN2JY_yIka2DNlnuJHQeb3_0yOCM4pQg", "short_id": "3ae54db4833c"}}}, {"tag": "sg-sg-4-idx-81", "type": "vless", "server": "***************", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "xtls-rprx-vision", "tls": {"enabled": true, "insecure": false, "server_name": "buylite.music.apple.com", "utls": {"enabled": true, "fingerprint": "ios"}, "reality": {"enabled": true, "public_key": "kR-_5J2EPiB9nrdgyXjgzfJQnxQodkGeV_jgKOEnlEM", "short_id": "fc814374abba"}}}, {"tag": "jp-jp-idx-82", "type": "vless", "server": "*************", "server_port": 443, "uuid": "5c38d810-a522-446e-bcdd-1082e8a8df86", "flow": "xtls-rprx-vision", "tls": {"enabled": true, "insecure": false, "server_name": "buylite.music.apple.com", "utls": {"enabled": true, "fingerprint": "chrome"}, "reality": {"enabled": true, "public_key": "IHJ7w7m13QsYPFq8eppdqyTyeLmXzTtOG9EGu_-ep2c", "short_id": "49824283a241"}}}], "route": {"rules": [{"inbound": "worker-in-1", "outbound": "worker-selector-1"}, {"inbound": "worker-in-2", "outbound": "worker-selector-2"}, {"inbound": "worker-in-3", "outbound": "worker-selector-3"}, {"inbound": "worker-in-4", "outbound": "worker-selector-4"}, {"inbound": "worker-in-5", "outbound": "worker-selector-5"}, {"inbound": "worker-in-6", "outbound": "worker-selector-6"}, {"inbound": "worker-in-7", "outbound": "worker-selector-7"}, {"inbound": "worker-in-8", "outbound": "worker-selector-8"}, {"inbound": "worker-in-9", "outbound": "worker-selector-9"}, {"inbound": "worker-in-10", "outbound": "worker-selector-10"}]}}