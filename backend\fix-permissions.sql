-- 修复PostgreSQL权限问题
-- 在psql中执行这些命令

-- 连接到tts_app_db数据库
\c tts_app_db

-- 授予用户对public模式的所有权限
GRANT ALL ON SCHEMA public TO tts_app_user;

-- 授予用户创建表的权限
GRANT CREATE ON SCHEMA public TO tts_app_user;

-- 授予用户对所有表的权限（包括未来创建的表）
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO tts_app_user;

-- 授予用户对所有序列的权限
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO tts_app_user;

-- 授予用户对所有函数的权限
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO tts_app_user;

-- 显示当前权限
\dp

-- 退出
\q
