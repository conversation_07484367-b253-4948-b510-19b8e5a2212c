#!/usr/bin/env node

/**
 * 工作池控制器测试脚本
 * 验证工作池模型的功能和性能
 */

require('dotenv').config();

// 颜色输出函数
function log(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color] || ''}${message}${colors.reset}`);
}

/**
 * 测试配置加载
 */
async function testConfigLoading() {
  log('blue', '\n🔧 测试工作池配置加载...');
  
  try {
    const { configAdapter } = require('./src/gateway/adapters/ConfigAdapter');
    const config = configAdapter.getConfig();
    
    log('green', '✅ 配置加载成功');
    console.log('   工作池大小:', config.SINGBOX_WORKER_POOL_SIZE);
    console.log('   起始端口:', config.SINGBOX_WORKER_PORT_START);
    console.log('   选择器前缀:', config.SINGBOX_WORKER_SELECTOR_PREFIX);
    console.log('   入口前缀:', config.SINGBOX_WORKER_INBOUND_PREFIX);
    console.log('   网络模式:', config.NETWORK_MODE);
    console.log('   网关启用:', config.ENABLE_SINGBOX_GATEWAY);
    
    return config;
  } catch (error) {
    log('red', '❌ 配置加载失败:');
    console.error('   ', error.message);
    return null;
  }
}

/**
 * 测试工作池控制器初始化
 */
async function testWorkerPoolController() {
  log('blue', '\n🏭 测试工作池控制器初始化...');
  
  try {
    const { WorkerPoolController } = require('./src/gateway/core/WorkerPoolController');
    const { configAdapter } = require('./src/gateway/adapters/ConfigAdapter');
    
    const config = configAdapter.getConfig();
    const controller = new WorkerPoolController(config);
    
    log('cyan', '   正在初始化工作池控制器...');
    await controller.initialize();
    
    log('green', '✅ 工作池控制器初始化成功');
    
    // 获取统计信息
    const stats = await controller.getStats();
    console.log('   总节点数:', stats.totalNodes);
    console.log('   健康节点数:', stats.healthyNodes);
    console.log('   总工人数:', stats.totalWorkers);
    console.log('   空闲工人数:', stats.idleWorkers);
    
    // 获取工人池状态
    const workerStatus = controller.getWorkerPoolStatus();
    console.log('   工人详情:');
    workerStatus.workers.forEach(worker => {
      console.log(`     工人${worker.id}: 端口${worker.port}, 选择器${worker.selector}, 状态${worker.isBusy ? '忙碌' : '空闲'}`);
    });
    
    await controller.cleanup();
    return controller;
    
  } catch (error) {
    log('red', '❌ 工作池控制器测试失败:');
    console.error('   ', error.message);
    return null;
  }
}

/**
 * 测试节点名称验证功能
 */
async function testNodeNameValidation() {
  log('blue', '\n🔧 测试节点名称验证功能...');

  try {
    const { WorkerPoolController } = require('./src/gateway/core/WorkerPoolController');
    const { configAdapter } = require('./src/gateway/adapters/ConfigAdapter');

    const config = configAdapter.getConfig();
    const controller = new WorkerPoolController(config);

    // 测试节点名称验证方法
    const testCases = [
      {
        input: 'jp-awsjp-01-idx-0',
        expected: true,
        description: '日本AWS节点'
      },
      {
        input: 'sg-awssg-04-idx-8',
        expected: true,
        description: '新加坡AWS节点'
      },
      {
        input: 'us-us-01-0-1-hy2-idx-10',
        expected: true,
        description: '美国Hysteria2节点'
      },
      {
        input: 'kr-kr-hy2-idx-30',
        expected: true,
        description: '韩国Hysteria2节点'
      },
      {
        input: 'hk-hkthk-01-idx-56',
        expected: true,
        description: '香港节点'
      },
      {
        input: 'invalid-node-name',
        expected: false,
        description: '无效节点名称'
      },
      {
        input: '🇯🇵AWS日本东京01',
        expected: false,
        description: '包含emoji的旧格式'
      },
      {
        input: '',
        expected: false,
        description: '空字符串'
      }
    ];

    let passedTests = 0;

    for (const testCase of testCases) {
      const result = controller.validateNodeName(testCase.input);
      const passed = result === testCase.expected;

      console.log(`   测试: ${testCase.description}`);
      console.log(`   输入: "${testCase.input}"`);
      console.log(`   期望: ${testCase.expected}`);
      console.log(`   结果: ${result}`);
      console.log(`   状态: ${passed ? '✅ 通过' : '❌ 失败'}`);
      console.log('');

      if (passed) passedTests++;
    }

    log('green', `✅ 节点名称验证测试完成: ${passedTests}/${testCases.length} 个测试通过`);
    return passedTests === testCases.length;

  } catch (error) {
    log('red', '❌ 节点名称验证测试失败:');
    console.error('   ', error.message);
    return false;
  }
}

/**
 * 测试工人获取和释放
 */
async function testWorkerAcquisition() {
  log('blue', '\n👷 测试工人获取和释放...');

  try {
    const { WorkerPoolController } = require('./src/gateway/core/WorkerPoolController');
    const { configAdapter } = require('./src/gateway/adapters/ConfigAdapter');

    const config = configAdapter.getConfig();
    const controller = new WorkerPoolController(config);

    await controller.initialize();

    // 测试获取工人
    log('cyan', '   正在获取工人...');
    const acquiredInfo = await controller.acquireWorker();
    const { worker, nodeTag } = acquiredInfo;

    log('green', '✅ 工人获取成功');
    console.log(`   工人ID: ${worker.id}`);
    console.log(`   工人端口: ${worker.port}`);
    console.log(`   分配节点: ${nodeTag}`);
    console.log(`   选择器: ${worker.selector}`);

    // 检查工人状态
    const statusAfterAcquire = controller.getWorkerPoolStatus();
    console.log(`   忙碌工人数: ${statusAfterAcquire.busyWorkers}`);
    console.log(`   空闲工人数: ${statusAfterAcquire.idleWorkers}`);

    // 测试释放工人
    log('cyan', '   正在释放工人...');
    controller.releaseWorker(worker);

    const statusAfterRelease = controller.getWorkerPoolStatus();
    console.log(`   释放后忙碌工人数: ${statusAfterRelease.busyWorkers}`);
    console.log(`   释放后空闲工人数: ${statusAfterRelease.idleWorkers}`);

    log('green', '✅ 工人释放成功');

    await controller.cleanup();
    return true;

  } catch (error) {
    log('red', '❌ 工人获取释放测试失败:');
    console.error('   ', error.message);
    return false;
  }
}

/**
 * 测试并发工人获取
 */
async function testConcurrentWorkerAcquisition() {
  log('blue', '\n🚀 测试并发工人获取...');
  
  try {
    const { WorkerPoolController } = require('./src/gateway/core/WorkerPoolController');
    const { configAdapter } = require('./src/gateway/adapters/ConfigAdapter');
    
    const config = configAdapter.getConfig();
    const controller = new WorkerPoolController(config);
    
    await controller.initialize();
    
    const concurrentCount = Math.min(5, config.SINGBOX_WORKER_POOL_SIZE);
    log('cyan', `   正在并发获取 ${concurrentCount} 个工人...`);
    
    // 并发获取多个工人
    const acquisitionPromises = Array.from({ length: concurrentCount }, async (_, index) => {
      try {
        const acquiredInfo = await controller.acquireWorker();
        console.log(`     工人${index + 1}: ID=${acquiredInfo.worker.id}, 端口=${acquiredInfo.worker.port}, 节点=${acquiredInfo.nodeTag}`);
        return acquiredInfo;
      } catch (error) {
        console.error(`     工人${index + 1}获取失败:`, error.message);
        return null;
      }
    });
    
    const results = await Promise.all(acquisitionPromises);
    const successfulAcquisitions = results.filter(result => result !== null);
    
    log('green', `✅ 成功获取 ${successfulAcquisitions.length}/${concurrentCount} 个工人`);
    
    // 检查工人池状态
    const status = controller.getWorkerPoolStatus();
    console.log(`   当前忙碌工人数: ${status.busyWorkers}`);
    console.log(`   当前空闲工人数: ${status.idleWorkers}`);
    
    // 释放所有工人
    log('cyan', '   正在释放所有工人...');
    successfulAcquisitions.forEach(acquiredInfo => {
      controller.releaseWorker(acquiredInfo.worker);
    });
    
    const finalStatus = controller.getWorkerPoolStatus();
    console.log(`   释放后忙碌工人数: ${finalStatus.busyWorkers}`);
    console.log(`   释放后空闲工人数: ${finalStatus.idleWorkers}`);
    
    await controller.cleanup();
    return true;
    
  } catch (error) {
    log('red', '❌ 并发工人获取测试失败:');
    console.error('   ', error.message);
    return false;
  }
}

/**
 * 测试网络适配器集成
 */
async function testNetworkAdapterIntegration() {
  log('blue', '\n🌐 测试网络适配器集成...');
  
  try {
    const { createProxyGateway } = require('./src/gateway');
    
    log('cyan', '   正在创建代理网关...');
    const gateway = createProxyGateway();
    await gateway.initialize();
    
    const networkClient = gateway.getNetworkClient();
    const singboxController = gateway.getSingboxController();
    
    log('green', '✅ 代理网关创建成功');
    console.log('   网络客户端类型:', networkClient.constructor.name);
    console.log('   控制器类型:', singboxController.constructor.name);
    
    // 检查是否是工作池控制器
    if (singboxController.constructor.name === 'WorkerPoolController') {
      const workerStatus = singboxController.getWorkerPoolStatus();
      console.log('   工作池状态:');
      console.log(`     总工人数: ${workerStatus.totalWorkers}`);
      console.log(`     空闲工人数: ${workerStatus.idleWorkers}`);
    }
    
    await gateway.stop();
    return true;
    
  } catch (error) {
    log('red', '❌ 网络适配器集成测试失败:');
    console.error('   ', error.message);
    return false;
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  log('cyan', '🧪 工作池控制器测试开始（包含emoji修复）');
  log('cyan', '================================');

  const results = {
    config: false,
    emoji: false,
    controller: false,
    acquisition: false,
    concurrent: false,
    integration: false
  };

  // 运行所有测试
  results.config = await testConfigLoading() !== null;
  results.nodeValidation = await testNodeNameValidation();
  results.controller = await testWorkerPoolController() !== null;
  results.acquisition = await testWorkerAcquisition();
  results.concurrent = await testConcurrentWorkerAcquisition();
  results.integration = await testNetworkAdapterIntegration();

  // 输出测试结果
  log('cyan', '\n📊 测试结果汇总');
  log('cyan', '================================');

  const testNames = {
    config: '配置加载',
    emoji: 'Emoji修复功能',
    controller: '控制器初始化',
    acquisition: '工人获取释放',
    concurrent: '并发工人获取',
    integration: '网络适配器集成'
  };

  let passedCount = 0;
  Object.entries(results).forEach(([key, passed]) => {
    const status = passed ? '✅ 通过' : '❌ 失败';
    console.log(`   ${testNames[key]}: ${status}`);
    if (passed) passedCount++;
  });

  const totalTests = Object.keys(results).length;
  log('cyan', `\n总计: ${passedCount}/${totalTests} 个测试通过`);

  if (passedCount === totalTests) {
    log('green', '🎉 所有测试通过！工作池控制器运行正常，emoji修复功能已启用。');
  } else {
    log('yellow', '⚠️  部分测试失败，请检查配置和环境。');
  }
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    log('red', '❌ 测试运行失败:');
    console.error(error);
    process.exit(1);
  });
}

module.exports = {
  testConfigLoading,
  testNodeNameValidation,
  testWorkerPoolController,
  testWorkerAcquisition,
  testConcurrentWorkerAcquisition,
  testNetworkAdapterIntegration,
  runTests
};
