# Redis 绿色版配置指南

## 🔴 Redis 启动步骤

### 1. 直接启动（推荐）
```bash
# 方法1：双击启动
直接双击 redis-server.exe

# 方法2：命令行启动
cd "您的Redis目录"
redis-server.exe
```

### 2. 使用配置文件启动
```bash
# 如果有 redis.windows.conf 文件
redis-server.exe redis.windows.conf
```

### 3. 测试Redis连接
```bash
# 打开新的命令行窗口
cd "您的Redis目录"
redis-cli.exe ping
# 应该返回: PONG
```

## 🔧 常见问题解决

### 问题1: 端口被占用
```bash
# 检查端口占用
netstat -ano | findstr :6379

# 如果被占用，可以修改端口
redis-server.exe --port 6380
```

### 问题2: 权限问题
- 右键 redis-server.exe → 以管理员身份运行

### 问题3: 防火墙阻止
- Windows防火墙 → 允许应用通过防火墙 → 添加redis-server.exe

## 📝 Redis 基本命令测试

启动Redis后，可以测试以下命令：

```bash
# 连接Redis
redis-cli.exe

# 在Redis CLI中执行：
ping                    # 测试连接
set test "hello"        # 设置键值
get test               # 获取值
keys *                 # 查看所有键
exit                   # 退出
```

## 🚀 自动启动配置

### 创建Redis启动脚本
创建 `start-redis.bat`:
```batch
@echo off
cd /d "您的Redis目录路径"
echo 启动Redis服务器...
redis-server.exe
pause
```

### 后台运行Redis
创建 `start-redis-background.bat`:
```batch
@echo off
cd /d "您的Redis目录路径"
start /min redis-server.exe
echo Redis已在后台启动
```

## 📊 验证Redis状态

### 检查Redis进程
```bash
tasklist | findstr redis
```

### 检查Redis日志
Redis启动后会在命令行显示日志，注意查看：
- 端口信息 (默认6379)
- 内存使用情况
- 是否有错误信息

## 🔗 与TTS应用连接

确保 `.env` 文件中的Redis配置正确：
```
REDIS_URL="redis://localhost:6379"
```

如果修改了Redis端口，相应更新配置：
```
REDIS_URL="redis://localhost:6380"
```
