// 多个备用API功能测试脚本
// 用于验证备用API配置解析和切换逻辑

console.log('=== 多个备用API功能测试 ===\n');

// 模拟环境变量
const testCases = [
  {
    name: '单个备用API（向后兼容）',
    env: {
      NEXT_PUBLIC_ENABLE_BACKUP_API: 'true',
      NEXT_PUBLIC_BACKUP_API_URL: 'https://backup1.example.com'
    }
  },
  {
    name: '多个备用API（逗号分隔）',
    env: {
      NEXT_PUBLIC_ENABLE_BACKUP_API: 'true',
      NEXT_PUBLIC_BACKUP_API_URL: 'https://backup1.example.com,https://backup2.example.com,https://backup3.example.com'
    }
  },
  {
    name: '多个备用API（包含空格）',
    env: {
      NEXT_PUBLIC_ENABLE_BACKUP_API: 'true',
      NEXT_PUBLIC_BACKUP_API_URL: ' https://backup1.example.com , https://backup2.example.com , https://backup3.example.com '
    }
  },
  {
    name: '备用API未启用',
    env: {
      NEXT_PUBLIC_ENABLE_BACKUP_API: 'false',
      NEXT_PUBLIC_BACKUP_API_URL: 'https://backup1.example.com,https://backup2.example.com'
    }
  },
  {
    name: '空的备用API配置',
    env: {
      NEXT_PUBLIC_ENABLE_BACKUP_API: 'true',
      NEXT_PUBLIC_BACKUP_API_URL: ''
    }
  }
];

// 模拟API配置解析逻辑
function parseBackupApiConfig(env) {
  const BACKUP_URL = env.NEXT_PUBLIC_BACKUP_API_URL || null;
  const BACKUP_URLS = env.NEXT_PUBLIC_BACKUP_API_URL 
    ? env.NEXT_PUBLIC_BACKUP_API_URL.split(',').map(url => url.trim()).filter(url => url.length > 0)
    : [];
  const ENABLE_BACKUP = env.NEXT_PUBLIC_ENABLE_BACKUP_API === 'true';

  return {
    BACKUP_URL,
    BACKUP_URLS,
    ENABLE_BACKUP
  };
}

// 模拟获取备用API数量的函数
function getBackupApiCount(env) {
  if (env.NEXT_PUBLIC_ENABLE_BACKUP_API !== 'true' || !env.NEXT_PUBLIC_BACKUP_API_URL) {
    return 0;
  }
  
  const backupUrls = env.NEXT_PUBLIC_BACKUP_API_URL.split(',').map(url => url.trim()).filter(url => url.length > 0);
  return backupUrls.length;
}

// 模拟获取指定索引的备用API URL
function getBackupApiUrl(env, index) {
  if (env.NEXT_PUBLIC_ENABLE_BACKUP_API !== 'true' || !env.NEXT_PUBLIC_BACKUP_API_URL) {
    return null;
  }
  
  const backupUrls = env.NEXT_PUBLIC_BACKUP_API_URL.split(',').map(url => url.trim()).filter(url => url.length > 0);
  return (index >= 0 && index < backupUrls.length) ? backupUrls[index] : null;
}

// 运行测试
testCases.forEach((testCase, i) => {
  console.log(`${i + 1}. ${testCase.name}`);
  console.log(`   环境变量: ${JSON.stringify(testCase.env)}`);
  
  const config = parseBackupApiConfig(testCase.env);
  console.log(`   解析结果: ${JSON.stringify(config)}`);
  
  const count = getBackupApiCount(testCase.env);
  console.log(`   备用API数量: ${count}`);
  
  if (count > 0) {
    console.log(`   备用API列表:`);
    for (let j = 0; j < count; j++) {
      const url = getBackupApiUrl(testCase.env, j);
      console.log(`     [${j}] ${url}`);
    }
  }
  
  console.log('');
});

console.log('=== 重试流程模拟 ===\n');

// 模拟重试流程
function simulateRetryFlow(env) {
  console.log('模拟重试流程:');
  console.log('1. 主API失败');
  console.log('2. 前端重试失败');
  
  const backupCount = getBackupApiCount(env);
  if (backupCount === 0) {
    console.log('3. 无备用API，直接进入数据中心重试');
    return;
  }
  
  console.log(`3. 开始备用API重试 (共${backupCount}个备用API):`);
  for (let i = 0; i < backupCount; i++) {
    const url = getBackupApiUrl(env, i);
    console.log(`   尝试备用API ${i + 1}/${backupCount}: ${url}`);
  }
  console.log('4. 所有备用API失败后，进入数据中心重试');
}

// 测试重试流程
const retryTestCase = testCases[1]; // 使用多个备用API的测试用例
console.log(`使用测试用例: ${retryTestCase.name}`);
simulateRetryFlow(retryTestCase.env);

console.log('\n=== 测试完成 ===');
