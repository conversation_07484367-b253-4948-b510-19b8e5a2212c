# PostgreSQL 绿色版配置指南

## 🐘 PostgreSQL 初始化和启动

### 1. 首次使用 - 初始化数据库
```bash
# 进入PostgreSQL的bin目录
cd "您的PostgreSQL路径\bin"

# 初始化数据库（只需要执行一次）
initdb.exe -D ..\data -U postgres -W
# 会提示设置postgres用户密码，请记住这个密码
```

### 2. 启动PostgreSQL服务
```bash
# 方法1：使用pg_ctl启动
cd "您的PostgreSQL路径\bin"
pg_ctl.exe -D ..\data start

# 方法2：直接启动服务器
postgres.exe -D ..\data
```

### 3. 连接数据库测试
```bash
# 连接到PostgreSQL
cd "您的PostgreSQL路径\bin"
psql.exe -U postgres

# 在psql中执行：
\l                      # 列出所有数据库
\q                      # 退出
```

## 🗄️ 创建TTS应用数据库

### 1. 创建数据库和用户
```sql
-- 连接到PostgreSQL
psql -U postgres

-- 创建数据库
CREATE DATABASE tts_app_db;

-- 创建用户
CREATE USER tts_app_user WITH PASSWORD 'TTS_DB_2024_SecurePass!';

-- 授权
GRANT ALL PRIVILEGES ON DATABASE tts_app_db TO tts_app_user;

-- 退出
\q
```

### 2. 测试连接
```bash
# 使用新用户连接数据库
psql -U tts_app_user -d tts_app_db -h localhost
```

## 🔧 常见问题解决

### 问题1: 端口被占用
```bash
# 检查端口占用
netstat -ano | findstr :5432

# 修改端口（在postgresql.conf中）
port = 5433
```

### 问题2: 权限问题
- 确保PostgreSQL目录有读写权限
- 以管理员身份运行命令行

### 问题3: 初始化失败
```bash
# 清理data目录重新初始化
rmdir /s ..\data
mkdir ..\data
initdb.exe -D ..\data -U postgres -W
```

## 📝 PostgreSQL 基本操作

### 连接数据库
```bash
# 连接本地数据库
psql -U postgres -h localhost

# 连接指定数据库
psql -U tts_app_user -d tts_app_db -h localhost
```

### 常用SQL命令
```sql
-- 查看数据库列表
\l

-- 连接到数据库
\c tts_app_db

-- 查看表列表
\dt

-- 查看表结构
\d table_name

-- 退出
\q
```

## 🚀 自动启动配置

### 创建PostgreSQL启动脚本
创建 `start-postgresql.bat`:
```batch
@echo off
cd /d "您的PostgreSQL路径\bin"
echo 启动PostgreSQL服务器...
pg_ctl.exe -D ..\data start
pause
```

### 停止PostgreSQL
创建 `stop-postgresql.bat`:
```batch
@echo off
cd /d "您的PostgreSQL路径\bin"
echo 停止PostgreSQL服务器...
pg_ctl.exe -D ..\data stop
pause
```

## 📊 验证PostgreSQL状态

### 检查进程
```bash
tasklist | findstr postgres
```

### 检查服务状态
```bash
cd "您的PostgreSQL路径\bin"
pg_ctl.exe -D ..\data status
```

## 🔗 与TTS应用连接

确保 `.env` 文件中的数据库配置正确：
```
DATABASE_URL="postgresql://tts_app_user:TTS_DB_2024_SecurePass!@localhost:5432/tts_app_db"
```

## 📋 完整启动流程

1. **初始化数据库**（仅首次）
2. **启动PostgreSQL服务**
3. **创建应用数据库和用户**
4. **运行TTS应用的数据库迁移**
5. **启动TTS应用**
