"use client"

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { DialogueLine } from './DialogueLine';

interface Voice {
  id: string;
  name: string;
  gender: string;
  language?: string;
  description: string;
  preview?: string;
}

interface DialogueLineData {
  id: number;
  voice: string;
  text: string;
}

interface VirtualDialogueListProps {
  dialogueLines: DialogueLineData[];
  voices: Voice[];
  voiceIconMapping: Record<string, string>;
  voiceIcons: string[];
  activeDialogueLineId: number | null;
  onSelectLine: (lineId: number) => void;
  onUpdateText: (lineId: number, text: string) => void;
  onRemoveLine: (lineId: number) => void;
  onTextInputFocus?: (lineId: number) => void;
  onEditVoice?: (lineId: number) => void;
  itemHeight?: number;
  containerHeight?: number;
  overscan?: number;
  className?: string;
}

export const VirtualDialogueList: React.FC<VirtualDialogueListProps> = ({
  dialogueLines,
  voices,
  voiceIconMapping,
  voiceIcons,
  activeDialogueLineId,
  onSelectLine,
  onUpdateText,
  onRemoveLine,
  onTextInputFocus,
  onEditVoice,
  itemHeight = 80,
  containerHeight = 350,
  overscan = 2,
  className = ""
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const isScrollingRef = useRef(false);
  const scrollTimeoutRef = useRef<NodeJS.Timeout>();

  // 计算可见范围
  const visibleCount = Math.ceil(containerHeight / itemHeight);
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(dialogueLines.length, startIndex + visibleCount + overscan * 2);
  const visibleLines = dialogueLines.slice(startIndex, endIndex);

  // 总高度
  const totalHeight = dialogueLines.length * itemHeight;

  // 处理滚动事件
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const newScrollTop = e.currentTarget.scrollTop;
    setScrollTop(newScrollTop);
    
    // 标记正在滚动
    isScrollingRef.current = true;
    
    // 清除之前的定时器
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }
    
    // 设置滚动结束标记
    scrollTimeoutRef.current = setTimeout(() => {
      isScrollingRef.current = false;
    }, 150);
  }, []);

  // 滚动到指定行
  const scrollToLine = useCallback((lineId: number) => {
    const index = dialogueLines.findIndex(line => line.id === lineId);
    if (index !== -1 && containerRef.current) {
      const targetScrollTop = index * itemHeight;
      containerRef.current.scrollTo({
        top: targetScrollTop,
        behavior: 'smooth'
      });
    }
  }, [dialogueLines, itemHeight]);

  // 当激活行变化时，确保其可见
  useEffect(() => {
    if (activeDialogueLineId !== null && !isScrollingRef.current) {
      const activeIndex = dialogueLines.findIndex(line => line.id === activeDialogueLineId);
      if (activeIndex !== -1) {
        const activeTop = activeIndex * itemHeight;
        const activeBottom = activeTop + itemHeight;
        const viewportTop = scrollTop;
        const viewportBottom = scrollTop + containerHeight;
        
        // 如果激活行不在可视区域内，滚动到该行
        if (activeTop < viewportTop || activeBottom > viewportBottom) {
          scrollToLine(activeDialogueLineId);
        }
      }
    }
  }, [activeDialogueLineId, dialogueLines, itemHeight, containerHeight, scrollTop, scrollToLine]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div 
      ref={containerRef}
      className={`overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div 
          style={{ 
            transform: `translateY(${startIndex * itemHeight}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0
          }}
        >
          {visibleLines.map((line, index) => {
            const actualIndex = startIndex + index;
            return (
              <div 
                key={line.id} 
                style={{ 
                  height: itemHeight,
                  paddingBottom: '12px' // 间距
                }}
              >
                <DialogueLine
                  line={line}
                  index={actualIndex}
                  voices={voices}
                  voiceIconMapping={voiceIconMapping}
                  voiceIcons={voiceIcons}
                  isActive={activeDialogueLineId === line.id}
                  onSelectLine={onSelectLine}
                  onUpdateText={onUpdateText}
                  onRemoveLine={onRemoveLine}
                  canRemove={dialogueLines.length > 1}
                  onTextInputFocus={onTextInputFocus}
                  onEditVoice={onEditVoice}
                />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

// 普通列表组件（用于少量数据）
export const RegularDialogueList: React.FC<Omit<VirtualDialogueListProps, 'itemHeight' | 'overscan'>> = ({
  dialogueLines,
  voices,
  voiceIconMapping,
  voiceIcons,
  activeDialogueLineId,
  onSelectLine,
  onUpdateText,
  onRemoveLine,
  onTextInputFocus,
  onEditVoice,
  containerHeight = 350,
  className = ""
}) => {
  return (
    <div 
      className={`space-y-3 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 pr-2 ${className}`}
      style={{ maxHeight: containerHeight }}
    >
      {dialogueLines.map((line, index) => (
        <DialogueLine
          key={line.id}
          line={line}
          index={index}
          voices={voices}
          voiceIconMapping={voiceIconMapping}
          voiceIcons={voiceIcons}
          isActive={activeDialogueLineId === line.id}
          onSelectLine={onSelectLine}
          onUpdateText={onUpdateText}
          onRemoveLine={onRemoveLine}
          canRemove={dialogueLines.length > 1}
          onTextInputFocus={onTextInputFocus}
          onEditVoice={onEditVoice}
        />
      ))}
    </div>
  );
};

// 智能列表组件 - 根据数据量自动选择渲染方式
interface SmartDialogueListProps extends Omit<VirtualDialogueListProps, 'itemHeight' | 'overscan'> {
  virtualThreshold?: number; // 超过多少行启用虚拟滚动
}

export const SmartDialogueList: React.FC<SmartDialogueListProps> = ({
  dialogueLines,
  virtualThreshold = 20,
  ...props
}) => {
  const shouldUseVirtual = dialogueLines.length > virtualThreshold;

  if (shouldUseVirtual) {
    return <VirtualDialogueList dialogueLines={dialogueLines} {...props} />;
  } else {
    return <RegularDialogueList dialogueLines={dialogueLines} {...props} />;
  }
};

export default SmartDialogueList;
