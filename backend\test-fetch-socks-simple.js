#!/usr/bin/env node

/**
 * 简单测试fetch-socks的使用
 */

require('dotenv').config();

// 颜色输出函数
function log(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color] || ''}${message}${colors.reset}`);
}

const TEST_URL = 'https://api.ipify.org?format=json';
const PROXY_PORT = 1081;

/**
 * 测试fetch-socks的基本用法
 */
async function testFetchSocks() {
  log('blue', '\n🧪 测试fetch-socks基本用法');
  log('blue', '========================');
  
  try {
    const { socksDispatcher } = require('fetch-socks');
    
    console.log('   创建SOCKS dispatcher...');
    const dispatcher = socksDispatcher({
      type: 5,
      host: '127.0.0.1',
      port: PROXY_PORT
    });
    
    console.log('   发起fetch请求...');
    console.log(`   URL: ${TEST_URL}`);
    console.log(`   代理: socks5://127.0.0.1:${PROXY_PORT}`);
    
    const response = await fetch(TEST_URL, {
      dispatcher: dispatcher,
      signal: AbortSignal.timeout(15000)
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    
    log('green', '✅ fetch-socks请求成功');
    console.log('   返回IP:', data.ip);
    
    return { success: true, ip: data.ip };
    
  } catch (error) {
    log('red', '❌ fetch-socks请求失败');
    console.log('   错误:', error.message);
    console.log('   错误类型:', error.name);
    console.log('   错误堆栈:', error.stack);
    
    return { success: false, error: error.message };
  }
}

/**
 * 测试传统socks-proxy-agent
 */
async function testSocksProxyAgent() {
  log('blue', '\n🧪 测试socks-proxy-agent');
  log('blue', '====================');
  
  try {
    const { SocksProxyAgent } = require('socks-proxy-agent');
    
    console.log('   创建SOCKS agent...');
    const agent = new SocksProxyAgent(`socks5h://127.0.0.1:${PROXY_PORT}`);
    
    console.log('   发起fetch请求...');
    console.log(`   URL: ${TEST_URL}`);
    console.log(`   代理: socks5h://127.0.0.1:${PROXY_PORT}`);
    
    const response = await fetch(TEST_URL, {
      agent: agent,
      signal: AbortSignal.timeout(15000)
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    
    log('green', '✅ socks-proxy-agent请求成功');
    console.log('   返回IP:', data.ip);
    
    return { success: true, ip: data.ip };
    
  } catch (error) {
    log('red', '❌ socks-proxy-agent请求失败');
    console.log('   错误:', error.message);
    console.log('   错误类型:', error.name);
    
    return { success: false, error: error.message };
  }
}

/**
 * 测试直连
 */
async function testDirect() {
  log('blue', '\n🧪 测试直连');
  log('blue', '============');
  
  try {
    console.log('   发起直连请求...');
    console.log(`   URL: ${TEST_URL}`);
    
    const response = await fetch(TEST_URL, {
      signal: AbortSignal.timeout(15000)
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    
    log('green', '✅ 直连请求成功');
    console.log('   返回IP:', data.ip);
    
    return { success: true, ip: data.ip };
    
  } catch (error) {
    log('red', '❌ 直连请求失败');
    console.log('   错误:', error.message);
    
    return { success: false, error: error.message };
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  log('cyan', '🔬 SOCKS代理测试');
  log('cyan', '================');
  
  console.log('   Node.js版本:', process.version);
  console.log('   fetch可用:', typeof fetch !== 'undefined');
  
  const results = [];
  
  // 测试直连
  const directResult = await testDirect();
  results.push({ method: 'direct', ...directResult });
  
  // 测试fetch-socks
  const fetchSocksResult = await testFetchSocks();
  results.push({ method: 'fetch-socks', ...fetchSocksResult });
  
  // 测试socks-proxy-agent
  const socksAgentResult = await testSocksProxyAgent();
  results.push({ method: 'socks-proxy-agent', ...socksAgentResult });
  
  // 分析结果
  log('cyan', '\n📊 测试结果');
  log('cyan', '============');
  
  console.log('\n📋 结果汇总:');
  results.forEach(result => {
    const status = result.success ? '✅ 成功' : '❌ 失败';
    const detail = result.success ? result.ip : result.error;
    console.log(`   ${result.method}: ${status} - ${detail}`);
  });
  
  const successResults = results.filter(r => r.success);
  if (successResults.length > 0) {
    const uniqueIPs = new Set(successResults.map(r => r.ip));
    console.log(`\n🔍 唯一IP数量: ${uniqueIPs.size}`);
    console.log(`   IP列表: ${Array.from(uniqueIPs).join(', ')}`);
    
    if (uniqueIPs.size > 1) {
      log('green', '🎉 检测到不同IP，代理工作正常！');
    } else {
      log('yellow', '⚠️  所有方法返回相同IP');
    }
  }
  
  return results;
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    log('red', '❌ 测试执行失败:');
    console.error(error);
    process.exit(1);
  });
}

module.exports = { runTests };
