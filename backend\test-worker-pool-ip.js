#!/usr/bin/env node

/**
 * 工作池IP地址测试脚本
 * 测试工作池模型是否正确使用不同节点进行网络请求
 * 通过获取IP地址验证节点切换和代理功能
 */

require('dotenv').config();

// 颜色输出函数
function log(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    magenta: '\x1b[35m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color] || ''}${message}${colors.reset}`);
}

/**
 * 获取IP地址的API服务列表
 */
const IP_SERVICES = [
  {
    name: 'ipify',
    url: 'https://api.ipify.org?format=json',
    parseResponse: (data) => data.ip
  },
  {
    name: 'ip-api',
    url: 'http://ip-api.com/json',
    parseResponse: (data) => ({
      ip: data.query,
      country: data.country,
      city: data.city,
      isp: data.isp
    })
  },
  {
    name: 'httpbin',
    url: 'https://httpbin.org/ip',
    parseResponse: (data) => data.origin
  },
  {
    name: 'myip',
    url: 'https://api.myip.com',
    parseResponse: (data) => ({
      ip: data.ip,
      country: data.country,
      cc: data.cc
    })
  }
];

/**
 * 使用工作池获取IP地址
 */
async function getIpViaWorkerPool(serviceIndex = 0) {
  const service = IP_SERVICES[serviceIndex];
  
  try {
    // 导入网络管理器
    const { networkManager } = require('./src/utils/networkManager');

    // 初始化网络管理器（自动使用gateway模式）
    await networkManager.initialize();
    
    log('cyan', `   正在通过工作池请求 ${service.name} (${service.url})`);
    
    const startTime = Date.now();
    
    // 发起网络请求
    const response = await networkManager.request({
      method: 'GET',
      url: service.url,
      headers: {
        'User-Agent': 'WorkerPool-Test/1.0',
        'Accept': 'application/json'
      }
    });
    
    const duration = Date.now() - startTime;
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    const ipInfo = service.parseResponse(data);
    
    // 获取工作池状态
    let stats = null;
    try {
      // 尝试获取网络管理器的客户端统计
      const networkStats = await networkManager.getStats();

      // 如果是gateway模式，尝试获取工作池统计
      if (networkManager.client && typeof networkManager.client.getGateway === 'function') {
        const gateway = networkManager.client.getGateway();
        if (gateway && typeof gateway.getSingboxController === 'function') {
          const controller = gateway.getSingboxController();
          if (controller && typeof controller.getStats === 'function') {
            stats = await controller.getStats();
          }
        }
      }

      // 如果无法获取工作池统计，使用网络管理器统计
      if (!stats) {
        stats = {
          totalWorkers: 'N/A',
          busyWorkers: 'N/A',
          idleWorkers: 'N/A',
          totalNodes: 'N/A',
          healthyNodes: 'N/A'
        };
      }
    } catch (error) {
      // 忽略统计获取错误
      stats = {
        totalWorkers: 'N/A',
        busyWorkers: 'N/A',
        idleWorkers: 'N/A',
        totalNodes: 'N/A',
        healthyNodes: 'N/A'
      };
    }
    
    return {
      service: service.name,
      url: service.url,
      ipInfo,
      duration,
      workerStats: {
        totalWorkers: stats.totalWorkers,
        busyWorkers: stats.busyWorkers,
        idleWorkers: stats.idleWorkers,
        totalNodes: stats.totalNodes,
        healthyNodes: stats.healthyNodes
      },
      success: true
    };
    
  } catch (error) {
    return {
      service: service.name,
      url: service.url,
      error: error.message,
      success: false
    };
  }
}

/**
 * 并发测试多个工人
 */
async function testConcurrentWorkers(concurrency = 5) {
  log('blue', `\n🚀 测试并发工人获取IP地址 (并发数: ${concurrency})`);
  
  try {
    const promises = Array.from({ length: concurrency }, async (_, index) => {
      const serviceIndex = index % IP_SERVICES.length;
      const result = await getIpViaWorkerPool(serviceIndex);
      
      return {
        workerId: index + 1,
        ...result
      };
    });
    
    const results = await Promise.all(promises);
    
    // 分析结果
    const successResults = results.filter(r => r.success);
    const failedResults = results.filter(r => !r.success);
    
    log('green', `✅ 并发测试完成: ${successResults.length}/${results.length} 成功`);
    
    // 显示成功的结果
    if (successResults.length > 0) {
      console.log('\n📊 成功的请求结果:');
      successResults.forEach(result => {
        const ipDisplay = typeof result.ipInfo === 'string' ? 
          result.ipInfo : 
          `${result.ipInfo.ip} (${result.ipInfo.country || 'Unknown'})`;
          
        console.log(`   工人${result.workerId}: ${result.service} -> ${ipDisplay} (${result.duration}ms)`);
      });
      
      // 分析IP地址分布
      const uniqueIPs = new Set();
      successResults.forEach(result => {
        const ip = typeof result.ipInfo === 'string' ? result.ipInfo : result.ipInfo.ip;
        uniqueIPs.add(ip);
      });
      
      log('cyan', `\n🌍 IP地址分析:`);
      console.log(`   总请求数: ${successResults.length}`);
      console.log(`   唯一IP数: ${uniqueIPs.size}`);
      console.log(`   IP列表: ${Array.from(uniqueIPs).join(', ')}`);
      
      if (uniqueIPs.size > 1) {
        log('green', '✅ 检测到多个不同IP，工作池正在使用不同节点！');
      } else if (uniqueIPs.size === 1) {
        log('yellow', '⚠️  所有请求使用相同IP，可能使用了相同节点或本地网络');
      }
      
      // 显示工作池统计（如果可用）
      if (successResults.length > 0 && successResults[0].workerStats) {
        const stats = successResults[0].workerStats;
        console.log(`\n📈 工作池状态:`);
        console.log(`   总工人数: ${stats.totalWorkers || 'N/A'}`);
        console.log(`   忙碌工人: ${stats.busyWorkers || 'N/A'}`);
        console.log(`   空闲工人: ${stats.idleWorkers || 'N/A'}`);
        console.log(`   健康节点: ${stats.healthyNodes || 'N/A'}/${stats.totalNodes || 'N/A'}`);
      }
    }
    
    // 显示失败的结果
    if (failedResults.length > 0) {
      console.log('\n❌ 失败的请求:');
      failedResults.forEach(result => {
        console.log(`   工人${result.workerId}: ${result.service} -> ${result.error}`);
      });
    }
    
    return {
      total: results.length,
      success: successResults.length,
      failed: failedResults.length,
      uniqueIPs: uniqueIPs.size,
      results: successResults
    };
    
  } catch (error) {
    log('red', `❌ 并发测试失败: ${error.message}`);
    return null;
  }
}

/**
 * 顺序测试不同服务
 */
async function testSequentialServices() {
  log('blue', '\n🔄 顺序测试不同IP服务');
  
  const results = [];
  
  for (let i = 0; i < IP_SERVICES.length; i++) {
    const service = IP_SERVICES[i];
    log('cyan', `\n   测试服务 ${i + 1}/${IP_SERVICES.length}: ${service.name}`);
    
    const result = await getIpViaWorkerPool(i);
    results.push(result);
    
    if (result.success) {
      const ipDisplay = typeof result.ipInfo === 'string' ? 
        result.ipInfo : 
        `${result.ipInfo.ip} (${result.ipInfo.country || 'Unknown'})`;
        
      log('green', `   ✅ ${service.name}: ${ipDisplay} (${result.duration}ms)`);
    } else {
      log('red', `   ❌ ${service.name}: ${result.error}`);
    }
    
    // 间隔1秒，让工人有时间切换节点
    if (i < IP_SERVICES.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  return results;
}

/**
 * 测试直连模式对比
 */
async function testDirectConnection() {
  log('blue', '\n🌐 测试直连模式对比');
  
  try {
    const service = IP_SERVICES[0]; // 使用ipify
    
    log('cyan', `   正在直连请求 ${service.name}`);
    
    const startTime = Date.now();
    const response = await fetch(service.url, {
      headers: {
        'User-Agent': 'Direct-Test/1.0',
        'Accept': 'application/json'
      }
    });
    
    const duration = Date.now() - startTime;
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    const ip = service.parseResponse(data);
    
    log('green', `   ✅ 直连IP: ${ip} (${duration}ms)`);
    
    return { ip, duration, success: true };
    
  } catch (error) {
    log('red', `   ❌ 直连失败: ${error.message}`);
    return { error: error.message, success: false };
  }
}

/**
 * 主测试函数
 */
async function runIpTests() {
  log('cyan', '🧪 工作池IP地址测试开始');
  log('cyan', '================================');
  
  // 检查配置
  const { configAdapter } = require('./src/gateway/adapters/ConfigAdapter');
  const config = configAdapter.getConfig();
  
  console.log('📋 测试配置:');
  console.log(`   网络模式: ${config.NETWORK_MODE}`);
  console.log(`   网关启用: ${config.ENABLE_SINGBOX_GATEWAY}`);
  console.log(`   工作池大小: ${config.SINGBOX_WORKER_POOL_SIZE}`);
  console.log(`   端口范围: ${config.SINGBOX_WORKER_PORT_START}-${config.SINGBOX_WORKER_PORT_START + config.SINGBOX_WORKER_POOL_SIZE - 1}`);
  
  if (config.NETWORK_MODE !== 'gateway' || !config.ENABLE_SINGBOX_GATEWAY) {
    log('yellow', '⚠️  警告: 当前配置未启用工作池模式');
  }
  
  const testResults = {
    sequential: null,
    concurrent: null,
    direct: null
  };
  
  try {
    // 1. 测试直连模式
    testResults.direct = await testDirectConnection();
    
    // 2. 顺序测试
    testResults.sequential = await testSequentialServices();
    
    // 3. 并发测试
    testResults.concurrent = await testConcurrentWorkers(5);
    
    // 输出总结
    log('cyan', '\n📊 测试结果总结');
    log('cyan', '================================');
    
    if (testResults.direct && testResults.direct.success) {
      console.log(`直连IP: ${testResults.direct.ip}`);
    }
    
    if (testResults.concurrent) {
      console.log(`并发测试: ${testResults.concurrent.success}/${testResults.concurrent.total} 成功`);
      console.log(`唯一IP数: ${testResults.concurrent.uniqueIPs}`);
      
      if (testResults.concurrent.uniqueIPs > 1) {
        log('green', '🎉 工作池正在使用多个不同节点！');
      } else {
        log('yellow', '⚠️  工作池可能使用了相同节点或存在配置问题');
      }
    }
    
    const successfulTests = Object.values(testResults).filter(r => r && (r.success || (r.success !== undefined && r.success > 0))).length;
    const totalTests = Object.keys(testResults).length;
    
    if (successfulTests === totalTests) {
      log('green', '🎉 所有测试完成！工作池IP测试成功。');
    } else {
      log('yellow', `⚠️  ${successfulTests}/${totalTests} 个测试成功，请检查网络和配置。`);
    }
    
  } catch (error) {
    log('red', `❌ 测试运行失败: ${error.message}`);
    console.error(error);
  }
}

// 运行测试
if (require.main === module) {
  runIpTests().catch(error => {
    log('red', '❌ 测试脚本执行失败:');
    console.error(error);
    process.exit(1);
  });
}

module.exports = {
  getIpViaWorkerPool,
  testConcurrentWorkers,
  testSequentialServices,
  testDirectConnection,
  runIpTests
};
