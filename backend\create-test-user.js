#!/usr/bin/env node

/**
 * 创建测试用户脚本
 * 在数据库中直接插入测试用户，绕过邮箱验证
 */

require('dotenv').config();
const { Pool } = require('pg');
const crypto = require('crypto');

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 密码哈希函数（与应用中的bcrypt函数一致）
function hashPassword(password) {
  const data = password + process.env.JWT_SECRET;
  const hash = crypto.createHash('sha256').update(data).digest();
  return Buffer.from(hash).toString('base64');
}

// 创建测试用户
async function createTestUser() {
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL
  });

  try {
    log('blue', '🧪 开始创建测试用户...');
    
    // 测试用户信息
    const testUsers = [
      {
        username: 'admin',
        password: 'admin',
        email: '<EMAIL>',
        isAdmin: true
      },
      {
        username: 'testuser',
        password: 'testpass',
        email: '<EMAIL>',
        isAdmin: false
      }
    ];

    for (const user of testUsers) {
      log('blue', `\n👤 创建用户: ${user.username}`);
      
      // 检查用户是否已存在
      const existingUser = await pool.query(
        'SELECT username FROM users WHERE username = $1 OR email = $2',
        [user.username, user.email]
      );

      if (existingUser.rows.length > 0) {
        log('yellow', `⚠️  用户 ${user.username} 已存在，跳过创建`);
        continue;
      }

      // 生成密码哈希
      const passwordHash = hashPassword(user.password);
      log('blue', `🔐 密码哈希: ${passwordHash.substring(0, 20)}...`);

      // 初始化用户数据
      const vipInfo = {
        type: user.isAdmin ? 'admin' : null,
        expireAt: user.isAdmin ? Date.now() + (365 * 24 * 60 * 60 * 1000) : 0, // 管理员1年有效期
        quotaChars: user.isAdmin ? 1000000 : 10000, // 管理员100万字符，普通用户1万字符
        usedChars: 0
      };

      const usageStats = {
        totalChars: 0,
        monthlyChars: 0,
        monthlyResetAt: getNextMonthResetTimestamp()
      };

      // 插入用户到数据库
      await pool.query(`
        INSERT INTO users (
          username, 
          password_hash, 
          email, 
          vip_info, 
          usage_stats,
          created_at
        ) VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
      `, [
        user.username,
        passwordHash,
        user.email,
        JSON.stringify(vipInfo),
        JSON.stringify(usageStats)
      ]);

      log('green', `✅ 用户 ${user.username} 创建成功`);
      log('green', `   📧 邮箱: ${user.email}`);
      log('green', `   🔑 密码: ${user.password}`);
      log('green', `   👑 管理员: ${user.isAdmin ? '是' : '否'}`);
      log('green', `   📊 配额: ${vipInfo.quotaChars.toLocaleString()} 字符`);
    }

    // 验证创建结果
    log('blue', '\n📋 验证创建结果...');
    const result = await pool.query('SELECT username, email, created_at FROM users ORDER BY created_at DESC');
    
    log('green', '\n✅ 数据库中的用户列表:');
    result.rows.forEach((row, index) => {
      log('green', `   ${index + 1}. ${row.username} (${row.email}) - 创建于: ${row.created_at}`);
    });

    log('blue', '\n🧪 测试登录信息:');
    log('blue', '================================');
    testUsers.forEach(user => {
      log('blue', `👤 用户名: ${user.username}`);
      log('blue', `🔑 密码: ${user.password}`);
      log('blue', `📧 邮箱: ${user.email}`);
      log('blue', `👑 管理员: ${user.isAdmin ? '是' : '否'}`);
      log('blue', '--------------------------------');
    });

    log('green', '\n🎉 测试用户创建完成！');
    log('green', '\n📋 下一步操作:');
    log('green', '   1. 启动TTS应用: npm run dev');
    log('green', '   2. 测试登录: POST /api/auth/login');
    log('green', '   3. 使用用户名和密码进行登录测试');

  } catch (error) {
    log('red', `❌ 创建测试用户失败: ${error.message}`);
    console.error('详细错误:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// 获取下个月重置时间戳
function getNextMonthResetTimestamp() {
  const now = new Date();
  const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
  return nextMonth.getTime();
}

// 测试密码哈希
async function testPasswordHash() {
  log('blue', '\n🔍 测试密码哈希功能...');
  
  const testPassword = 'admin';
  const hash = hashPassword(testPassword);
  
  log('blue', `密码: ${testPassword}`);
  log('blue', `JWT_SECRET: ${process.env.JWT_SECRET?.substring(0, 20)}...`);
  log('blue', `哈希结果: ${hash}`);
  
  // 验证哈希一致性
  const hash2 = hashPassword(testPassword);
  if (hash === hash2) {
    log('green', '✅ 密码哈希一致性验证通过');
  } else {
    log('red', '❌ 密码哈希一致性验证失败');
  }
}

// 主函数
async function main() {
  log('blue', '🚀 TTS应用测试用户创建工具');
  log('blue', '================================');
  
  // 检查环境变量
  if (!process.env.DATABASE_URL) {
    log('red', '❌ 错误: DATABASE_URL 环境变量未设置');
    process.exit(1);
  }
  
  if (!process.env.JWT_SECRET) {
    log('red', '❌ 错误: JWT_SECRET 环境变量未设置');
    process.exit(1);
  }

  // 测试密码哈希
  await testPasswordHash();
  
  // 创建测试用户
  await createTestUser();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { createTestUser, hashPassword };
