"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[150],{2712:(e,t,n)=>{n.d(t,{N:()=>i});var r=n(2115),i=globalThis?.document?r.useLayoutEffect:()=>{}},3655:(e,t,n)=>{n.d(t,{hO:()=>l,sG:()=>s});var r=n(2115),i=n(7650),u=n(9708),o=n(5155),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=r.forwardRef((e,n)=>{let{asChild:r,...i}=e,s=r?u.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(s,{...i,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function l(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},4378:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(2115),i=n(7650),u=n(3655),o=n(2712),s=n(5155),l=r.forwardRef((e,t)=>{var n,l;let{container:a,...d}=e,[c,f]=r.useState(!1);(0,o.N)(()=>f(!0),[]);let v=a||c&&(null===(l=globalThis)||void 0===l?void 0:null===(n=l.document)||void 0===n?void 0:n.body);return v?i.createPortal((0,s.jsx)(u.sG.div,{...d,ref:t}),v):null});l.displayName="Portal"},4416:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},5845:(e,t,n)=>{n.d(t,{i:()=>u});var r=n(2115),i=n(9033);function u({prop:e,defaultProp:t,onChange:n=()=>{}}){let[u,o]=function({defaultProp:e,onChange:t}){let n=r.useState(e),[u]=n,o=r.useRef(u),s=(0,i.c)(t);return r.useEffect(()=>{o.current!==u&&(s(u),o.current=u)},[u,o,s]),n}({defaultProp:t,onChange:n}),s=void 0!==e,l=s?e:u,a=(0,i.c)(n);return[l,r.useCallback(t=>{if(s){let n="function"==typeof t?t(e):t;n!==e&&a(n)}else o(t)},[s,e,o,a])]}},6081:(e,t,n)=>{n.d(t,{A:()=>o,q:()=>u});var r=n(2115),i=n(5155);function u(e,t){let n=r.createContext(t),u=e=>{let{children:t,...u}=e,o=r.useMemo(()=>u,Object.values(u));return(0,i.jsx)(n.Provider,{value:o,children:t})};return u.displayName=e+"Provider",[u,function(i){let u=r.useContext(n);if(u)return u;if(void 0!==t)return t;throw Error(`\`${i}\` must be used within \`${e}\``)}]}function o(e,t=[]){let n=[],u=()=>{let t=n.map(e=>r.createContext(e));return function(n){let i=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:i}}),[n,i])}};return u.scopeName=e,[function(t,u){let o=r.createContext(u),s=n.length;n=[...n,u];let l=t=>{let{scope:n,children:u,...l}=t,a=n?.[e]?.[s]||o,d=r.useMemo(()=>l,Object.values(l));return(0,i.jsx)(a.Provider,{value:d,children:u})};return l.displayName=t+"Provider",[l,function(n,i){let l=i?.[e]?.[s]||o,a=r.useContext(l);if(a)return a;if(void 0!==u)return u;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=n.reduce((t,{useScope:n,scopeName:r})=>{let i=n(e)[`__scope${r}`];return{...t,...i}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}(u,...t)]}},8905:(e,t,n)=>{n.d(t,{C:()=>o});var r=n(2115),i=n(6101),u=n(2712),o=e=>{let{present:t,children:n}=e,o=function(e){var t,n;let[i,o]=r.useState(),l=r.useRef({}),a=r.useRef(e),d=r.useRef("none"),[c,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=s(l.current);d.current="mounted"===c?e:"none"},[c]),(0,u.N)(()=>{let t=l.current,n=a.current;if(n!==e){let r=d.current,i=s(t);e?f("MOUNT"):"none"===i||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==i?f("ANIMATION_OUT"):f("UNMOUNT"),a.current=e}},[e,f]),(0,u.N)(()=>{if(i){var e;let t;let n=null!==(e=i.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=s(l.current).includes(e.animationName);if(e.target===i&&r&&(f("ANIMATION_END"),!a.current)){let e=i.style.animationFillMode;i.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=e)})}},u=e=>{e.target===i&&(d.current=s(l.current))};return i.addEventListener("animationstart",u),i.addEventListener("animationcancel",r),i.addEventListener("animationend",r),()=>{n.clearTimeout(t),i.removeEventListener("animationstart",u),i.removeEventListener("animationcancel",r),i.removeEventListener("animationend",r)}}f("ANIMATION_END")},[i,f]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{e&&(l.current=getComputedStyle(e)),o(e)},[])}}(t),l="function"==typeof n?n({present:o.isPresent}):r.Children.only(n),a=(0,i.s)(o.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,i=r&&"isReactWarning"in r&&r.isReactWarning;return i?e.ref:(i=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof n||o.isPresent?r.cloneElement(l,{ref:a}):null};function s(e){return(null==e?void 0:e.animationName)||"none"}o.displayName="Presence"},9033:(e,t,n)=>{n.d(t,{c:()=>i});var r=n(2115);function i(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},9178:(e,t,n)=>{n.d(t,{lg:()=>y,qW:()=>f,bL:()=>E});var r,i=n(2115),u=n(5185),o=n(3655),s=n(6101),l=n(9033),a=n(5155),d="dismissableLayer.update",c=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=i.forwardRef((e,t)=>{var n,f;let{disableOutsidePointerEvents:v=!1,onEscapeKeyDown:E,onPointerDownOutside:y,onFocusOutside:h,onInteractOutside:w,onDismiss:b,...N}=e,g=i.useContext(c),[O,C]=i.useState(null),L=null!==(f=null==O?void 0:O.ownerDocument)&&void 0!==f?f:null===(n=globalThis)||void 0===n?void 0:n.document,[,P]=i.useState({}),T=(0,s.s)(t,e=>C(e)),D=Array.from(g.layers),[k]=[...g.layersWithOutsidePointerEventsDisabled].slice(-1),M=D.indexOf(k),x=O?D.indexOf(O):-1,R=g.layersWithOutsidePointerEventsDisabled.size>0,A=x>=M,S=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,l.c)(e),u=i.useRef(!1),o=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!u.current){let t=function(){p("dismissableLayer.pointerDownOutside",r,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",o.current),o.current=t,n.addEventListener("click",o.current,{once:!0})):t()}else n.removeEventListener("click",o.current);u.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",o.current)}},[n,r]),{onPointerDownCapture:()=>u.current=!0}}(e=>{let t=e.target,n=[...g.branches].some(e=>e.contains(t));!A||n||(null==y||y(e),null==w||w(e),e.defaultPrevented||null==b||b())},L),_=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,l.c)(e),u=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!u.current&&p("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>u.current=!0,onBlurCapture:()=>u.current=!1}}(e=>{let t=e.target;[...g.branches].some(e=>e.contains(t))||(null==h||h(e),null==w||w(e),e.defaultPrevented||null==b||b())},L);return!function(e,t=globalThis?.document){let n=(0,l.c)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{x===g.layers.size-1&&(null==E||E(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},L),i.useEffect(()=>{if(O)return v&&(0===g.layersWithOutsidePointerEventsDisabled.size&&(r=L.body.style.pointerEvents,L.body.style.pointerEvents="none"),g.layersWithOutsidePointerEventsDisabled.add(O)),g.layers.add(O),m(),()=>{v&&1===g.layersWithOutsidePointerEventsDisabled.size&&(L.body.style.pointerEvents=r)}},[O,L,v,g]),i.useEffect(()=>()=>{O&&(g.layers.delete(O),g.layersWithOutsidePointerEventsDisabled.delete(O),m())},[O,g]),i.useEffect(()=>{let e=()=>P({});return document.addEventListener(d,e),()=>document.removeEventListener(d,e)},[]),(0,a.jsx)(o.sG.div,{...N,ref:T,style:{pointerEvents:R?A?"auto":"none":void 0,...e.style},onFocusCapture:(0,u.m)(e.onFocusCapture,_.onFocusCapture),onBlurCapture:(0,u.m)(e.onBlurCapture,_.onBlurCapture),onPointerDownCapture:(0,u.m)(e.onPointerDownCapture,S.onPointerDownCapture)})});f.displayName="DismissableLayer";var v=i.forwardRef((e,t)=>{let n=i.useContext(c),r=i.useRef(null),u=(0,s.s)(t,r);return i.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,a.jsx)(o.sG.div,{...e,ref:u})});function m(){let e=new CustomEvent(d);document.dispatchEvent(e)}function p(e,t,n,r){let{discrete:i}=r,u=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&u.addEventListener(e,t,{once:!0}),i?(0,o.hO)(u,s):u.dispatchEvent(s)}v.displayName="DismissableLayerBranch";var E=f,y=v},9946:(e,t,n)=>{n.d(t,{A:()=>l});var r=n(2115);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),u=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:i=24,strokeWidth:s=2,absoluteStrokeWidth:l,className:a="",children:d,iconNode:c,...f}=e;return(0,r.createElement)("svg",{ref:t,...o,width:i,height:i,stroke:n,strokeWidth:l?24*Number(s)/Number(i):s,className:u("lucide",a),...f},[...c.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(d)?d:[d]])}),l=(e,t)=>{let n=(0,r.forwardRef)((n,o)=>{let{className:l,...a}=n;return(0,r.createElement)(s,{ref:o,iconNode:t,className:u("lucide-".concat(i(e)),l),...a})});return n.displayName="".concat(e),n}}}]);