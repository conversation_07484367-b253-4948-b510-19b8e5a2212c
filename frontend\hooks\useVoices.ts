/**
 * 声音数据管理的自定义Hook
 * 处理声音数据的异步加载、错误处理和重试机制
 */

import { useState, useEffect, useCallback } from 'react';
import { Voice, VoicesError, VoicesState } from '@/lib/types/voices';

// 声音图标数组 - 从原代码迁移
const voiceIcons = [
  "https://eleven-public-cdn.elevenlabs.io/payloadcms/ox7fne3bkeo-brian.jpg",
  "https://eleven-public-cdn.elevenlabs.io/payloadcms/twhwqss70ic-alice.jpg",
  "https://eleven-public-cdn.elevenlabs.io/payloadcms/4vvqikmli2m-bill.jpg",
  "https://eleven-public-cdn.elevenlabs.io/payloadcms/gwb2kbm395-callum.jpg",
  "https://eleven-public-cdn.elevenlabs.io/payloadcms/ixo4og3542i-charlie.jpg",
  "https://eleven-public-cdn.elevenlabs.io/payloadcms/zw9ec3ktkch-charlotte.jpg",
  "https://eleven-public-cdn.elevenlabs.io/payloadcms/xu3c1krvtn-chris.jpg",
  "https://eleven-public-cdn.elevenlabs.io/payloadcms/toavylo6g7-daniel.jpg"
];

// Fisher-Yates 洗牌算法 - 从原代码迁移
function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

// 创建错误对象的辅助函数
function createVoicesError(type: VoicesError['type'], message: string, retryable: boolean = true): VoicesError {
  return { type, message, retryable };
}

export function useVoices() {
  const [state, setState] = useState<VoicesState>({
    voices: [],
    isLoading: true,
    error: null,
    voiceIconMapping: {}
  });

  // 客户端挂载状态 - 解决水合失败问题
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // 获取配置的URL，支持环境变量和fallback
  const getVoicesUrl = useCallback(() => {
    return process.env.NEXT_PUBLIC_VOICES_JSON_URL || 
           "https://my-tts-config-1256990318.cos.ap-guangzhou.myqcloud.com/voices.json";
  }, []);

  // 生成声音图标映射 - 只在客户端执行以避免水合失败
  const generateVoiceIconMapping = useCallback((voices: Voice[]): Record<string, string> => {
    // 如果不在客户端，返回空映射
    if (!isClient) {
      return {};
    }

    const shuffledIcons = shuffleArray(voiceIcons);
    const mapping: Record<string, string> = {};
    voices.forEach((voice, index) => {
      mapping[voice.id] = shuffledIcons[index % shuffledIcons.length];
    });
    return mapping;
  }, [isClient]);

  // 获取声音数据的核心函数
  const fetchVoices = useCallback(async (): Promise<void> => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      const url = getVoicesUrl();
      // 添加缓存控制：确保每次都验证服务器文件是否更新
      // 如果文件未变返回304使用缓存，如果已更新返回200下载新内容
      const response = await fetch(url, {
        cache: 'no-cache'
      });

      if (!response.ok) {
        throw createVoicesError(
          'network',
          `无法获取声音列表: ${response.status} ${response.statusText}`,
          true
        );
      }

      const data: Voice[] = await response.json();

      // 验证数据格式
      if (!Array.isArray(data)) {
        throw createVoicesError(
          'parse',
          '声音数据格式错误：期望数组格式',
          true
        );
      }

      if (data.length === 0) {
        throw createVoicesError(
          'empty',
          '声音列表为空，请联系管理员',
          true
        );
      }

      // 验证每个声音对象的必要字段
      const invalidVoices = data.filter(voice => 
        !voice.id || !voice.name || !voice.gender || !voice.description
      );

      if (invalidVoices.length > 0) {
        console.warn('发现无效的声音数据:', invalidVoices);
      }

      // 生成图标映射
      const voiceIconMapping = generateVoiceIconMapping(data);

      setState({
        voices: data,
        isLoading: false,
        error: null,
        voiceIconMapping
      });

    } catch (error: any) {
      console.error('获取声音数据失败:', error);
      
      let voicesError: VoicesError;
      
      if (error instanceof TypeError && error.message.includes('fetch')) {
        voicesError = createVoicesError(
          'network',
          '网络连接失败，请检查网络后重试',
          true
        );
      } else if (error.type) {
        voicesError = error; // 已经是VoicesError类型
      } else {
        voicesError = createVoicesError(
          'unknown',
          error.message || '加载声音配置时发生未知错误',
          true
        );
      }

      setState(prev => ({
        ...prev,
        isLoading: false,
        error: voicesError
      }));
    }
  }, [getVoicesUrl, generateVoiceIconMapping]);

  // 重试函数
  const retry = useCallback(() => {
    if (state.error?.retryable) {
      fetchVoices();
    }
  }, [state.error?.retryable, fetchVoices]);

  // 初始化加载
  useEffect(() => {
    fetchVoices();
  }, [fetchVoices]);

  // 客户端挂载后重新生成图标映射 - 解决水合失败问题
  useEffect(() => {
    if (isClient && state.voices.length > 0 && Object.keys(state.voiceIconMapping).length === 0) {
      const voiceIconMapping = generateVoiceIconMapping(state.voices);
      setState(prev => ({
        ...prev,
        voiceIconMapping
      }));
    }
  }, [isClient, state.voices, state.voiceIconMapping, generateVoiceIconMapping]);

  return {
    ...state,
    retry,
    refetch: fetchVoices
  };
}
