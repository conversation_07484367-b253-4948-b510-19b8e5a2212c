// 测试腾讯云SES配置的脚本
// 在Cloudflare Worker中运行此代码来验证配置

export default {
  async fetch(request, env) {
    // 检查环境变量
    const config = {
      TENCENT_SECRET_ID: env.TENCENT_SECRET_ID,
      TENCENT_SECRET_KEY: env.TENCENT_SECRET_KEY,
      SES_REGION: env.SES_REGION,
      FROM_EMAIL: env.FROM_EMAIL,
      FROM_EMAIL_NAME: env.FROM_EMAIL_NAME,
      VERIFICATION_TEMPLATE_ID: env.VERIFICATION_TEMPLATE_ID
    };

    // 检查配置完整性
    const missingVars = [];
    Object.entries(config).forEach(([key, value]) => {
      if (!value) {
        missingVars.push(key);
      }
    });

    const response = {
      timestamp: new Date().toISOString(),
      configCheck: {
        TENCENT_SECRET_ID: config.TENCENT_SECRET_ID ? `存在 (${config.TENCENT_SECRET_ID.substring(0, 8)}...)` : '❌ 缺失',
        TENCENT_SECRET_KEY: config.TENCENT_SECRET_KEY ? `存在 (${config.TENCENT_SECRET_KEY.substring(0, 8)}...)` : '❌ 缺失',
        SES_REGION: config.SES_REGION || '❌ 缺失',
        FROM_EMAIL: config.FROM_EMAIL || '❌ 缺失',
        FROM_EMAIL_NAME: config.FROM_EMAIL_NAME || '❌ 缺失',
        VERIFICATION_TEMPLATE_ID: config.VERIFICATION_TEMPLATE_ID || '❌ 缺失'
      },
      missingVariables: missingVars,
      status: missingVars.length === 0 ? '✅ 配置完整' : '❌ 配置不完整'
    };

    return new Response(JSON.stringify(response, null, 2), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
};
