"use client"

import React, { useRef, useMemo, useEffect } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import * as THREE from 'three';

// 自然云朵流体着色器 - 模拟无方向性的云朵翻涌和扩散
const fragmentShader = `
  uniform vec3 u_color_a;
  uniform vec3 u_color_b;
  uniform vec3 u_color_c;
  uniform vec3 u_color_d;
  uniform float u_time;
  uniform float u_intensity;
  uniform float u_speed;
  uniform float u_complexity;
  varying vec2 vUv;

  // 高质量噪声函数
  float random(vec2 st) {
    return fract(sin(dot(st.xy, vec2(12.9898, 78.233))) * 43758.5453123);
  }

  float noise(vec2 st) {
    vec2 i = floor(st);
    vec2 f = fract(st);

    float a = random(i);
    float b = random(i + vec2(1.0, 0.0));
    float c = random(i + vec2(0.0, 1.0));
    float d = random(i + vec2(1.0, 1.0));

    vec2 u = f * f * (3.0 - 2.0 * f);
    return mix(a, b, u.x) + (c - a) * u.y * (1.0 - u.x) + (d - b) * u.x * u.y;
  }

  // 多层分形布朗运动 - 创建大块云雾纹理
  float fbm(vec2 st, int octaves) {
    float value = 0.0;
    float amplitude = 0.6;

    for(int i = 0; i < 8; i++) {
      if(i >= octaves) break;
      value += amplitude * noise(st);
      st *= 2.0;
      amplitude *= 0.5;
    }
    return value;
  }

  // 真随机混沌扩散函数 - 每层完全随机的运动方向
  vec2 chaoticDiffusion(vec2 p, float time, float strength, float layerOffset) {
    // 为每层创建完全不同的随机种子和方向偏移
    float seedA = layerOffset * 17.239 + 31.415;
    float seedB = layerOffset * 23.567 + 47.891;
    float seedC = layerOffset * 19.823 + 53.267;

    // 为每层创建独特的时间偏移和频率，完全随机化
    float timeA = time * (0.8 + sin(seedA) * 0.6) + seedA;
    float timeB = time * (1.0 + cos(seedB) * 0.8) + seedB;
    float timeC = time * (1.2 + sin(seedC) * 0.7) + seedC;

    // 使用随机化的空间频率和时间速度创建独立的密度场
    float density1 = fbm(p * (2.0 + sin(seedA) * 1.0) + timeA * (0.1 + cos(seedA) * 0.05), 3);
    float density2 = fbm(p * (1.5 + cos(seedB) * 0.8) + timeB * (0.08 + sin(seedB) * 0.04) + vec2(sin(seedA) * 150.0, cos(seedA) * 100.0), 4);
    float density3 = fbm(p * (2.8 + sin(seedC) * 1.2) + timeC * (0.12 + cos(seedC) * 0.06) + vec2(cos(seedB) * 200.0, sin(seedB) * 180.0), 2);
    float density4 = fbm(p * (2.2 + cos(seedA) * 0.9) + timeA * (0.07 + sin(seedC) * 0.03) + vec2(sin(seedC) * 250.0, cos(seedC) * 220.0), 3);

    // 为每层创建完全随机方向的梯度场
    float gradOffset1 = 0.015 + sin(seedA) * 0.01;
    float gradOffset2 = 0.018 + cos(seedB) * 0.012;
    float gradOffset3 = 0.012 + sin(seedC) * 0.008;

    vec2 gradient1 = vec2(
      fbm(p + vec2(gradOffset1, 0.0) + timeA * (0.012 + cos(seedA) * 0.008), 2) - fbm(p - vec2(gradOffset1, 0.0) + timeA * (0.012 + cos(seedA) * 0.008), 2),
      fbm(p + vec2(0.0, gradOffset1) + timeA * (0.01 + sin(seedA) * 0.006), 2) - fbm(p - vec2(0.0, gradOffset1) + timeA * (0.01 + sin(seedA) * 0.006), 2)
    );

    vec2 gradient2 = vec2(
      fbm(p * (1.4 + sin(seedB) * 0.4) + timeB * (0.055 + cos(seedB) * 0.02) + vec2(gradOffset2, 0.0), 3) - fbm(p * (1.4 + sin(seedB) * 0.4) + timeB * (0.055 + cos(seedB) * 0.02) - vec2(gradOffset2, 0.0), 3),
      fbm(p * (1.4 + sin(seedB) * 0.4) + timeB * (0.048 + sin(seedB) * 0.018) + vec2(0.0, gradOffset2), 3) - fbm(p * (1.4 + sin(seedB) * 0.4) + timeB * (0.048 + sin(seedB) * 0.018) - vec2(0.0, gradOffset2), 3)
    );

    vec2 gradient3 = vec2(
      fbm(p * (0.8 + cos(seedC) * 0.3) + timeC * (0.075 + sin(seedC) * 0.025) + vec2(gradOffset3, 0.0), 2) - fbm(p * (0.8 + cos(seedC) * 0.3) + timeC * (0.075 + sin(seedC) * 0.025) - vec2(gradOffset3, 0.0), 2),
      fbm(p * (0.8 + cos(seedC) * 0.3) + timeC * (0.065 + cos(seedC) * 0.02) + vec2(0.0, gradOffset3), 2) - fbm(p * (0.8 + cos(seedC) * 0.3) + timeC * (0.065 + cos(seedC) * 0.02) - vec2(0.0, gradOffset3), 2)
    );

    // 多层独立的扩散力场，每层有完全随机的运动特征
    vec2 diffusionForce = vec2(0.0);
    diffusionForce += strength * (0.35 + sin(seedA) * 0.1) * gradient1 * smoothstep(0.1, 0.9, density1);
    diffusionForce += strength * (0.28 + cos(seedB) * 0.08) * gradient2 * smoothstep(0.2, 0.8, density2);
    diffusionForce += strength * (0.25 + sin(seedC) * 0.06) * gradient3 * smoothstep(0.05, 0.95, density3);

    // 为每层添加完全随机方向的多频率湍流
    float freqX1 = 2.8 + sin(seedA) * 1.5;
    float freqY1 = 2.2 + cos(seedA) * 1.3;
    float freqX2 = 2.0 + cos(seedB) * 1.8;
    float freqY2 = 3.2 + sin(seedB) * 1.6;

    diffusionForce += strength * (0.18 + cos(seedA) * 0.08) * vec2(
      sin(p.x * freqX1 + timeA * (0.25 + sin(seedA) * 0.15)) * cos(p.y * freqY1 + timeB * (0.22 + cos(seedA) * 0.12)),
      cos(p.x * freqX2 + timeC * (0.28 + cos(seedB) * 0.18)) * sin(p.y * freqY2 + timeA * (0.24 + sin(seedB) * 0.14))
    );

    // 添加高频完全随机方向的混沌扰动
    float hiFreqX1 = 8.0 + sin(seedB) * 4.0;
    float hiFreqY1 = 7.5 + cos(seedB) * 3.5;
    float hiFreqX2 = 6.8 + cos(seedC) * 3.8;
    float hiFreqY2 = 9.2 + sin(seedC) * 4.2;

    diffusionForce += strength * (0.12 + sin(seedB) * 0.06) * vec2(
      sin(p.x * hiFreqX1 + timeB * (0.42 + cos(seedB) * 0.18)) * cos(p.y * hiFreqY1 + timeC * (0.35 + sin(seedB) * 0.15)),
      cos(p.x * hiFreqX2 + timeA * (0.48 + sin(seedC) * 0.22)) * sin(p.y * hiFreqY2 + timeB * (0.38 + cos(seedC) * 0.16))
    );

    // 添加超高频完全随机的微扰动
    float ultraFreqX1 = 14.0 + cos(seedC) * 8.0;
    float ultraFreqY1 = 11.5 + sin(seedC) * 6.5;
    float ultraFreqX2 = 16.8 + sin(seedA) * 7.2;
    float ultraFreqY2 = 13.2 + cos(seedA) * 5.8;

    diffusionForce += strength * (0.06 + cos(seedC) * 0.04) * vec2(
      sin(p.x * ultraFreqX1 + timeC * (0.65 + sin(seedC) * 0.25)) * cos(p.y * ultraFreqY1 + timeA * (0.68 + cos(seedC) * 0.28)),
      cos(p.x * ultraFreqX2 + timeB * (0.72 + cos(seedA) * 0.32)) * sin(p.y * ultraFreqY2 + timeC * (0.66 + sin(seedA) * 0.26))
    );

    return p + diffusionForce;
  }

  void main() {
    vec2 p = vUv;
    float time = u_time * u_speed;

    // 创建多层完全随机的混沌扩散，每层都有独立的随机运动
    vec2 diffused1 = chaoticDiffusion(p, time, 0.18 * u_complexity, 0.0);
    vec2 diffused2 = chaoticDiffusion(p + vec2(0.31, 0.69), time, 0.16 * u_complexity, 1.0);
    vec2 diffused3 = chaoticDiffusion(p + vec2(0.87, 0.13), time, 0.14 * u_complexity, 2.0);
    vec2 diffused4 = chaoticDiffusion(p + vec2(0.24, 0.76), time, 0.12 * u_complexity, 3.0);
    vec2 diffused5 = chaoticDiffusion(p + vec2(0.58, 0.42), time, 0.10 * u_complexity, 4.0);

    // 为每层云朵创建独立的随机时间和空间参数
    float timeOffset1 = time * (0.085 + sin(13.7) * 0.03) + cos(17.3) * 5.0;
    float timeOffset2 = time * (0.072 + cos(19.2) * 0.025) + sin(23.8) * 7.0;
    float timeOffset3 = time * (0.098 + sin(29.4) * 0.035) + cos(31.6) * 9.0;
    float timeOffset4 = time * (0.063 + cos(37.1) * 0.022) + sin(41.9) * 11.0;
    float timeOffset5 = time * (0.081 + sin(43.7) * 0.028) + cos(47.2) * 13.0;

    // 生成多层完全随机变化的云雾噪声
    float cloud1 = fbm(diffused1 * (1.9 + sin(11.3) * 0.4) + timeOffset1, 4);
    float cloud2 = fbm(diffused2 * (1.5 + cos(15.7) * 0.3) + timeOffset2 + vec2(sin(12.4) * 80.0, cos(18.9) * 60.0), 3);
    float cloud3 = fbm(diffused3 * (2.3 + sin(21.8) * 0.5) + timeOffset3 + vec2(cos(25.3) * 120.0, sin(28.7) * 90.0), 5);
    float cloud4 = fbm(diffused4 * (1.7 + cos(33.2) * 0.35) + timeOffset4 + vec2(sin(36.8) * 160.0, cos(39.4) * 130.0), 3);
    float cloud5 = fbm(diffused5 * (2.1 + sin(42.6) * 0.45) + timeOffset5 + vec2(cos(45.1) * 200.0, sin(48.9) * 170.0), 4);

    // 添加多层完全随机的高频混沌噪声
    float detailTime1 = time * (0.115 + cos(52.3) * 0.04) + sin(55.7) * 15.0;
    float detailTime2 = time * (0.098 + sin(59.1) * 0.035) + cos(62.8) * 17.0;
    float detailTime3 = time * (0.132 + cos(66.4) * 0.045) + sin(69.9) * 19.0;

    float detailNoise1 = fbm(p * (7.2 + sin(71.2) * 1.5) + detailTime1 + vec2(cos(74.6) * 320.0, sin(77.3) * 280.0), 2) * (0.32 + cos(79.8) * 0.08);
    float detailNoise2 = fbm(p * (9.4 + cos(82.1) * 2.0) + detailTime2 + vec2(sin(85.7) * 420.0, cos(88.3) * 380.0), 2) * (0.26 + sin(91.4) * 0.06);
    float detailNoise3 = fbm(p * (10.8 + sin(94.9) * 1.8) + detailTime3 + vec2(cos(97.2) * 520.0, sin(99.8) * 480.0), 2) * (0.20 + cos(102.5) * 0.05);

    // 创建高度混沌的密度分布，每层独立变化
    float density1 = smoothstep(0.1, 0.9, cloud1 + detailNoise1 * 0.8);
    float density2 = smoothstep(0.15, 0.85, cloud2 + detailNoise2 * 0.7);
    float density3 = smoothstep(0.08, 0.92, cloud3 + detailNoise3 * 0.9);
    float density4 = smoothstep(0.2, 0.8, cloud4 + detailNoise1 * 0.6);
    float density5 = smoothstep(0.12, 0.88, cloud5 + detailNoise2 * 0.5);

    // 计算整体云朵密度，包含所有5层的复杂交互
    float totalDensity = (density1 + density2 + density3 + density4 + density5) / 5.0;

    // 添加多频率时间相关的密度调制，创造极度混沌的变化
    float densityModulation = 0.12 * sin(time * 0.25 + totalDensity * 8.0)
                            + 0.09 * cos(time * 0.38 + density1 * 6.0)
                            + 0.07 * sin(time * 0.31 + density2 * 7.0)
                            + 0.06 * cos(time * 0.42 + density3 * 5.5)
                            + 0.05 * sin(time * 0.29 + density4 * 6.5)
                            + 0.04 * cos(time * 0.35 + density5 * 7.5);

    // 添加基于位置的混沌调制
    float spatialChaos = 0.08 * sin(p.x * 12.0 + time * 0.45) * cos(p.y * 10.5 + time * 0.38)
                       + 0.06 * cos(p.x * 15.2 + time * 0.52) * sin(p.y * 13.8 + time * 0.41);

    totalDensity = clamp(totalDensity + densityModulation + spatialChaos, 0.0, 1.0);

    // 创建自然的边缘衰减，模拟云朵向外的自然消散
    vec2 centerDist = p - 0.5;
    float distFromCenter = length(centerDist);

    // 使用平滑的径向衰减，而非硬边界
    float naturalFade = 1.0 - smoothstep(0.3, 0.7, distFromCenter);
    naturalFade = mix(0.4, 1.0, naturalFade); // 保持一定的最小透明度

    // 添加基于密度的额外衰减，让稀薄区域更透明
    float densityFade = smoothstep(0.1, 0.6, totalDensity);
    naturalFade *= mix(0.6, 1.0, densityFade);

    // 创建极度混沌的颜色过渡系统
    vec3 lightColor = mix(u_color_a, u_color_b, density2 * 0.8 + 0.2 + detailNoise1 * 0.25);
    vec3 darkColor = mix(u_color_c, u_color_d, density1 * 0.6 + 0.4 + detailNoise2 * 0.2);
    vec3 midColor1 = mix(lightColor, darkColor, 0.4 + density4 * 0.4 + detailNoise3 * 0.15);
    vec3 midColor2 = mix(u_color_b, u_color_c, 0.6 + density5 * 0.3 + detailNoise1 * 0.18);

    // 使用多层复杂的颜色混合，每层独立变化
    float colorMix1 = smoothstep(0.1, 0.9, totalDensity + detailNoise1 * 0.12);
    float colorMix2 = smoothstep(0.05, 0.95, density3 + detailNoise2 * 0.1);
    float colorMix3 = smoothstep(0.15, 0.85, density5 + detailNoise3 * 0.08);

    // 多阶段颜色混合，创造复杂的色彩变化
    vec3 baseColor = mix(lightColor, darkColor, colorMix1);
    vec3 blendColor1 = mix(baseColor, midColor1, colorMix2 * 0.5);
    vec3 finalColor = mix(blendColor1, midColor2, colorMix3 * 0.3);

    // 应用高度动态的亮度变化，模拟混沌光照
    float naturalBrightness = 0.8 + 0.25 * smoothstep(0.2, 0.8, totalDensity);
    naturalBrightness += 0.15 * (density3 - 0.5) + 0.12 * (density4 - 0.5) + 0.1 * (density5 - 0.5);

    // 添加多频率的混沌亮度扰动
    float brightnessNoise = 0.08 * sin(time * 0.45 + totalDensity * 10.0)
                          + 0.06 * cos(time * 0.52 + density1 * 8.0)
                          + 0.05 * sin(time * 0.38 + density2 * 9.0)
                          + 0.04 * cos(time * 0.61 + density3 * 7.5)
                          + 0.03 * sin(time * 0.47 + density4 * 8.5);

    // 添加基于空间位置的混沌亮度变化
    float spatialBrightness = 0.06 * sin(p.x * 18.0 + time * 0.68) * cos(p.y * 16.5 + time * 0.72)
                            + 0.04 * cos(p.x * 22.3 + time * 0.75) * sin(p.y * 19.8 + time * 0.69);

    naturalBrightness += brightnessNoise + spatialBrightness;

    // 应用强度和自然衰减
    finalColor *= u_intensity * naturalFade * naturalBrightness;

    gl_FragColor = vec4(finalColor, naturalFade);
  }
`;

const vertexShader = `
  varying vec2 vUv;
  void main() {
    vUv = uv;
    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
  }
`;

interface AudioAuroraPlaneProps {
  colorA?: string;
  colorB?: string;
  colorC?: string;
  colorD?: string;
  intensity?: number;
  speed?: number;
  complexity?: number;
  isActive?: boolean;
}

function AudioAuroraPlane({ 
  colorA = "#1e3a8a", // 深蓝
  colorB = "#3b82f6", // 蓝色
  colorC = "#8b5cf6", // 紫色
  colorD = "#ec4899", // 粉色
  intensity = 1.0,
  speed = 1.0,
  complexity = 1.0,
  isActive = true
}: AudioAuroraPlaneProps) {
  const materialRef = useRef<THREE.ShaderMaterial>(null);

  useFrame(({ clock }) => {
    if (materialRef.current && isActive) {
      materialRef.current.uniforms.u_time.value = clock.getElapsedTime();
    }
  });

  const uniforms = useMemo(() => ({
    u_time: { value: 0.0 },
    u_intensity: { value: intensity },
    u_speed: { value: speed },
    u_complexity: { value: complexity },
    u_color_a: { value: new THREE.Color(colorA) },
    u_color_b: { value: new THREE.Color(colorB) },
    u_color_c: { value: new THREE.Color(colorC) },
    u_color_d: { value: new THREE.Color(colorD) },
  }), [colorA, colorB, colorC, colorD, intensity, speed, complexity]);

  // 动态更新uniforms
  useEffect(() => {
    if (materialRef.current) {
      materialRef.current.uniforms.u_intensity.value = intensity;
      materialRef.current.uniforms.u_speed.value = speed;
      materialRef.current.uniforms.u_complexity.value = complexity;
    }
  }, [intensity, speed, complexity]);

  return (
    <mesh>
      <planeGeometry args={[2, 2]} />
      <shaderMaterial
        ref={materialRef}
        fragmentShader={fragmentShader}
        vertexShader={vertexShader}
        uniforms={uniforms}
      />
    </mesh>
  );
}

interface AudioAuroraEffectProps {
  colorA?: string;
  colorB?: string;
  colorC?: string;
  colorD?: string;
  intensity?: number;
  speed?: number;
  complexity?: number;
  isPlaying?: boolean;
  className?: string;
}

export default function AudioAuroraEffect({
  colorA = "#1e3a8a", // 深蓝
  colorB = "#3b82f6", // 蓝色  
  colorC = "#8b5cf6", // 紫色
  colorD = "#ec4899", // 粉色
  intensity = 1.0,
  speed = 1.0,
  complexity = 1.0,
  isPlaying = false,
  className = ""
}: AudioAuroraEffectProps) {
  const [hasWebGLError, setHasWebGLError] = React.useState(false);

  // 根据播放状态动态调整参数
  const dynamicIntensity = isPlaying ? intensity * 1.5 : intensity * 0.7;
  const dynamicSpeed = isPlaying ? speed * 1.8 : speed * 0.5;
  const dynamicComplexity = isPlaying ? complexity * 1.2 : complexity * 0.8;

  // CSS降级方案
  const fallbackStyle = {
    background: `linear-gradient(45deg, ${colorA}, ${colorB}, ${colorC}, ${colorD})`,
    backgroundSize: '400% 400%',
    animation: `aurora-fallback ${isPlaying ? '3s' : '6s'} ease-in-out infinite`
  };

  if (hasWebGLError) {
    return (
      <div
        className={`w-full h-full ${className}`}
        style={fallbackStyle}
      />
    );
  }

  return (
    <div className={`w-full h-full ${className}`}>
      <Canvas
        orthographic
        camera={{
          position: [0, 0, 1],
          left: -1,
          right: 1,
          top: 1,
          bottom: -1,
          near: 0.1,
          far: 10
        }}
        style={{ width: '100%', height: '100%' }}
        gl={{
          antialias: true,
          alpha: true,
          powerPreference: "high-performance"
        }}
        onCreated={({ gl }) => {
          if (!gl.getContext()) {
            setHasWebGLError(true);
          }
        }}
        onError={(error) => {
          console.warn('WebGL Error, falling back to CSS animation:', error);
          setHasWebGLError(true);
        }}
      >
        <AudioAuroraPlane
          colorA={colorA}
          colorB={colorB}
          colorC={colorC}
          colorD={colorD}
          intensity={dynamicIntensity}
          speed={dynamicSpeed}
          complexity={dynamicComplexity}
          isActive={true}
        />
      </Canvas>

      {/* CSS降级动画样式 */}
      <style jsx>{`
        @keyframes aurora-fallback {
          0%, 100% {
            background-position: 0% 50%;
          }
          25% {
            background-position: 100% 25%;
          }
          50% {
            background-position: 100% 100%;
          }
          75% {
            background-position: 0% 75%;
          }
        }
      `}</style>
    </div>
  );
}

// 预设配色方案
export const AudioAuroraPresets = {
  // 蓝白流体 - 符合文档描述的有机流体效果
  blueGradient: {
    colorA: "#ffffff", // 纯白色 - 流体的高光部分
    colorB: "#f0f9ff", // 极浅蓝白 - 较轻、较稀薄的部分
    colorC: "#0ea5e9", // 明亮天蓝色 - 流体主体
    colorD: "#1e40af", // 深蓝色 - 较厚的区域或阴影面
  },
  // 彩虹流动
  rainbow: {
    colorA: "#3b82f6", // 蓝色
    colorB: "#8b5cf6", // 紫色
    colorC: "#ec4899", // 粉色
    colorD: "#f59e0b", // 橙色
  },
  // 极光绿
  aurora: {
    colorA: "#065f46", // 深绿
    colorB: "#10b981", // 绿色
    colorC: "#34d399", // 浅绿
    colorD: "#6ee7b7", // 极浅绿
  }
};
