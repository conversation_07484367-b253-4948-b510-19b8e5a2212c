"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { auth } from "@/lib/auth-service"
import { TokenManager } from "@/lib/api"
import { useToast } from "@/hooks/use-toast"
import { handleAuthError } from "@/lib/error-utils"

export default function TestAuthPage() {
  const [result, setResult] = useState<string>("")
  const [showAuthDialog, setShowAuthDialog] = useState(false)
  const { toast } = useToast()

  const testInvalidToken = async () => {
    try {
      // 设置一个无效的token
      TokenManager.setTokens("invalid_token", "invalid_refresh_token", "<EMAIL>")
      
      // 尝试获取用户配额
      const data = await auth.getUserQuota()
      setResult("成功获取数据: " + JSON.stringify(data))
    } catch (error: any) {
      console.error('测试错误:', error)
      setResult("捕获错误: " + error.message + " (code: " + (error.code || "无") + ")")
      
      // 使用错误处理函数
      const { isAuthError: isAuth, shouldRedirect } = handleAuthError(error, () => {
        setShowAuthDialog(true)
        toast({
          title: "认证失败",
          description: "会话已过期，正在跳转到登录页面...",
          variant: "destructive",
        })
      })
      
      if (isAuth && shouldRedirect) {
        setTimeout(() => {
          window.location.href = '/login'
        }, 2000)
      }
    }
  }

  const testNoToken = async () => {
    try {
      // 清除所有token
      TokenManager.clearTokens()
      
      // 尝试获取用户配额
      const data = await auth.getUserQuota()
      setResult("成功获取数据: " + JSON.stringify(data))
    } catch (error: any) {
      console.error('测试错误:', error)
      setResult("捕获错误: " + error.message + " (code: " + (error.code || "无") + ")")
      
      // 使用错误处理函数
      const { isAuthError: isAuth, shouldRedirect } = handleAuthError(error, () => {
        setShowAuthDialog(true)
        toast({
          title: "认证失败", 
          description: "会话已过期，正在跳转到登录页面...",
          variant: "destructive",
        })
      })
      
      if (isAuth && shouldRedirect) {
        setTimeout(() => {
          window.location.href = '/login'
        }, 2000)
      }
    }
  }

  return (
    <div className="container mx-auto p-4">
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>认证错误处理测试</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Button onClick={testInvalidToken} variant="outline">
              测试无效Token
            </Button>
            <Button onClick={testNoToken} variant="outline">
              测试无Token
            </Button>
          </div>
          
          {result && (
            <div className="p-4 bg-gray-100 rounded-lg">
              <h3 className="font-semibold mb-2">测试结果:</h3>
              <pre className="text-sm whitespace-pre-wrap">{result}</pre>
            </div>
          )}
          
          {showAuthDialog && (
            <div className="p-4 bg-red-100 border border-red-300 rounded-lg">
              <h3 className="font-semibold text-red-800">认证错误弹窗已触发!</h3>
              <p className="text-red-600">正在准备跳转到登录页面...</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
