# 对话式TTS功能实现说明

## 🎯 实现概述

基于您的分析，我已经成功完善了现有后端TTS系统的缺失功能，确保与参考代码逻辑一致。

## ✅ 已完成的功能

### 1. **音频合并函数 (combineAudio)**
- **位置**: `src/utils/ttsUtils.js`
- **功能**: 合并多个音频ArrayBuffer为单个音频文件
- **来源**: 从参考代码`worker.js`完整迁移

```javascript
function combineAudio(audioDataList) {
  if (!audioDataList || audioDataList.length === 0) {
    return new ArrayBuffer(0);
  }
  // ... 完整实现
}
```

### 2. **语音ID映射函数 (getVoiceId)**
- **位置**: `src/utils/ttsUtils.js`
- **功能**: 根据语音名称获取对应的ElevenLabs Voice ID
- **数据源**: PostgreSQL数据库中的`voice_mappings`表

```javascript
async function getVoiceId(voiceName) {
  // 从数据库查询语音映射
  // 支持回退机制
}
```

### 3. **对话式TTS处理器**
- **位置**: `src/services/ttsProcessor.js`
- **功能**: 完整的对话式TTS处理逻辑
- **特性**:
  - 支持多说话者音频生成
  - 按顺序处理每个说话者
  - 自动合并所有说话者音频
  - 实时进度反馈

```javascript
async startDialogue(taskId, taskData, username) {
  // 1. 验证PRO权限
  // 2. 逐个处理说话者
  // 3. 合并所有音频
  // 4. 存储最终文件
}
```

### 4. **任务类型分发逻辑**
- **位置**: `src/services/ttsProcessor.js` 和 `src/services/websocketManager.js`
- **功能**: 根据`taskType`自动分发到对应处理器

```javascript
async start(taskId, taskData, username) {
  if (taskData.taskType === 'dialogue') {
    return await this.startDialogue(taskId, taskData, username);
  } else {
    return await this.startSingle(taskId, taskData, username);
  }
}
```

### 5. **VIP权限检查差异化**
- **普通TTS**: 需要`STANDARD`权限
- **对话式TTS**: 需要`PRO`权限
- **实现**: 在各自的处理方法中调用不同的权限检查

## 🔄 数据流程对比

### 原有流程（仅支持普通TTS）
```
WebSocket连接 → 生成taskId → 验证token → 
单一处理器 → 文本分割 → 音频生成 → 合并 → 存储
```

### 新流程（支持两种TTS类型）
```
WebSocket连接 → 生成taskId → 验证token → 
任务类型判断 → 
├─ single: STANDARD权限 → 单人TTS处理
└─ dialogue: PRO权限 → 多人对话TTS处理
```

## 📊 对话式TTS处理详细流程

1. **初始化阶段**
   - 验证`dialogue`数组格式
   - 计算总字符数
   - 检查PRO权限和配额

2. **逐个说话者处理**
   ```
   for each speaker in dialogue:
     获取语音ID → 文本分割 → 并发音频生成 → 合并说话者音频
   ```

3. **最终合并**
   - 将所有说话者音频按顺序合并
   - 存储最终音频文件
   - 更新用户使用量

## 🛠️ 关键技术实现

### 音频合并算法
```javascript
// 计算总长度
const totalLength = audioDataList.reduce((acc, buffer) => 
  acc + (buffer.byteLength || 0), 0);

// 创建合并缓冲区
const combined = new Uint8Array(totalLength);

// 逐个复制音频数据
let offset = 0;
for (const buffer of audioDataList) {
  combined.set(new Uint8Array(buffer), offset);
  offset += buffer.byteLength;
}
```

### 语音映射查询
```javascript
// 数据库查询，支持回退
const result = await dbClient.query(
  'SELECT voice_id FROM voice_mappings WHERE voice_name = $1',
  [voiceName]
);
return result.rows[0]?.voice_id || voiceName;
```

## 🧪 测试验证

已创建完整的测试脚本 `test_dialogue_tts.js`：

### 测试内容
1. **普通TTS测试**: 验证单人语音合成
2. **对话式TTS测试**: 验证多人对话合成
3. **权限验证**: 确保权限检查正确
4. **进度追踪**: 验证实时进度反馈

### 运行测试
```bash
node test_dialogue_tts.js
```

## 📋 使用示例

### 普通TTS请求
```javascript
{
  "action": "start",
  "token": "your_token",
  "input": "这是普通TTS文本",
  "voice": "Adam",
  "model": "eleven_turbo_v2"
}
```

### 对话式TTS请求
```javascript
{
  "action": "start",
  "token": "your_token", 
  "taskType": "dialogue",
  "dialogue": [
    {
      "voice": "Adam",
      "text": "你好！"
    },
    {
      "voice": "Alice", 
      "text": "你好，很高兴见到你！"
    }
  ],
  "model": "eleven_turbo_v2"
}
```

## ⚠️ 注意事项

1. **权限要求**:
   - 普通TTS: STANDARD会员
   - 对话式TTS: PRO会员

2. **数据库依赖**:
   - 需要`voice_mappings`表有完整的语音映射数据
   - 建议运行`scripts/create_tables.sql`初始化数据

3. **性能考虑**:
   - 对话式TTS会消耗更多资源
   - 建议限制对话长度和说话者数量

## 🔧 故障排除

### 常见问题
1. **"此功能需要PRO会员权限"**: 用户权限不足
2. **"未找到语音映射"**: 数据库缺少语音数据
3. **"dialogue必须是非空数组"**: 请求格式错误

### 调试建议
1. 检查数据库连接和语音映射数据
2. 验证用户VIP状态和权限
3. 查看WebSocket消息和进度日志

## 🎉 总结

现在系统已经完全支持：
- ✅ 普通TTS（单人语音合成）
- ✅ 对话式TTS（多人对话合成）
- ✅ 权限差异化管理
- ✅ 实时进度追踪
- ✅ 完整的错误处理

所有功能都与参考代码逻辑保持一致，可以安全部署使用。
