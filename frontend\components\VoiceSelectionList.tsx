"use client"

import React from 'react';
import { Play, Pause } from 'lucide-react';

interface Voice {
  id: string;
  name: string;
  description: string;
  preview?: string;
  gender: string;
  language?: string;
}

interface VoiceSelectionListProps {
  filteredVoices: Voice[];
  onSelectVoice: (voiceId: string) => void;
  currentVoiceId?: string | null;
  previewingVoice: string | null;
  handleVoicePreview: (previewUrl: string | null, voiceId: string) => void;
  voiceIconMapping: Record<string, string>;
  voiceIcons: string[];
  listHeightClass?: string;
  showSelectionIndicator?: boolean;
}

export const VoiceSelectionList: React.FC<VoiceSelectionListProps> = ({
  filteredVoices,
  onSelectVoice,
  currentVoiceId,
  previewingVoice,
  handleVoicePreview,
  voiceIconMapping,
  voiceIcons,
  listHeightClass = 'max-h-80',
  showSelectionIndicator = true
}) => {
  return (
    <div className={`${listHeightClass} overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100`}>
      {filteredVoices.length > 0 ? (
        filteredVoices.map((voice) => (
          <div
            key={voice.id}
            data-voice-id={voice.id}
            className={`relative p-2.5 border-b border-gray-100 last:border-b-0 cursor-pointer transition-all duration-300 hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50/50 group/item ${
              currentVoiceId === voice.id
                ? "bg-gradient-to-r from-blue-50 to-purple-50 border-blue-100"
                : ""
            }`}
            onClick={() => onSelectVoice(voice.id)}
          >
            <div className="flex items-center gap-2.5">
              {/* Enhanced Avatar */}
              <div
                className={`relative w-10 h-10 rounded-full overflow-hidden shadow-lg transition-all duration-300 group-hover/item:scale-110 group-hover/item:rotate-12 ${
                  currentVoiceId === voice.id ? "ring-3 ring-blue-300 scale-110" : ""
                }`}
              >
                {voiceIconMapping[voice.id] ? (
                  <img
                    src={voiceIconMapping[voice.id]}
                    alt={voice.name}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      // 如果图片加载失败，显示备用的文字头像
                      const target = e.target as HTMLImageElement
                      target.style.display = 'none'
                      const parent = target.parentElement
                      if (parent) {
                        parent.innerHTML = `
                          <div class="w-full h-full rounded-full flex items-center justify-center text-white font-bold ${
                            voice.gender === "male"
                              ? "bg-gradient-to-br from-blue-500 to-blue-700"
                              : voice.gender === "female"
                              ? "bg-gradient-to-br from-pink-500 to-pink-700"
                              : "bg-gradient-to-br from-gray-500 to-gray-700"
                          }">
                            ${voice.name.charAt(0).toUpperCase()}
                          </div>
                        `
                      }
                    }}
                  />
                ) : (
                  // 服务器端渲染或图标映射未加载时的占位符 - 解决水合失败问题
                  <div className={`w-full h-full rounded-full flex items-center justify-center text-white font-bold animate-pulse ${
                    voice.gender === "male"
                      ? "bg-gradient-to-br from-blue-400 to-blue-600"
                      : voice.gender === "female"
                      ? "bg-gradient-to-br from-pink-400 to-pink-600"
                      : "bg-gradient-to-br from-gray-400 to-gray-600"
                  }`}>
                    {voice.name.charAt(0).toUpperCase()}
                  </div>
                )}
              </div>

              {/* Voice Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <div className="font-semibold text-gray-900 text-base truncate group-hover/item:text-blue-700 transition-colors duration-300">
                    {voice.name}
                  </div>
                  {/* 性别标识 */}
                  <div className={`flex items-center gap-1 px-2 py-0.5 text-xs font-medium rounded-full flex-shrink-0 ${
                    voice.gender === "male"
                      ? "bg-blue-100 text-blue-700 border border-blue-200"
                      : voice.gender === "female"
                      ? "bg-pink-100 text-pink-700 border border-pink-200"
                      : "bg-gray-100 text-gray-700 border border-gray-200"
                  }`}>
                    <div className={`w-2 h-2 rounded-full ${
                      voice.gender === "male"
                        ? "bg-blue-500"
                        : voice.gender === "female"
                        ? "bg-pink-500"
                        : "bg-gray-500"
                    }`} />
                    <span>
                      {voice.gender === "male" ? "男生" : voice.gender === "female" ? "女生" : "中性"}
                    </span>
                  </div>

                  {/* 选中状态的闪烁动画点 */}
                  {currentVoiceId === voice.id && (
                    <div className="w-3 h-3 bg-blue-500 rounded-full animate-ping flex-shrink-0 ml-10" />
                  )}
                </div>
                <div className={`text-sm leading-relaxed truncate transition-colors duration-300 ${
                  voice.gender === "male"
                    ? "text-blue-600 group-hover/item:text-blue-700"
                    : voice.gender === "female"
                    ? "text-pink-600 group-hover/item:text-pink-700"
                    : "text-gray-600 group-hover/item:text-gray-700"
                }`}>
                  {voice.description}
                </div>
              </div>

              {/* Preview Button */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleVoicePreview(voice.preview || null, voice.id);
                }}
                disabled={!voice.preview}
                className={`button-hover-optimized w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-125 hover:rotate-12 group/play shadow-lg disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:scale-100 ${
                  previewingVoice === voice.id
                    ? "bg-gradient-to-r from-green-100 to-green-200 hover:from-green-200 hover:to-green-300"
                    : "bg-gradient-to-r from-gray-100 to-gray-200 hover:from-blue-100 hover:to-blue-200"
                }`}
              >
                {previewingVoice === voice.id ? (
                  <div className="flex items-center justify-center">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div>
                  </div>
                ) : (
                  <Play className="w-4 h-4 text-gray-600 group-hover/play:text-blue-600 ml-0.5 transition-colors duration-300" />
                )}
              </button>
            </div>

            {/* Enhanced Selection Indicator */}
            {showSelectionIndicator && currentVoiceId === voice.id && (
              <div className="absolute left-0 top-0 bottom-0 w-2 bg-gradient-to-b from-blue-400 via-purple-500 to-pink-400 rounded-r animate-pulse" />
            )}
          </div>
        ))
      ) : (
        <div className="p-8 text-center text-gray-500">
          <p>没有找到匹配的声音。</p>
        </div>
      )}
    </div>
  );
};
