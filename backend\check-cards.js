#!/usr/bin/env node

require('dotenv').config();
const { Pool } = require('pg');

// 颜色输出函数
function log(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color] || ''}${message}${colors.reset}`);
}

// 检查数据库中的卡密
async function checkCards() {
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL
  });

  try {
    log('blue', '🔍 检查数据库中的卡密数据...\n');

    // 检查cards表是否存在
    const tableExists = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'cards'
      );
    `);

    if (!tableExists.rows[0].exists) {
      log('red', '❌ cards表不存在！请先运行数据库迁移。');
      return;
    }

    log('green', '✅ cards表存在');

    // 查询所有卡密
    const allCards = await pool.query('SELECT * FROM cards ORDER BY created_at DESC');
    
    log('blue', `📊 数据库中共有 ${allCards.rows.length} 张卡密\n`);

    if (allCards.rows.length === 0) {
      log('yellow', '⚠️  数据库中没有卡密数据');
      log('cyan', '💡 可以运行以下命令创建测试卡密：');
      log('cyan', '   npm run cards:create');
      return;
    }

    // 按状态统计
    const statusStats = await pool.query(`
      SELECT status, COUNT(*) as count 
      FROM cards 
      GROUP BY status 
      ORDER BY status
    `);

    log('cyan', '📈 卡密状态统计：');
    statusStats.rows.forEach(row => {
      const statusName = {
        'unused': '未使用',
        'used': '已使用',
        'expired': '已过期'
      }[row.status] || row.status;
      log('yellow', `   ${statusName}: ${row.count} 张`);
    });

    // 按套餐类型统计
    const packageStats = await pool.query(`
      SELECT package_type, COUNT(*) as count 
      FROM cards 
      GROUP BY package_type 
      ORDER BY package_type
    `);

    log('cyan', '\n📦 套餐类型统计：');
    packageStats.rows.forEach(row => {
      const packageName = {
        'M': '标准月套餐',
        'Q': '标准季度套餐',
        'H': '标准半年套餐',
        'PM': 'PRO月套餐',
        'PQ': 'PRO季度套餐',
        'PH': 'PRO半年套餐',
        'PT': '测试套餐'
      }[row.package_type] || row.package_type;
      log('yellow', `   ${packageName} (${row.package_type}): ${row.count} 张`);
    });

    // 显示最近的几张卡密详情
    log('cyan', '\n🎫 最近创建的卡密（最多显示5张）：');
    const recentCards = allCards.rows.slice(0, 5);
    
    recentCards.forEach((card, index) => {
      log('magenta', `\n${index + 1}. 卡密: ${card.code}`);
      log('blue', `   套餐类型: ${card.package_type}`);
      log('green', `   状态: ${card.status}`);
      log('yellow', `   创建时间: ${new Date(card.created_at).toLocaleString('zh-CN')}`);
      
      if (card.package_info) {
        try {
          const packageInfo = typeof card.package_info === 'string' 
            ? JSON.parse(card.package_info) 
            : card.package_info;
          log('cyan', `   套餐信息: ${packageInfo.description || '无描述'}`);
          log('cyan', `   字符配额: ${packageInfo.quotaChars?.toLocaleString() || '未知'}`);
          log('cyan', `   价格: ¥${packageInfo.price || '未知'}`);
        } catch (e) {
          log('red', `   套餐信息解析失败: ${e.message}`);
        }
      }
      
      if (card.used_at) {
        log('yellow', `   使用时间: ${new Date(card.used_at).toLocaleString('zh-CN')}`);
      }
      if (card.used_by) {
        log('yellow', `   使用者: ${card.used_by}`);
      }
    });

  } catch (error) {
    log('red', `❌ 检查失败: ${error.message}`);
    console.error(error);
  } finally {
    await pool.end();
  }
}

// 主函数
async function main() {
  try {
    await checkCards();
  } catch (error) {
    log('red', `❌ 程序执行失败: ${error.message}`);
    process.exit(1);
  }
}

// 执行脚本
if (require.main === module) {
  main();
}

module.exports = { checkCards };
