/**
 * 声音数据加载状态组件
 * 包含加载中、错误状态的UI展示
 */

"use client"

import React from 'react';
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Al<PERSON><PERSON><PERSON>gle, RefreshCw, Wifi, FileX, HelpCircle } from 'lucide-react';
import { VoicesError } from '@/lib/types/voices';

interface VoicesLoadingProps {
  className?: string;
}

interface VoicesErrorProps {
  error: VoicesError;
  onRetry: () => void;
  className?: string;
}

// 加载状态组件
export const VoicesLoading: React.FC<VoicesLoadingProps> = ({ className = "" }) => {
  return (
    <div className={`flex items-center justify-center min-h-screen ${className}`}>
      <Card className="w-full max-w-md mx-4">
        <CardContent className="flex flex-col items-center justify-center p-8">
          {/* 加载动画 */}
          <div className="relative mb-6">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-gray-200"></div>
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-500 border-t-transparent absolute top-0 left-0"></div>
          </div>
          
          {/* 加载文本 */}
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            正在加载声音配置
          </h3>
          <p className="text-sm text-gray-600 text-center">
            正在从服务器获取最新的声音列表，请稍候...
          </p>
          
          {/* 加载进度指示 */}
          <div className="flex items-center gap-2 mt-4">
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"
                style={{ animationDelay: `${i * 0.15}s` }}
              />
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// 错误状态组件
export const VoicesError: React.FC<VoicesErrorProps> = ({ error, onRetry, className = "" }) => {
  // 根据错误类型选择图标和颜色
  const getErrorIcon = () => {
    switch (error.type) {
      case 'network':
        return <Wifi className="w-12 h-12 text-red-500" />;
      case 'parse':
        return <FileX className="w-12 h-12 text-orange-500" />;
      case 'empty':
        return <AlertTriangle className="w-12 h-12 text-yellow-500" />;
      default:
        return <HelpCircle className="w-12 h-12 text-gray-500" />;
    }
  };

  const getErrorTitle = () => {
    switch (error.type) {
      case 'network':
        return '网络连接失败';
      case 'parse':
        return '数据格式错误';
      case 'empty':
        return '暂无声音数据';
      default:
        return '加载失败';
    }
  };

  const getErrorDescription = () => {
    switch (error.type) {
      case 'network':
        return '无法连接到服务器，请检查网络连接后重试';
      case 'parse':
        return '服务器返回的数据格式不正确，请联系技术支持';
      case 'empty':
        return '当前没有可用的声音选项，请稍后再试或联系管理员';
      default:
        return '发生了未知错误，请尝试刷新页面或联系技术支持';
    }
  };

  return (
    <div className={`flex items-center justify-center min-h-screen ${className}`}>
      <Card className="w-full max-w-md mx-4">
        <CardContent className="flex flex-col items-center justify-center p-8">
          {/* 错误图标 */}
          <div className="mb-6">
            {getErrorIcon()}
          </div>
          
          {/* 错误标题 */}
          <h3 className="text-lg font-semibold text-gray-900 mb-2 text-center">
            {getErrorTitle()}
          </h3>
          
          {/* 错误描述 */}
          <p className="text-sm text-gray-600 text-center mb-6">
            {getErrorDescription()}
          </p>
          
          {/* 详细错误信息（开发环境显示） */}
          {process.env.NODE_ENV === 'development' && (
            <div className="w-full mb-4 p-3 bg-gray-100 rounded-lg">
              <p className="text-xs text-gray-700 font-mono break-all">
                {error.message}
              </p>
            </div>
          )}
          
          {/* 操作按钮 */}
          <div className="flex flex-col gap-3 w-full">
            {error.retryable && (
              <Button 
                onClick={onRetry}
                className="w-full flex items-center gap-2"
                variant="default"
              >
                <RefreshCw className="w-4 h-4" />
                重试加载
              </Button>
            )}
            
            <Button 
              onClick={() => window.location.reload()}
              variant="outline"
              className="w-full"
            >
              刷新页面
            </Button>
          </div>
          
          {/* 帮助提示 */}
          <p className="text-xs text-gray-500 text-center mt-4">
            如果问题持续存在，请联系技术支持
          </p>
        </CardContent>
      </Card>
    </div>
  );
};
