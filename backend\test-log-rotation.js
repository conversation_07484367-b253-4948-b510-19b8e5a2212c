#!/usr/bin/env node

/**
 * 日志轮转功能测试脚本
 * 用于验证Logger类的文件大小限制和轮转功能
 */

const { logger } = require('./src/utils/logger');
const fs = require('fs');
const path = require('path');

// 测试配置
const TEST_CONFIG = {
  // 设置较小的文件大小限制用于测试 (1KB)
  maxFileSize: 1024,
  maxFiles: 5,
  testLogCount: 100,
  testMessage: 'This is a test log message for rotation testing. '.repeat(10) // 约500字节
};

async function testLogRotation() {
  console.log('🚀 开始测试日志轮转功能...\n');
  
  // 临时修改logger配置用于测试
  const originalMaxFileSize = logger.maxFileSize;
  const originalMaxFiles = logger.maxFiles;
  
  logger.maxFileSize = TEST_CONFIG.maxFileSize;
  logger.maxFiles = TEST_CONFIG.maxFiles;
  
  try {
    // 清理测试前的日志文件
    await cleanupTestLogs();
    
    console.log(`📝 生成 ${TEST_CONFIG.testLogCount} 条测试日志...`);
    console.log(`📏 单条日志大小: ~${TEST_CONFIG.testMessage.length} 字节`);
    console.log(`📦 文件大小限制: ${TEST_CONFIG.maxFileSize} 字节`);
    console.log(`📚 最大文件数量: ${TEST_CONFIG.maxFiles}\n`);
    
    // 生成大量日志触发轮转
    for (let i = 1; i <= TEST_CONFIG.testLogCount; i++) {
      logger.info(`测试日志 ${i}`, {
        testData: TEST_CONFIG.testMessage,
        iteration: i,
        timestamp: new Date().toISOString()
      });
      
      // 每10条日志显示进度
      if (i % 10 === 0) {
        console.log(`✅ 已生成 ${i} 条日志`);
        await sleep(100); // 短暂延迟确保文件写入
      }
    }
    
    console.log('\n📊 检查日志文件状态...');
    await checkLogFiles();
    
    console.log('\n🧹 测试日志清理功能...');
    logger.cleanupOldLogs();
    
    console.log('\n📊 清理后的日志文件状态...');
    await checkLogFiles();
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    // 恢复原始配置
    logger.maxFileSize = originalMaxFileSize;
    logger.maxFiles = originalMaxFiles;
    
    console.log('\n✅ 日志轮转功能测试完成!');
    console.log('\n💡 提示: 查看 ./logs 目录中的文件来验证轮转效果');
  }
}

async function cleanupTestLogs() {
  const logDir = logger.logDir;
  const today = new Date().toISOString().split('T')[0];
  
  try {
    const files = fs.readdirSync(logDir);
    const testFiles = files.filter(file => 
      file.startsWith(today) && (file.endsWith('.log') || file.endsWith('.gz'))
    );
    
    for (const file of testFiles) {
      const filePath = path.join(logDir, file);
      fs.unlinkSync(filePath);
      console.log(`🗑️  删除测试文件: ${file}`);
    }
  } catch (error) {
    console.log('📁 日志目录为空或不存在，跳过清理');
  }
}

async function checkLogFiles() {
  const logDir = logger.logDir;
  const today = new Date().toISOString().split('T')[0];
  
  try {
    const files = fs.readdirSync(logDir);
    const logFiles = files.filter(file => 
      file.startsWith(today) && (file.endsWith('.log') || file.endsWith('.gz'))
    ).sort();
    
    console.log(`📁 日志目录: ${logDir}`);
    console.log(`📄 找到 ${logFiles.length} 个日志文件:`);
    
    for (const file of logFiles) {
      const filePath = path.join(logDir, file);
      const stats = fs.statSync(filePath);
      const sizeKB = (stats.size / 1024).toFixed(2);
      
      console.log(`   📄 ${file} - ${sizeKB} KB`);
    }
    
    if (logFiles.length === 0) {
      console.log('   (无日志文件)');
    }
  } catch (error) {
    console.error('❌ 检查日志文件时发生错误:', error);
  }
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 运行测试
if (require.main === module) {
  testLogRotation().catch(console.error);
}

module.exports = { testLogRotation };
