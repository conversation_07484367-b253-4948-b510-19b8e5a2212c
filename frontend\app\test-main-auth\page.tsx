"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { auth } from "@/lib/auth-service"
import { TokenManager } from "@/lib/api"
import { useToast } from "@/hooks/use-toast"
import { handleAuthError } from "@/lib/error-utils"
import { Clock, LogOut } from "lucide-react"

export default function TestMainAuthPage() {
  const [result, setResult] = useState<string>("")
  const [showAuthDialog, setShowAuthDialog] = useState(false)
  const { toast } = useToast()

  // 模拟主页面的fetchUserStatus函数
  const fetchUserStatus = async () => {
    try {
      setResult("开始获取用户状态...")
      const data = await auth.getUserQuota()
      setResult("成功获取用户状态: " + JSON.stringify(data, null, 2))
    } catch (error: any) {
      console.error('获取用户状态失败:', error)
      setResult(`获取用户状态失败: ${error.message}\n错误码: ${error.code || "无"}\nshouldRedirect: ${error.shouldRedirect || "无"}`)
      
      // 【修复】添加认证错误处理逻辑 - 与主页面完全相同
      const { isAuthError: isAuth, shouldRedirect } = handleAuthError(error, () => {
        // 认证错误回调：显示登录过期弹窗
        console.log("认证错误回调被触发，显示弹窗")
        setShowAuthDialog(true)
      })
      
      console.log("错误处理结果:", { isAuth, shouldRedirect })
      
      if (isAuth && shouldRedirect) {
        // 如果是认证错误且需要跳转，延迟跳转给用户看到弹窗
        console.log("将在2秒后跳转到登录页面")
        setTimeout(() => {
          console.log("执行跳转到登录页面")
          window.location.href = '/login'
        }, 2000)
      }
    }
  }

  const simulateExpiredTokens = () => {
    // 设置无效的tokens来模拟过期
    TokenManager.setTokens("expired_access_token", "expired_refresh_token", "<EMAIL>")
    setResult("已设置过期的tokens，现在可以测试fetchUserStatus")
  }

  const clearTokens = () => {
    TokenManager.clearTokens()
    setResult("已清除所有tokens")
    setShowAuthDialog(false)
  }

  const checkTokens = () => {
    const accessToken = TokenManager.getAccessToken()
    const refreshToken = TokenManager.getRefreshToken()
    setResult(`当前tokens状态:
Access Token: ${accessToken ? `${accessToken.substring(0, 20)}...` : "无"}
Refresh Token: ${refreshToken ? `${refreshToken.substring(0, 20)}...` : "无"}`)
  }

  return (
    <div className="container mx-auto p-4">
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>主页面认证错误处理测试</CardTitle>
          <p className="text-sm text-gray-600">模拟主页面的fetchUserStatus函数行为</p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Button onClick={checkTokens} variant="outline">
              检查当前Tokens
            </Button>
            <Button onClick={simulateExpiredTokens} variant="secondary">
              设置过期Tokens
            </Button>
            <Button onClick={fetchUserStatus} variant="default">
              测试fetchUserStatus (主页面逻辑)
            </Button>
            <Button onClick={clearTokens} variant="destructive">
              清除所有Tokens
            </Button>
          </div>
          
          {result && (
            <div className="p-4 bg-gray-100 rounded-lg">
              <h3 className="font-semibold mb-2">测试结果:</h3>
              <pre className="text-sm whitespace-pre-wrap">{result}</pre>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 登录过期弹窗 - 与主页面完全相同 */}
      <Dialog open={showAuthDialog} onOpenChange={setShowAuthDialog}>
        <DialogContent className="sm:max-w-md border-0 shadow-2xl bg-white/95 backdrop-blur-xl">
          <DialogHeader className="text-center space-y-4">
            <div className="mx-auto w-16 h-16 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full flex items-center justify-center">
              <Clock className="w-8 h-8 text-blue-500" />
            </div>
            <DialogTitle className="text-center text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              登录已过期
            </DialogTitle>
            <DialogDescription className="text-gray-600">
              <div className="flex items-center justify-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <span className="text-sm">您的会话已过期，请重新登录以继续。</span>
              </div>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-6">
            <Button
              onClick={async () => {
                setShowAuthDialog(false)
                await auth.logout()
                window.location.href = "/login"
              }}
              className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <LogOut className="w-4 h-4 mr-2" />
              重新登录
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
