(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[454],{3279:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>w});var t=s(5155),r=s(9137),n=s.n(r),l=s(2115),i=s(7168),o=s(8482),d=s(9852),c=s(5139),m=s(646),x=s(9588),b=s(5339),f=s(1007),u=s(8883),g=s(2919),h=s(8749),p=s(2657),j=s(2486),y=s(5525),v=s(6194);function w(){var e;let[a,s]=(0,l.useState)({username:"",email:"",password:"",confirmPassword:"",agreeToTerms:!1,subscribeNewsletter:!1}),[r,w]=(0,l.useState)(!1),[N,k]=(0,l.useState)(!1),[C,E]=(0,l.useState)(!1),[S,A]=(0,l.useState)({}),[R,T]=(0,l.useState)(""),[z,P]=(0,l.useState)(!1),[F,V]=(0,l.useState)(0),[Z,_]=(0,l.useState)(!1),[$,D]=(0,l.useState)(!1),[L,I]=(0,l.useState)(""),[O,W]=(0,l.useState)(!1),B=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[a,s]=(0,l.useState)({isSending:!1,sendError:null,isCodeSent:!1,isVerifying:!1,verifyError:null,countdown:0,canResend:!0,pendingRegistration:null});(0,l.useEffect)(()=>{let e;return a.countdown>0&&(e=setTimeout(()=>{s(e=>({...e,countdown:e.countdown-1,canResend:e.countdown<=1}))},1e3)),()=>{e&&clearTimeout(e)}},[a.countdown]);let t=(0,l.useCallback)(async a=>{s(e=>({...e,isSending:!0,sendError:null}));try{let e=await v.j2.sendVerificationCode(a);return s(e=>({...e,isSending:!1,isCodeSent:!0,countdown:60,canResend:!1,pendingRegistration:{email:a.email,username:a.username,password:a.password}})),e}catch(r){var t;let a=r instanceof Error?r.message:"发送验证码失败";throw s(e=>({...e,isSending:!1,sendError:a})),null===(t=e.onError)||void 0===t||t.call(e,a),r}},[e]),r=(0,l.useCallback)(async()=>{if(a.canResend&&a.pendingRegistration)return await t(a.pendingRegistration)},[a.canResend,a.pendingRegistration,t]),n=(0,l.useCallback)(async t=>{var r,n;if(!a.pendingRegistration)throw Error("没有待验证的注册信息");s(e=>({...e,isVerifying:!0,verifyError:null}));try{let n={username:a.pendingRegistration.username,email:a.pendingRegistration.email,code:t.trim()},l=await v.j2.verifyEmailAndRegister(n);return s(e=>({...e,isVerifying:!1,pendingRegistration:null})),null===(r=e.onSuccess)||void 0===r||r.call(e),l}catch(t){let a=t instanceof Error?t.message:"验证失败";throw s(e=>({...e,isVerifying:!1,verifyError:a})),null===(n=e.onError)||void 0===n||n.call(e,a),t}},[a.pendingRegistration,e]),i=(0,l.useCallback)(()=>{s(e=>({...e,sendError:null,verifyError:null}))},[]),o=(0,l.useCallback)(()=>{s({isSending:!1,sendError:null,isCodeSent:!1,isVerifying:!1,verifyError:null,countdown:0,canResend:!0,pendingRegistration:null})},[]),d=(0,l.useCallback)(()=>a.countdown>0?"".concat(a.countdown,"秒后可重新发送"):"重新发送验证码",[a.countdown]);return{...a,sendVerificationCode:t,resendVerificationCode:r,verifyEmailAndRegister:n,clearErrors:i,reset:o,getCountdownText:d,hasError:!!(a.sendError||a.verifyError),isLoading:a.isSending||a.isVerifying}}({onSuccess:()=>{_(!0),setTimeout(()=>{window.location.href="/"},2e3)},onError:e=>{T(e)}});(0,l.useEffect)(()=>{D(!0)},[]),(0,l.useEffect)(()=>{P(!0)},[]),(0,l.useEffect)(()=>{var e;let s;V((e=a.password,s=0,e.length>=8&&(s+=1),/[a-z]/.test(e)&&(s+=1),/[A-Z]/.test(e)&&(s+=1),/[0-9]/.test(e)&&(s+=1),/[^A-Za-z0-9]/.test(e)&&(s+=1),s))},[a.password]);let q=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),G=e=>/^[a-zA-Z0-9_]{3,20}$/.test(e),H=e=>e.length>=8&&/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(e),J=()=>{let e={};return a.username.trim()?G(a.username)||(e.username="用户名只能包含字母、数字和下划线，长度3-20位"):e.username="用户名不能为空",a.email.trim()?q(a.email)||(e.email="请输入有效的邮箱地址"):e.email="邮箱地址不能为空",a.password?H(a.password)||(e.password="密码至少8位，包含大小写字母和数字"):e.password="密码不能为空",a.confirmPassword?a.password!==a.confirmPassword&&(e.confirmPassword="两次输入的密码不一致"):e.confirmPassword="请确认密码",a.agreeToTerms||(e.agreeToTerms="请同意服务条款和隐私政策"),A(e),0===Object.keys(e).length},K=(e,a)=>{s(s=>({...s,[e]:a})),S[e]&&A(a=>({...a,[e]:""})),R&&T("")},M=async e=>{if(e.preventDefault(),J()){if(O){if(!L.trim()){T("请输入验证码");return}E(!0),T("");try{await B.verifyEmailAndRegister(L)}catch(e){}finally{E(!1)}}else{E(!0),T("");try{await B.sendVerificationCode({email:a.email,username:a.username,password:a.password}),W(!0)}catch(e){}finally{E(!1)}}}},Q=[{left:18,top:22,duration:10},{left:72,top:38,duration:12},{left:42,top:62,duration:9},{left:82,top:16,duration:11},{left:28,top:78,duration:8},{left:62,top:48,duration:13},{left:52,top:32,duration:10},{left:38,top:82,duration:12}],U=()=>(0,t.jsx)("div",{className:"absolute inset-0 overflow-hidden pointer-events-none",children:Q.map((e,a)=>(0,t.jsx)("div",{className:"absolute w-2 h-2 bg-gradient-to-r from-green-400 to-blue-400 rounded-full opacity-20 animate-float",style:{left:"".concat(e.left,"%"),top:"".concat(e.top,"%"),animationDelay:"".concat(2*a,"s"),animationDuration:"".concat(e.duration,"s")}},a))});return Z?(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-green-50/30 to-blue-50/20 flex items-center justify-center p-6 relative overflow-hidden",children:[$&&(0,t.jsx)(U,{}),(0,t.jsx)("div",{className:"w-full max-w-md",children:(0,t.jsx)(o.Zp,{className:"border-0 shadow-2xl bg-white/90 backdrop-blur-xl relative overflow-hidden",children:(0,t.jsxs)(o.Wu,{className:"p-10 text-center",children:[(0,t.jsx)("div",{className:"flex justify-center mb-6",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-500 to-blue-600 rounded-full blur-lg opacity-50 animate-pulse"}),(0,t.jsx)("div",{className:"relative p-4 bg-gradient-to-r from-green-500 to-blue-600 rounded-full shadow-xl",children:(0,t.jsx)(m.A,{className:"w-8 h-8 text-white"})})]})}),(0,t.jsx)("h2",{className:"text-3xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-4",children:"注册成功！"}),(0,t.jsx)("p",{className:"text-gray-600 text-lg mb-6",children:"欢迎加入 AI 语音工作室，正在为您跳转到主页面..."}),(0,t.jsx)("div",{className:"flex justify-center",children:(0,t.jsx)("div",{className:"w-8 h-8 border-4 border-green-500 border-t-transparent rounded-full animate-spin"})})]})})})]}):(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b min-h-screen bg-gradient-to-br from-slate-50 via-green-50/30 to-blue-50/20 flex items-center justify-center p-6 relative overflow-hidden",children:[$&&(0,t.jsx)(U,{className:"jsx-3e1eea4381e0e46b"}),(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute top-20 right-20 w-72 h-72 bg-gradient-to-r from-green-200/20 to-blue-200/20 rounded-full blur-3xl animate-pulse"}),(0,t.jsx)("div",{style:{animationDelay:"2s"},className:"jsx-3e1eea4381e0e46b absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-r from-blue-200/20 to-purple-200/20 rounded-full blur-3xl animate-pulse"}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b "+"w-full max-w-2xl transition-all duration-1000 ".concat(z?"opacity-100 translate-y-0":"opacity-0 translate-y-8"),children:[(0,t.jsxs)(o.Zp,{className:"border-0 shadow-2xl bg-white/90 backdrop-blur-xl relative overflow-hidden",children:[(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-0 bg-gradient-to-r from-green-500/5 to-blue-500/5"}),(0,t.jsxs)(o.aR,{className:"text-center pb-8 relative",children:[(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b flex justify-center mb-6",children:(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b relative",children:[(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-0 bg-gradient-to-r from-green-500 to-blue-600 rounded-2xl blur-lg opacity-50 animate-pulse"}),(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b relative p-4 bg-gradient-to-r from-green-500 to-blue-600 rounded-2xl shadow-xl",children:(0,t.jsx)(x.A,{className:"w-8 h-8 text-white"})})]})}),(0,t.jsx)(o.ZB,{className:"text-3xl font-bold bg-gradient-to-r from-gray-900 via-green-800 to-blue-800 bg-clip-text text-transparent mb-2",children:"创建账户"}),(0,t.jsx)("p",{className:"jsx-3e1eea4381e0e46b text-gray-600 text-lg",children:"加入 AI 语音工作室，开启您的创作之旅"})]}),(0,t.jsxs)(o.Wu,{className:"relative",children:[(0,t.jsxs)("form",{onSubmit:M,className:"jsx-3e1eea4381e0e46b space-y-6",children:[(R||B.hasError)&&(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-3 animate-fade-in",children:[(0,t.jsx)(b.A,{className:"w-5 h-5 text-red-500 flex-shrink-0"}),(0,t.jsx)("p",{className:"jsx-3e1eea4381e0e46b text-red-700 text-sm",children:R||B.sendError||B.verifyError})]}),B.isCodeSent&&!B.hasError&&(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b p-4 bg-green-50 border border-green-200 rounded-lg flex items-center gap-3 animate-fade-in",children:[(0,t.jsx)(m.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,t.jsxs)("p",{className:"jsx-3e1eea4381e0e46b text-green-700 text-sm",children:["验证码已发送到 ",null===(e=B.pendingRegistration)||void 0===e?void 0:e.email,"，请查收邮件"]})]}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b space-y-2",children:[(0,t.jsxs)("label",{htmlFor:"username",className:"jsx-3e1eea4381e0e46b block text-sm font-semibold text-gray-700",children:["用户名 ",(0,t.jsx)("span",{className:"jsx-3e1eea4381e0e46b text-red-500",children:"*"})]}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b relative",children:[(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(f.A,{className:"h-5 w-5 text-gray-400"})}),(0,t.jsx)(d.p,{id:"username",type:"text",value:a.username,onChange:e=>K("username",e.target.value),placeholder:"请输入用户名",className:"pl-10 h-12 text-lg border-2 transition-all duration-300 ".concat(S.username?"border-red-400 focus:border-red-500 focus:ring-red-100":"border-gray-200 focus:border-green-400 focus:ring-green-100"),disabled:C})]}),S.username&&(0,t.jsxs)("p",{className:"jsx-3e1eea4381e0e46b text-red-500 text-sm flex items-center gap-2 animate-fade-in",children:[(0,t.jsx)(b.A,{className:"w-4 h-4"}),S.username]})]}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b space-y-2",children:[(0,t.jsxs)("label",{htmlFor:"email",className:"jsx-3e1eea4381e0e46b block text-sm font-semibold text-gray-700",children:["邮箱地址 ",(0,t.jsx)("span",{className:"jsx-3e1eea4381e0e46b text-red-500",children:"*"})]}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b relative",children:[(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(u.A,{className:"h-5 w-5 text-gray-400"})}),(0,t.jsx)(d.p,{id:"email",type:"email",value:a.email,onChange:e=>K("email",e.target.value),placeholder:"请输入邮箱地址",className:"pl-10 h-12 text-lg border-2 transition-all duration-300 ".concat(S.email?"border-red-400 focus:border-red-500 focus:ring-red-100":"border-gray-200 focus:border-green-400 focus:ring-green-100"),disabled:C})]}),S.email&&(0,t.jsxs)("p",{className:"jsx-3e1eea4381e0e46b text-red-500 text-sm flex items-center gap-2 animate-fade-in",children:[(0,t.jsx)(b.A,{className:"w-4 h-4"}),S.email]})]})]}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b space-y-2",children:[(0,t.jsxs)("label",{htmlFor:"password",className:"jsx-3e1eea4381e0e46b block text-sm font-semibold text-gray-700",children:["密码 ",(0,t.jsx)("span",{className:"jsx-3e1eea4381e0e46b text-red-500",children:"*"})]}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b relative",children:[(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(g.A,{className:"h-5 w-5 text-gray-400"})}),(0,t.jsx)(d.p,{id:"password",type:r?"text":"password",value:a.password,onChange:e=>K("password",e.target.value),placeholder:"请输入密码",className:"pl-10 pr-12 h-12 text-lg border-2 transition-all duration-300 ".concat(S.password?"border-red-400 focus:border-red-500 focus:ring-red-100":"border-gray-200 focus:border-green-400 focus:ring-green-100"),disabled:C}),(0,t.jsx)("button",{type:"button",onClick:()=>w(!r),disabled:C,className:"jsx-3e1eea4381e0e46b absolute inset-y-0 right-0 pr-3 flex items-center hover:bg-gray-50 rounded-r-lg transition-colors duration-200",children:r?(0,t.jsx)(h.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"}):(0,t.jsx)(p.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"})})]}),a.password&&(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b space-y-2",children:(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b flex items-center gap-2",children:[(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b flex-1 bg-gray-200 rounded-full h-2 overflow-hidden",children:(0,t.jsx)("div",{style:{width:"".concat(F/5*100,"%")},className:"jsx-3e1eea4381e0e46b "+"h-full transition-all duration-300 ".concat(F<=1?"bg-red-500":F<=2?"bg-orange-500":F<=3?"bg-yellow-500":F<=4?"bg-blue-500":"bg-green-500")})}),(0,t.jsx)("span",{className:"jsx-3e1eea4381e0e46b text-sm font-medium text-gray-600",children:F<=1?"弱":F<=2?"一般":F<=3?"中等":F<=4?"强":"很强"})]})}),S.password&&(0,t.jsxs)("p",{className:"jsx-3e1eea4381e0e46b text-red-500 text-sm flex items-center gap-2 animate-fade-in",children:[(0,t.jsx)(b.A,{className:"w-4 h-4"}),S.password]})]}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b space-y-2",children:[(0,t.jsxs)("label",{htmlFor:"confirmPassword",className:"jsx-3e1eea4381e0e46b block text-sm font-semibold text-gray-700",children:["确认密码 ",(0,t.jsx)("span",{className:"jsx-3e1eea4381e0e46b text-red-500",children:"*"})]}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b relative",children:[(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(g.A,{className:"h-5 w-5 text-gray-400"})}),(0,t.jsx)(d.p,{id:"confirmPassword",type:N?"text":"password",value:a.confirmPassword,onChange:e=>K("confirmPassword",e.target.value),placeholder:"请再次输入密码",className:"pl-10 pr-12 h-12 text-lg border-2 transition-all duration-300 ".concat(S.confirmPassword?"border-red-400 focus:border-red-500 focus:ring-red-100":"border-gray-200 focus:border-green-400 focus:ring-green-100"),disabled:C}),(0,t.jsx)("button",{type:"button",onClick:()=>k(!N),disabled:C,className:"jsx-3e1eea4381e0e46b absolute inset-y-0 right-0 pr-3 flex items-center hover:bg-gray-50 rounded-r-lg transition-colors duration-200",children:N?(0,t.jsx)(h.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"}):(0,t.jsx)(p.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"})})]}),S.confirmPassword&&(0,t.jsxs)("p",{className:"jsx-3e1eea4381e0e46b text-red-500 text-sm flex items-center gap-2 animate-fade-in",children:[(0,t.jsx)(b.A,{className:"w-4 h-4"}),S.confirmPassword]})]}),O&&(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b space-y-2",children:[(0,t.jsxs)("label",{htmlFor:"verificationCode",className:"jsx-3e1eea4381e0e46b block text-sm font-semibold text-gray-700",children:["邮箱验证码 ",(0,t.jsx)("span",{className:"jsx-3e1eea4381e0e46b text-red-500",children:"*"})]}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b relative",children:[(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(u.A,{className:"h-5 w-5 text-gray-400"})}),(0,t.jsx)(d.p,{id:"verificationCode",type:"text",value:L,onChange:e=>I(e.target.value),placeholder:"请输入6位验证码",className:"pl-10 h-12 text-lg border-2 transition-all duration-300 border-gray-200 focus:border-green-400 focus:ring-green-100",disabled:C,maxLength:6})]}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b flex items-center justify-between",children:[(0,t.jsx)(i.$,{type:"button",variant:"ghost",onClick:()=>{W(!1),I(""),B.reset(),T("")},className:"text-gray-600 hover:text-gray-800",disabled:C,children:"← 返回修改信息"}),(0,t.jsx)(i.$,{type:"button",variant:"outline",onClick:B.resendVerificationCode,disabled:!B.canResend||B.isSending,className:"text-sm",children:B.isSending?(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b flex items-center gap-2",children:[(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b w-4 h-4 border-2 border-green-500 border-t-transparent rounded-full animate-spin"}),"发送中..."]}):B.getCountdownText()})]})]}),!O&&(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b space-y-4",children:[(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b flex items-start space-x-3",children:[(0,t.jsx)(c.S,{id:"agreeToTerms",checked:a.agreeToTerms,onCheckedChange:e=>K("agreeToTerms",e),disabled:C,className:"data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500 mt-1"}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b flex-1",children:[(0,t.jsxs)("label",{htmlFor:"agreeToTerms",className:"jsx-3e1eea4381e0e46b text-sm text-gray-700 cursor-pointer select-none leading-relaxed",children:["我已阅读并同意"," ",(0,t.jsx)("button",{type:"button",onClick:()=>alert("服务条款页面"),className:"jsx-3e1eea4381e0e46b text-green-600 hover:text-green-800 hover:underline font-semibold",children:"服务条款"})," ","和"," ",(0,t.jsx)("button",{type:"button",onClick:()=>alert("隐私政策页面"),className:"jsx-3e1eea4381e0e46b text-green-600 hover:text-green-800 hover:underline font-semibold",children:"隐私政策"})," ",(0,t.jsx)("span",{className:"jsx-3e1eea4381e0e46b text-red-500",children:"*"})]}),S.agreeToTerms&&(0,t.jsxs)("p",{className:"jsx-3e1eea4381e0e46b text-red-500 text-sm flex items-center gap-2 mt-2 animate-fade-in",children:[(0,t.jsx)(b.A,{className:"w-4 h-4"}),S.agreeToTerms]})]})]}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b flex items-center space-x-3",children:[(0,t.jsx)(c.S,{id:"subscribeNewsletter",checked:a.subscribeNewsletter,onCheckedChange:e=>K("subscribeNewsletter",e),disabled:C,className:"data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"}),(0,t.jsx)("label",{htmlFor:"subscribeNewsletter",className:"jsx-3e1eea4381e0e46b text-sm text-gray-700 cursor-pointer select-none",children:"订阅产品更新和优惠信息"})]})]}),(0,t.jsxs)(i.$,{type:"submit",disabled:C||O&&!L.trim(),className:"w-full h-12 text-lg font-bold bg-gradient-to-r from-green-500 via-blue-500 to-purple-500 hover:from-green-600 hover:via-blue-600 hover:to-purple-600 disabled:from-gray-400 disabled:to-gray-500 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:scale-100 relative overflow-hidden group",children:[(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b relative z-10 flex items-center justify-center gap-3",children:C?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),O?"验证中...":"发送验证码中..."]}):(0,t.jsx)(t.Fragment,{children:O?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(m.A,{className:"w-5 h-5"}),"完成注册"]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"w-5 h-5"}),"发送验证码"]})})})]}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b p-4 bg-blue-50 border border-blue-200 rounded-lg flex items-start gap-3",children:[(0,t.jsx)(y.A,{className:"w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5"}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b text-sm text-blue-800",children:[(0,t.jsx)("p",{className:"jsx-3e1eea4381e0e46b font-semibold mb-1",children:"安全提示"}),(0,t.jsx)("p",{className:"jsx-3e1eea4381e0e46b",children:"您的个人信息将被安全加密存储，我们承诺不会向第三方泄露您的隐私信息。"})]})]})]}),(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b mt-8 text-center",children:(0,t.jsxs)("p",{className:"jsx-3e1eea4381e0e46b text-gray-600",children:["已有账户？"," ",(0,t.jsx)("button",{onClick:()=>window.location.href="/login",className:"jsx-3e1eea4381e0e46b text-green-600 hover:text-green-800 font-semibold hover:underline transition-colors duration-200",children:"立即登录"})]})})]})]}),(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b mt-6 text-center",children:(0,t.jsx)("button",{onClick:()=>window.location.href="/",className:"jsx-3e1eea4381e0e46b text-gray-600 hover:text-gray-800 text-sm hover:underline transition-colors duration-200",children:"← 返回首页"})})]}),(0,t.jsx)(n(),{id:"3e1eea4381e0e46b",children:"@-webkit-keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-webkit-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-moz-keyframes float{0%,100%{-moz-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-moz-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-o-keyframes float{0%,100%{-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-o-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);-moz-transform:translatey(0px)rotate(0deg);-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-webkit-transform:translatey(-20px)rotate(180deg);-moz-transform:translatey(-20px)rotate(180deg);-o-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-webkit-keyframes fade-in{from{opacity:0;-webkit-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-webkit-transform:translatey(0);transform:translatey(0)}}@-moz-keyframes fade-in{from{opacity:0;-moz-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-moz-transform:translatey(0);transform:translatey(0)}}@-o-keyframes fade-in{from{opacity:0;-o-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-o-transform:translatey(0);transform:translatey(0)}}@keyframes fade-in{from{opacity:0;-webkit-transform:translatey(10px);-moz-transform:translatey(10px);-o-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-webkit-transform:translatey(0);-moz-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0)}}.animate-float.jsx-3e1eea4381e0e46b{-webkit-animation:float 8s ease-in-out infinite;-moz-animation:float 8s ease-in-out infinite;-o-animation:float 8s ease-in-out infinite;animation:float 8s ease-in-out infinite}.animate-fade-in.jsx-3e1eea4381e0e46b{-webkit-animation:fade-in.3s ease-out forwards;-moz-animation:fade-in.3s ease-out forwards;-o-animation:fade-in.3s ease-out forwards;animation:fade-in.3s ease-out forwards}.animate-float.jsx-3e1eea4381e0e46b{will-change:transform;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.animate-fade-in.jsx-3e1eea4381e0e46b{will-change:transform,opacity;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}"})]})}},4828:(e,a,s)=>{Promise.resolve().then(s.bind(s,3279))},5139:(e,a,s)=>{"use strict";s.d(a,{S:()=>o});var t=s(5155),r=s(2115),n=s(6981),l=s(5196),i=s(3999);let o=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(n.bL,{ref:a,className:(0,i.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",s),...r,children:(0,t.jsx)(n.C1,{className:(0,i.cn)("flex items-center justify-center text-current"),children:(0,t.jsx)(l.A,{className:"h-4 w-4"})})})});o.displayName=n.bL.displayName},9852:(e,a,s)=>{"use strict";s.d(a,{p:()=>l});var t=s(5155),r=s(2115),n=s(3999);let l=r.forwardRef((e,a)=>{let{className:s,type:r,...l}=e;return(0,t.jsx)("input",{type:r,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:a,...l})});l.displayName="Input"}},e=>{var a=a=>e(e.s=a);e.O(0,[352,550,964,576,441,684,358],()=>a(4828)),_N_E=e.O()}]);