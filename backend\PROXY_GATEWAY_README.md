# 高可用API代理网关集成指南

## 🎯 概述

本项目已成功集成了基于sing-box的高可用API代理网关系统，采用高内聚低耦合的架构设计，提供智能的网络请求路由和故障转移能力。

## 🏗️ 架构特点

### 高内聚设计
- **独立模块**: 代理网关功能完全封装在 `src/gateway/` 目录中
- **单一职责**: 每个组件只负责特定功能
- **内部协调**: 模块内部组件紧密协作

### 低耦合集成
- **接口驱动**: 通过标准接口与现有系统集成
- **配置驱动**: 通过环境变量控制功能开关
- **向后兼容**: 完全兼容现有TTS功能
- **渐进迁移**: 支持逐步启用新功能

## 📁 目录结构

```
backend/src/
├── gateway/                    # 代理网关独立模块
│   ├── core/                  # 核心功能（高内聚）
│   │   ├── SingboxController.js    # sing-box API控制
│   │   └── ProxyGateway.js         # 代理网关主控制器
│   ├── adapters/              # 适配器层（低耦合）
│   │   ├── NetworkAdapter.js       # 网络请求适配器
│   │   ├── ConfigAdapter.js        # 配置适配器
│   │   └── LoggerAdapter.js        # 日志适配器
│   ├── interfaces/            # 接口定义
│   │   ├── INetworkClient.js       # 网络客户端接口
│   │   ├── IProxyProvider.js       # 代理提供者接口
│   │   └── IHealthChecker.js       # 健康检查接口
│   └── index.js               # 统一导出
├── utils/
│   ├── networkManager.js      # 网络管理器（统一入口）
│   └── ttsUtils.js            # 扩展了网关集成函数
└── ...
```

## 🚀 快速开始

### 1. 安装依赖

```bash
cd backend
npm install
```

新增依赖：
- `socks-proxy-agent`: 用于SOCKS代理连接

### 2. 配置环境变量

在 `.env` 文件中添加以下配置：

```bash
# ========== 网络模式配置 ==========
# 网络模式: direct, proxy, gateway, fallback
NETWORK_MODE=direct

# ========== sing-box代理网关配置 ==========
# 启用sing-box代理网关
ENABLE_SINGBOX_GATEWAY=false

# sing-box控制API配置
SINGBOX_API_ENDPOINT=http://127.0.0.1:9090
SINGBOX_SELECTOR_NAME=proxy-selector

# 本地代理端口配置
SINGBOX_PROXY_HOST=127.0.0.1
SINGBOX_PROXY_PORT=1080

# 节点健康管理配置
SINGBOX_HEALTH_CHECK_INTERVAL=30000
SINGBOX_NODE_TIMEOUT=15000
SINGBOX_MAX_RETRIES=3

# 调试和监控配置
SINGBOX_DEBUG=false
SINGBOX_ENABLE_STATS=true

# 降级和容错配置
SINGBOX_FALLBACK_ENABLED=true
```

### 3. 测试功能

运行测试脚本验证集成：

```bash
node test-proxy-gateway.js
```

## 🔧 网络模式说明

### 1. Direct模式 (默认)
- 直接连接到目标API
- 不使用任何代理
- 适用于网络无限制的环境

```bash
NETWORK_MODE=direct
```

### 2. Proxy模式
- 使用现有的代理服务器
- 兼容原有的多代理配置
- 支持智能重试和故障转移

```bash
NETWORK_MODE=proxy
ENABLE_TTS_PROXY=true
TTS_PROXY_URLS="https://proxy1.example.com,https://proxy2.example.com"
```

### 3. Gateway模式 (新增)
- 使用sing-box代理网关
- 支持动态节点切换
- 提供最高的可用性

```bash
NETWORK_MODE=gateway
ENABLE_SINGBOX_GATEWAY=true
SINGBOX_API_ENDPOINT=http://127.0.0.1:9090
```

### 4. Fallback模式
- 先尝试直连，失败后使用代理
- 自动降级机制
- 平衡性能和可用性

```bash
NETWORK_MODE=fallback
ENABLE_SINGBOX_GATEWAY=true
SINGBOX_FALLBACK_ENABLED=true
```

## 🔌 API使用

### 使用新的智能生成函数

```javascript
const { generateSpeechSmart } = require('./src/utils/ttsUtils');

// 自动根据配置选择最佳网络模式
const audioBuffer = await generateSpeechSmart(
  '测试文本',
  'voiceId',
  'eleven_turbo_v2',
  0.5,
  0.75,
  0.5,
  1.0
);
```

### 直接使用网络管理器

```javascript
const { networkManager } = require('./src/utils/networkManager');

// 发起网络请求
const response = await networkManager.request({
  url: 'https://api.example.com/data',
  method: 'GET',
  headers: { 'Authorization': 'Bearer token' }
});
```

### 使用代理网关

```javascript
const { createProxyGateway } = require('./src/gateway');

const gateway = createProxyGateway();
await gateway.initialize();

const networkClient = gateway.getNetworkClient();
const response = await networkClient.request(options);
```

## 📊 监控和统计

### 获取统计信息

```javascript
const { networkManager } = require('./src/utils/networkManager');

const stats = await networkManager.getStats();
console.log('网络统计:', stats);
```

### 健康检查

```javascript
const isHealthy = await networkManager.healthCheck();
console.log('网络健康状态:', isHealthy);
```

## 🛠️ sing-box配置

### 基本配置示例（使用Clash API）

```json
{
  "log": {
    "level": "info"
  },
  "experimental": {
    "clash_api": {
      "external_controller": "127.0.0.1:9090"
    }
  },
  "inbounds": [
    {
      "type": "socks",
      "listen": "127.0.0.1",
      "listen_port": 1080
    }
  ],
  "outbounds": [
    {
      "type": "selector",
      "tag": "proxy-selector",
      "outbounds": ["node-1", "node-2", "node-3"]
    },
    {
      "type": "vless",
      "tag": "node-1",
      "server": "example1.com",
      "server_port": 443
    }
  ]
}
```

## 🔄 迁移指南

### 从现有系统迁移

1. **保持现有配置不变**
   ```bash
   NETWORK_MODE=direct  # 或 proxy
   ```

2. **逐步启用网关功能**
   ```bash
   NETWORK_MODE=fallback
   ENABLE_SINGBOX_GATEWAY=true
   ```

3. **完全切换到网关模式**
   ```bash
   NETWORK_MODE=gateway
   ```

### 代码迁移

现有代码无需修改，新功能通过以下方式启用：

```javascript
// 原有代码（继续工作）
const audioBuffer = await generateSpeech(text, voiceId, ...);

// 新代码（自动选择最佳模式）
const audioBuffer = await generateSpeechSmart(text, voiceId, ...);
```

## 🚨 故障排除

### 常见问题

1. **sing-box连接失败**
   - 检查sing-box是否运行
   - 验证API端点配置
   - 确认端口未被占用

2. **代理切换不生效**
   - 检查节点配置
   - 验证健康检查设置
   - 查看日志输出

3. **性能问题**
   - 调整超时设置
   - 优化健康检查间隔
   - 检查网络延迟

### 调试模式

启用详细日志：

```bash
SINGBOX_DEBUG=true
DEBUG=true
```

## 📈 性能优化

### 推荐配置

```bash
# 生产环境推荐配置
SINGBOX_HEALTH_CHECK_INTERVAL=30000
SINGBOX_NODE_TIMEOUT=15000
SINGBOX_MAX_RETRIES=3
SINGBOX_FALLBACK_ENABLED=true
```

### 监控指标

- 请求成功率
- 平均响应时间
- 节点切换频率
- 健康检查状态

## 🔒 安全考虑

1. **API访问控制**: sing-box API默认只监听本地
2. **认证机制**: 支持代理认证密钥
3. **日志安全**: 敏感信息自动脱敏
4. **降级保护**: 自动回退到安全模式

## 📚 更多资源

- [sing-box官方文档](https://sing-box.sagernet.org/)
- [代理网关设计文档](./代理网关系统方案.md)
- [API参考文档](./API_REFERENCE.md)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进代理网关功能。
