# TTS路径追踪日志使用说明

## 🎯 功能概述

TTS路径追踪日志功能提供了完整的TTS音频生成路径追踪，帮助开发者和运维人员准确了解每个TTS请求的生成方式、网络路径和性能指标。

## 🔧 环境变量配置

### 基础配置

```bash
# 是否启用TTS路径追踪日志 (true/false)
ENABLE_TTS_ROUTE_LOGGING=true

# TTS路径日志级别 (basic/detailed/verbose)
TTS_ROUTE_LOG_LEVEL=detailed

# 是否启用网关节点追踪日志 (true/false)
ENABLE_GATEWAY_NODE_LOGGING=true

# 是否启用网络请求详细日志 (true/false)
ENABLE_NETWORK_REQUEST_LOGGING=true
```

### 日志级别说明

- **basic**: 只记录关键路径信息（路由决策、成功/失败）
- **detailed**: 记录详细路径信息（包含代理、节点、耗时等）
- **verbose**: 记录所有调试信息（包含请求参数、响应详情等）

## 📊 日志标识符

系统使用统一的日志标识符来区分不同类型的日志：

| 标识符 | 说明 | 示例 |
|--------|------|------|
| `[TTS-ROUTE]` | 路由决策日志 | 选择网关模式或传统模式 |
| `[TTS-GATEWAY]` | 网关模式日志 | sing-box网关相关操作 |
| `[TTS-PROXY]` | 代理模式日志 | 传统代理服务器操作 |
| `[TTS-DIRECT]` | 直连模式日志 | 直接连接ElevenLabs API |
| `[TTS-NODE]` | 节点管理日志 | 节点切换、健康检查等 |
| `[TTS-SUCCESS]` | 成功日志 | 音频生成成功信息 |
| `[TTS-FALLBACK]` | 降级日志 | 模式切换和降级操作 |
| `[TTS-ERROR]` | 错误日志 | 各种错误和异常情况 |
| `[TTS-NETWORK]` | 网络请求日志 | HTTP请求详细信息 |

## 📝 日志示例

### 网关模式成功示例

```
[TTS-ROUTE] Route decision: gateway {"decision":"gateway","networkMode":"gateway","gatewayEnabled":true,"textLength":50}
[TTS-GATEWAY] Starting gateway request {"textLength":50,"voiceId":"9lHjugDhwqoxA5MhX0az","modelId":"eleven_turbo_v2"}
[TTS-GATEWAY] Making request via gateway {"socksProxy":"127.0.0.1:1080","currentNode":"SG-01-yless","method":"POST","hasAgent":true}
[TTS-NETWORK] POST https://api.elevenlabs.io/v1/text-to-speech/9lHjugDhwqoxA5MhX0az {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/9lHjugDhwqoxA5MhX0az","status":200,"duration":"3956ms","responseOk":true}
[TTS-GATEWAY] Gateway response received {"status":200,"ok":true,"currentNode":"SG-01-yless"}
[TTS-GATEWAY] Request completed successfully {"audioSize":186454,"totalDuration":"4200ms","requestDuration":"3956ms"}
[TTS-SUCCESS] Generated via gateway {"mode":"gateway","audioSize":186454,"duration":"4200ms"}
```

### 代理模式降级示例

```
[TTS-ROUTE] Route decision: traditional {"decision":"traditional","networkMode":"proxy"}
[TTS-PROXY] Using fallback mode {"strategy":"direct-first-then-proxy"}
[TTS-DIRECT] Attempting direct connection first
[TTS-NETWORK] POST https://api.elevenlabs.io/v1/text-to-speech/9lHjugDhwqoxA5MhX0az {"method":"POST","status":403,"duration":"1200ms","responseOk":false}
[TTS-FALLBACK] Fallback from direct to proxy {"from":"direct","to":"proxy","reason":"ElevenLabs API error: 403 Forbidden"}
[TTS-PROXY] Starting smart proxy retry {"maxRetries":3,"totalProxies":3,"selectionStrategy":"random"}
[TTS-PROXY] Attempting proxy request {"attempt":1,"maxRetries":3,"proxyUrl":"https://***@proxy1.example.com","strategy":"random"}
[TTS-PROXY] Proxy request successful {"attempt":1,"audioSize":186454,"requestDuration":"2800ms","totalDuration":"4000ms"}
[TTS-SUCCESS] Generated via proxy-fallback {"mode":"proxy-fallback","audioSize":186454,"duration":"4000ms","originalError":"ElevenLabs API error: 403 Forbidden"}
```

### 节点切换示例

```
[TTS-NODE] Attempting node switch {"targetNode":"SG-02-yless","currentNode":"SG-01-yless","selector":"proxy-selector","apiType":"clash"}
[TTS-NODE] Node switch successful {"oldNode":"SG-01-yless","newNode":"SG-02-yless","switchCount":5,"totalNodes":3,"healthyNodes":2}
```

## 🔍 日志分析指南

### 1. 路径追踪

通过 `[TTS-ROUTE]` 日志可以看到系统的路由决策：
- `gateway`: 使用sing-box网关模式
- `traditional`: 使用传统代理/直连模式

### 2. 性能分析

关注以下性能指标：
- `requestDuration`: 单个网络请求耗时
- `totalDuration`: 整个TTS生成耗时
- `audioSize`: 生成的音频文件大小

### 3. 故障排查

#### 网关模式问题
- 查看 `[TTS-GATEWAY]` 和 `[TTS-NODE]` 日志
- 检查SOCKS代理连接状态
- 确认当前使用的节点

#### 代理模式问题
- 查看 `[TTS-PROXY]` 日志
- 检查代理URL和重试次数
- 确认代理选择策略

#### 网络问题
- 查看 `[TTS-NETWORK]` 日志
- 检查HTTP状态码和响应时间
- 分析错误模式

## 🛠️ 故障排查流程

### 1. 确认日志配置

```bash
# 检查环境变量
echo $ENABLE_TTS_ROUTE_LOGGING
echo $TTS_ROUTE_LOG_LEVEL
```

### 2. 查看日志输出

```bash
# 实时查看日志
tail -f /var/log/tts-app/$(date +%Y-%m-%d).log | grep "TTS-"

# 过滤特定类型的日志
grep "TTS-ROUTE" /var/log/tts-app/$(date +%Y-%m-%d).log
grep "TTS-ERROR" /var/log/tts-app/$(date +%Y-%m-%d).log
```

### 3. 分析问题模式

```bash
# 统计不同路径的使用情况
grep "TTS-SUCCESS" /var/log/tts-app/$(date +%Y-%m-%d).log | grep -o '"mode":"[^"]*"' | sort | uniq -c

# 查看错误分布
grep "TTS-ERROR" /var/log/tts-app/$(date +%Y-%m-%d).log | grep -o '"mode":"[^"]*"' | sort | uniq -c

# 分析性能问题
grep "TTS-SUCCESS" /var/log/tts-app/$(date +%Y-%m-%d).log | grep -o '"duration":"[^"]*"' | sort -n
```

## 🔧 测试和验证

### 运行测试脚本

```bash
cd backend
node test-tts-logging.js
```

### 手动测试

1. **启用详细日志**
   ```bash
   export ENABLE_TTS_ROUTE_LOGGING=true
   export TTS_ROUTE_LOG_LEVEL=detailed
   ```

2. **发起TTS请求**
   通过前端或API直接发起TTS请求

3. **查看日志输出**
   检查控制台或日志文件中的TTS路径日志

## 📈 性能影响

### 日志开销

- **basic级别**: 几乎无性能影响（< 1ms）
- **detailed级别**: 轻微性能影响（< 5ms）
- **verbose级别**: 可感知性能影响（< 10ms）

### 生产环境建议

- 生产环境推荐使用 `basic` 或 `detailed` 级别
- 调试时可临时启用 `verbose` 级别
- 可通过环境变量动态调整，无需重启服务

## 🔒 安全考虑

### 敏感信息保护

系统自动过滤以下敏感信息：
- API密钥和令牌（显示为 `***`）
- 代理认证信息（URL中的用户名密码）
- 过长的请求体（截断并添加 `...`）

### 日志访问控制

- 确保日志文件有适当的访问权限
- 定期清理旧日志文件
- 考虑使用日志轮转机制

## 🚀 最佳实践

1. **开发环境**: 启用 `verbose` 级别，便于调试
2. **测试环境**: 使用 `detailed` 级别，平衡信息量和性能
3. **生产环境**: 使用 `basic` 级别，关注关键路径
4. **监控告警**: 基于 `[TTS-ERROR]` 日志设置告警
5. **性能分析**: 定期分析 `duration` 指标，优化性能

## 📞 支持和反馈

如果在使用过程中遇到问题或有改进建议，请：
1. 检查本文档的故障排查部分
2. 运行测试脚本验证功能
3. 收集相关日志信息
4. 联系开发团队获取支持
