#!/usr/bin/env node

/**
 * TTS路径追踪日志功能测试脚本
 * 验证新增的日志功能是否正常工作
 */

require('dotenv').config();

// 颜色输出函数
function log(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color] || colors.white}${message}${colors.reset}`);
}

/**
 * 测试TTS日志器配置
 */
function testLoggerConfig() {
  log('blue', '\n🔧 测试TTS日志器配置...');
  
  try {
    const { ttsLogger } = require('./src/utils/ttsLogger');
    
    console.log('   日志器配置:');
    console.log(`   - 启用状态: ${ttsLogger.enabled}`);
    console.log(`   - 日志级别: ${ttsLogger.logLevel}`);
    console.log(`   - 网关节点日志: ${ttsLogger.gatewayNodeLogging}`);
    console.log(`   - 网络请求日志: ${ttsLogger.networkRequestLogging}`);
    
    // 测试日志标识符
    console.log('   - 日志标识符:', Object.keys(ttsLogger.tags).join(', '));
    
    log('green', '✅ TTS日志器配置测试通过');
    return true;
    
  } catch (error) {
    log('red', `❌ TTS日志器配置测试失败: ${error.message}`);
    return false;
  }
}

/**
 * 测试日志输出功能
 */
function testLogOutput() {
  log('blue', '\n📝 测试日志输出功能...');
  
  try {
    const { ttsLogger } = require('./src/utils/ttsLogger');
    
    // 创建测试上下文
    const testContext = {
      logContext: {
        taskId: 'test-task-123',
        username: 'test-user'
      }
    };
    
    const contextLogger = ttsLogger.createContextLogger(testContext);
    
    // 测试各种日志类型
    console.log('   测试各种日志类型输出:');
    
    contextLogger.logRoute('gateway', { NETWORK_MODE: 'gateway', ENABLE_SINGBOX_GATEWAY: true });
    contextLogger.logGateway('Test gateway action', { testData: 'example' });
    contextLogger.logProxy('Test proxy action', { proxyUrl: 'https://example.com' });
    contextLogger.logDirect('Test direct action', { directConnection: true });
    contextLogger.logNode('Test node action', { nodeId: 'test-node-1' });
    contextLogger.logNetwork('POST', 'https://api.example.com/test', 200, 1500);
    contextLogger.logSuccess('gateway', { audioSize: 12345, duration: '2500ms' });
    contextLogger.logFallback('gateway', 'traditional', 'Test fallback reason');
    
    log('green', '✅ 日志输出功能测试通过');
    return true;
    
  } catch (error) {
    log('red', `❌ 日志输出功能测试失败: ${error.message}`);
    return false;
  }
}

/**
 * 测试环境变量控制
 */
function testEnvControl() {
  log('blue', '\n🎛️  测试环境变量控制...');
  
  try {
    // 保存原始环境变量
    const originalEnabled = process.env.ENABLE_TTS_ROUTE_LOGGING;
    const originalLevel = process.env.TTS_ROUTE_LOG_LEVEL;
    
    // 测试禁用日志
    process.env.ENABLE_TTS_ROUTE_LOGGING = 'false';
    delete require.cache[require.resolve('./src/utils/ttsLogger')];
    let { ttsLogger } = require('./src/utils/ttsLogger');
    
    if (!ttsLogger.enabled) {
      console.log('   ✓ 环境变量禁用日志功能正常');
    } else {
      throw new Error('环境变量禁用日志功能失败');
    }
    
    // 测试不同日志级别
    process.env.ENABLE_TTS_ROUTE_LOGGING = 'true';
    process.env.TTS_ROUTE_LOG_LEVEL = 'basic';
    delete require.cache[require.resolve('./src/utils/ttsLogger')];
    ({ ttsLogger } = require('./src/utils/ttsLogger'));
    
    if (ttsLogger.logLevel === 'basic') {
      console.log('   ✓ 日志级别设置正常');
    } else {
      throw new Error('日志级别设置失败');
    }
    
    // 恢复原始环境变量
    if (originalEnabled) process.env.ENABLE_TTS_ROUTE_LOGGING = originalEnabled;
    if (originalLevel) process.env.TTS_ROUTE_LOG_LEVEL = originalLevel;
    
    log('green', '✅ 环境变量控制测试通过');
    return true;
    
  } catch (error) {
    log('red', `❌ 环境变量控制测试失败: ${error.message}`);
    return false;
  }
}

/**
 * 测试敏感信息过滤
 */
function testSensitiveDataFiltering() {
  log('blue', '\n🔒 测试敏感信息过滤...');
  
  try {
    const { ttsLogger } = require('./src/utils/ttsLogger');
    
    // 测试敏感数据过滤
    const sensitiveData = {
      secret: 'super-secret-key',
      token: 'bearer-token-123',
      apiKey: 'api-key-456',
      body: 'a'.repeat(300), // 长文本
      normalData: 'normal-value'
    };
    
    const filtered = ttsLogger.filterSensitiveData(sensitiveData);
    
    if (filtered.secret === '***' && 
        filtered.token === '***' && 
        filtered.apiKey === '***' &&
        filtered.body.endsWith('...') &&
        filtered.normalData === 'normal-value') {
      console.log('   ✓ 敏感信息过滤正常');
    } else {
      throw new Error('敏感信息过滤失败');
    }
    
    // 测试URL清理
    const testUrl = 'https://api.example.com/v1/test?secret=123&token=abc';
    const cleanUrl = ttsLogger.sanitizeUrl(testUrl);
    
    if (cleanUrl === 'https://api.example.com/v1/test') {
      console.log('   ✓ URL清理功能正常');
    } else {
      throw new Error('URL清理功能失败');
    }
    
    log('green', '✅ 敏感信息过滤测试通过');
    return true;
    
  } catch (error) {
    log('red', `❌ 敏感信息过滤测试失败: ${error.message}`);
    return false;
  }
}

/**
 * 测试TTS函数集成
 */
async function testTTSIntegration() {
  log('blue', '\n🎵 测试TTS函数集成...');
  
  try {
    // 注意：这里只测试函数是否能正确加载和调用日志功能
    // 不进行实际的API调用以避免网络依赖
    
    const { generateSpeechSmart } = require('./src/utils/ttsUtils');
    
    if (typeof generateSpeechSmart === 'function') {
      console.log('   ✓ generateSpeechSmart函数加载正常');
    } else {
      throw new Error('generateSpeechSmart函数加载失败');
    }
    
    // 检查其他相关函数
    const { generateSpeechWithGateway, callTtsProxyWithSmartRetry, callDirectElevenLabs } = require('./src/utils/ttsUtils');
    
    if (typeof generateSpeechWithGateway === 'function' &&
        typeof callTtsProxyWithSmartRetry === 'function' &&
        typeof callDirectElevenLabs === 'function') {
      console.log('   ✓ 所有TTS相关函数加载正常');
    } else {
      throw new Error('TTS相关函数加载失败');
    }
    
    log('green', '✅ TTS函数集成测试通过');
    return true;
    
  } catch (error) {
    log('red', `❌ TTS函数集成测试失败: ${error.message}`);
    return false;
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  log('cyan', '🚀 开始TTS路径追踪日志功能测试...\n');
  
  const tests = [
    { name: 'TTS日志器配置', fn: testLoggerConfig },
    { name: '日志输出功能', fn: testLogOutput },
    { name: '环境变量控制', fn: testEnvControl },
    { name: '敏感信息过滤', fn: testSensitiveDataFiltering },
    { name: 'TTS函数集成', fn: testTTSIntegration }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      log('red', `❌ 测试 "${test.name}" 异常: ${error.message}`);
      failed++;
    }
  }
  
  // 输出测试结果
  log('cyan', '\n📊 测试结果汇总:');
  log('green', `✅ 通过: ${passed} 个测试`);
  if (failed > 0) {
    log('red', `❌ 失败: ${failed} 个测试`);
  }
  
  if (failed === 0) {
    log('green', '\n🎉 所有测试通过！TTS路径追踪日志功能已成功实施。');
    console.log('\n📝 使用说明:');
    console.log('   1. 通过环境变量 ENABLE_TTS_ROUTE_LOGGING 控制日志开关');
    console.log('   2. 通过环境变量 TTS_ROUTE_LOG_LEVEL 控制日志详细程度');
    console.log('   3. 查看后端日志文件或控制台输出来追踪TTS生成路径');
  } else {
    log('red', '\n❌ 部分测试失败，请检查实施过程。');
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    log('red', `\n💥 测试运行异常: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  });
}

module.exports = {
  runTests,
  testLoggerConfig,
  testLogOutput,
  testEnvControl,
  testSensitiveDataFiltering,
  testTTSIntegration
};
