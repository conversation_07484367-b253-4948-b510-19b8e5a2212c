#!/usr/bin/env node

/**
 * 代理网关功能测试脚本
 * 用于验证sing-box代理网关集成是否正常工作
 */

require('dotenv').config();

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * 测试配置加载
 */
async function testConfigLoading() {
  log('blue', '\n🔧 测试配置加载...');
  
  try {
    const { configAdapter } = require('./src/gateway/adapters/ConfigAdapter');
    const config = configAdapter.getConfig();
    
    log('green', '✅ 配置加载成功');
    console.log('   网络模式:', config.NETWORK_MODE);
    console.log('   sing-box启用:', config.ENABLE_SINGBOX_GATEWAY);
    console.log('   代理端点:', config.SINGBOX_API_ENDPOINT);
    console.log('   代理端口:', `${config.SINGBOX_PROXY_HOST}:${config.SINGBOX_PROXY_PORT}`);
    
    return true;
  } catch (error) {
    log('red', '❌ 配置加载失败:');
    console.error('   ', error.message);
    return false;
  }
}

/**
 * 测试网络管理器初始化
 */
async function testNetworkManager() {
  log('blue', '\n🌐 测试网络管理器初始化...');
  
  try {
    const { networkManager } = require('./src/utils/networkManager');
    
    // 初始化网络管理器
    await networkManager.initialize();
    
    const status = networkManager.getStatus();
    log('green', '✅ 网络管理器初始化成功');
    console.log('   初始化状态:', status.initialized);
    console.log('   当前模式:', status.mode);
    
    return networkManager;
  } catch (error) {
    log('red', '❌ 网络管理器初始化失败:');
    console.error('   ', error.message);
    return null;
  }
}

/**
 * 测试sing-box控制器（仅在启用时）
 */
async function testSingboxController() {
  log('blue', '\n📡 测试sing-box控制器...');
  
  try {
    const { configAdapter } = require('./src/gateway/adapters/ConfigAdapter');
    const config = configAdapter.getConfig();
    
    if (!config.ENABLE_SINGBOX_GATEWAY) {
      log('yellow', '⚠️  sing-box网关未启用，跳过测试');
      return true;
    }
    
    const { SingboxController } = require('./src/gateway/core/SingboxController');
    const controller = new SingboxController(config);

    // 尝试初始化
    await controller.initialize();

    const stats = await controller.getStats();
    log('green', '✅ sing-box控制器测试成功');
    console.log('   总节点数:', stats.totalNodes);
    console.log('   健康节点数:', stats.healthyNodes);
    console.log('   当前节点:', stats.currentNode);
    console.log('   API类型:', stats.apiType);
    
    // 清理
    await controller.cleanup();
    return true;
    
  } catch (error) {
    log('red', '❌ sing-box控制器测试失败:');
    console.error('   ', error.message);
    
    // 如果是连接错误，这是正常的（sing-box可能未运行）
    if (error.message.includes('ECONNREFUSED') || error.message.includes('fetch')) {
      log('yellow', '   💡 提示: 请确保sing-box服务正在运行并监听在配置的端口');
    }
    
    return false;
  }
}

/**
 * 测试网络请求功能
 */
async function testNetworkRequest(networkManager) {
  log('blue', '\n🌍 测试网络请求功能...');
  
  if (!networkManager) {
    log('yellow', '⚠️  网络管理器不可用，跳过测试');
    return false;
  }
  
  try {
    // 测试简单的HTTP请求
    const testUrl = 'https://httpbin.org/ip';
    log('cyan', `   正在请求: ${testUrl}`);
    
    const response = await networkManager.get(testUrl, { timeout: 10000 });
    
    if (response.ok) {
      const data = await response.json();
      log('green', '✅ 网络请求测试成功');
      console.log('   响应状态:', response.status);
      console.log('   客户端IP:', data.origin);
      
      return true;
    } else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
  } catch (error) {
    log('red', '❌ 网络请求测试失败:');
    console.error('   ', error.message);
    return false;
  }
}

/**
 * 测试健康检查功能
 */
async function testHealthCheck(networkManager) {
  log('blue', '\n💓 测试健康检查功能...');
  
  if (!networkManager) {
    log('yellow', '⚠️  网络管理器不可用，跳过测试');
    return false;
  }
  
  try {
    const isHealthy = await networkManager.healthCheck();
    
    if (isHealthy) {
      log('green', '✅ 健康检查通过');
    } else {
      log('yellow', '⚠️  健康检查失败，但这可能是正常的');
    }
    
    return true;
  } catch (error) {
    log('red', '❌ 健康检查测试失败:');
    console.error('   ', error.message);
    return false;
  }
}

/**
 * 测试TTS集成功能
 */
async function testTTSIntegration() {
  log('blue', '\n🎵 测试TTS集成功能...');
  
  try {
    const { generateSpeechSmart } = require('./src/utils/ttsUtils');
    
    // 测试参数
    const testText = '这是一个代理网关集成测试。';
    const voiceId = 'pNInz6obpgDQGcFmaJgB'; // Adam voice
    
    log('cyan', `   正在生成音频: "${testText}"`);
    
    const audioBuffer = await generateSpeechSmart(
      testText,
      voiceId,
      'eleven_turbo_v2',
      0.5,
      0.75,
      0.5,
      1.0
    );
    
    if (audioBuffer && audioBuffer.byteLength > 0) {
      log('green', '✅ TTS集成测试成功');
      console.log('   音频大小:', `${audioBuffer.byteLength} bytes`);
      console.log('   音频大小:', `${(audioBuffer.byteLength / 1024).toFixed(2)} KB`);
      
      return true;
    } else {
      throw new Error('生成的音频数据为空');
    }
    
  } catch (error) {
    log('red', '❌ TTS集成测试失败:');
    console.error('   ', error.message);
    
    // 如果是内容违规，这是正常的
    if (error.isContentViolation) {
      log('yellow', '   💡 提示: 检测到内容违规，这是正常的安全机制');
      return true;
    }
    
    return false;
  }
}

/**
 * 测试统计信息
 */
async function testStatistics(networkManager) {
  log('blue', '\n📊 测试统计信息...');
  
  if (!networkManager) {
    log('yellow', '⚠️  网络管理器不可用，跳过测试');
    return false;
  }
  
  try {
    const stats = await networkManager.getStats();
    
    log('green', '✅ 统计信息获取成功');
    console.log('   管理器统计:');
    console.log('     总请求数:', stats.manager.totalRequests);
    console.log('     成功请求数:', stats.manager.successfulRequests);
    console.log('     失败请求数:', stats.manager.failedRequests);
    
    if (stats.client) {
      console.log('   客户端统计:');
      console.log('     模式:', stats.client.mode);
      console.log('     请求数:', stats.client.requestCount);
      console.log('     成功率:', `${((stats.client.successCount / stats.client.requestCount) * 100).toFixed(2)}%`);
    }
    
    return true;
  } catch (error) {
    log('red', '❌ 统计信息测试失败:');
    console.error('   ', error.message);
    return false;
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  log('cyan', '🚀 开始代理网关功能测试...\n');
  
  const results = {
    config: false,
    networkManager: false,
    singboxController: false,
    networkRequest: false,
    healthCheck: false,
    ttsIntegration: false,
    statistics: false
  };
  
  // 执行测试
  results.config = await testConfigLoading();
  
  const networkManager = await testNetworkManager();
  results.networkManager = !!networkManager;
  
  results.singboxController = await testSingboxController();
  results.networkRequest = await testNetworkRequest(networkManager);
  results.healthCheck = await testHealthCheck(networkManager);
  results.ttsIntegration = await testTTSIntegration();
  results.statistics = await testStatistics(networkManager);
  
  // 输出测试结果
  log('cyan', '\n📋 测试结果汇总:');
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅ 通过' : '❌ 失败';
    const color = passed ? 'green' : 'red';
    log(color, `   ${test}: ${status}`);
  });
  
  const passedCount = Object.values(results).filter(Boolean).length;
  const totalCount = Object.keys(results).length;
  
  log('cyan', `\n🎯 总体结果: ${passedCount}/${totalCount} 项测试通过`);
  
  if (passedCount === totalCount) {
    log('green', '🎉 所有测试通过！代理网关功能正常。');
  } else if (passedCount >= totalCount * 0.7) {
    log('yellow', '⚠️  大部分测试通过，系统基本可用。');
  } else {
    log('red', '❌ 多项测试失败，请检查配置和环境。');
  }
  
  // 清理
  if (networkManager) {
    await networkManager.cleanup();
  }
}

// 运行测试
runTests().catch(error => {
  log('red', '💥 测试执行失败:');
  console.error(error);
  process.exit(1);
});
