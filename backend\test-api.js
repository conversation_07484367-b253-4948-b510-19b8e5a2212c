#!/usr/bin/env node

/**
 * TTS API 测试脚本
 * 用于测试本地开发环境的API端点
 */

const http = require('http');

// 测试配置
const BASE_URL = 'http://localhost:3000';
const TEST_ENDPOINTS = [
  { method: 'GET', path: '/health', description: '健康检查' },
  { method: 'GET', path: '/api', description: 'API信息' },
  { method: 'GET', path: '/api/auth/verify', description: '令牌验证 (应该返回401)' },
  { method: 'GET', path: '/api/tts/voices', description: '语音列表 (应该返回401)' },
  { method: 'GET', path: '/api/user/quota', description: '用户配额 (应该返回401)' }
];

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// HTTP请求函数
function makeRequest(method, path) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, BASE_URL);
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'TTS-API-Test/1.0'
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

// 测试单个端点
async function testEndpoint(endpoint) {
  try {
    log('blue', `\n🧪 测试: ${endpoint.description}`);
    console.log(`   ${endpoint.method} ${endpoint.path}`);
    
    const response = await makeRequest(endpoint.method, endpoint.path);
    
    // 判断测试结果
    let success = false;
    let message = '';
    
    if (endpoint.path === '/health' || endpoint.path === '/api') {
      success = response.statusCode === 200;
      message = success ? '✅ 成功' : `❌ 失败 (状态码: ${response.statusCode})`;
    } else {
      // 对于需要认证的端点，401是预期的
      success = response.statusCode === 401;
      message = success ? '✅ 正确返回401 (需要认证)' : `⚠️  意外状态码: ${response.statusCode}`;
    }
    
    log(success ? 'green' : 'red', `   ${message}`);
    
    // 显示响应体（如果不太长）
    if (response.body && response.body.length < 200) {
      try {
        const parsed = JSON.parse(response.body);
        console.log(`   响应: ${JSON.stringify(parsed, null, 2).substring(0, 100)}...`);
      } catch (e) {
        console.log(`   响应: ${response.body.substring(0, 100)}...`);
      }
    }
    
    return { success, statusCode: response.statusCode };
  } catch (error) {
    log('red', `   ❌ 错误: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// 主测试函数
async function runTests() {
  log('blue', '🚀 开始TTS API测试...\n');
  
  const results = [];
  
  for (const endpoint of TEST_ENDPOINTS) {
    const result = await testEndpoint(endpoint);
    results.push({ endpoint, result });
  }
  
  // 测试总结
  log('blue', '\n📊 测试总结:');
  const successful = results.filter(r => r.result.success).length;
  const total = results.length;
  
  log('blue', `   总计: ${total} 个测试`);
  log('green', `   成功: ${successful} 个`);
  log('red', `   失败: ${total - successful} 个`);
  
  if (successful === total) {
    log('green', '\n🎉 所有测试通过！基础API功能正常');
  } else {
    log('yellow', '\n⚠️  部分测试失败，请检查服务状态');
  }
  
  // 下一步建议
  log('blue', '\n📋 下一步建议:');
  console.log('   1. 安装PostgreSQL和Redis以启用完整功能');
  console.log('   2. 运行数据库初始化脚本: npm run migrate:create');
  console.log('   3. 测试用户注册和登录功能');
  console.log('   4. 测试TTS生成功能');
}

// 检查服务是否运行
async function checkService() {
  try {
    await makeRequest('GET', '/health');
    return true;
  } catch (error) {
    return false;
  }
}

// 启动测试
async function main() {
  log('blue', '🔍 检查TTS服务状态...');
  
  const isRunning = await checkService();
  if (!isRunning) {
    log('red', '❌ TTS服务未运行！');
    console.log('请先启动服务: npm run dev');
    process.exit(1);
  }
  
  log('green', '✅ TTS服务正在运行');
  await runTests();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { makeRequest, testEndpoint, runTests };
